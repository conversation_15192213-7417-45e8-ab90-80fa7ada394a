package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.util.List;

/**
 * 简单的NPK打包测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SimplePackTest {
    
    public static void main(String[] args) {
        System.out.println("=== 简单NPK打包测试 ===");
        
        // 先提取一些文件用于测试
        extractSampleFiles();
        
        // 然后测试打包
        testPacking();
    }
    
    /**
     * 提取示例文件
     */
    private static void extractSampleFiles() {
        System.out.println("步骤1: 提取示例文件...");
        
        try {
            File npkFile = new File("item.npk");
            if (!npkFile.exists()) {
                System.out.println("item.npk文件不存在，跳过提取");
                return;
            }
            
            // 创建测试目录
            File testDir = new File("pack_test");
            if (testDir.exists()) {
                deleteDirectory(testDir);
            }
            testDir.mkdirs();
            
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            List<NpkEntry> entries = parsedFile.getEntries();
            
            // 提取前5个文件
            int extractCount = Math.min(5, entries.size());
            int successCount = 0;
            
            for (int i = 0; i < extractCount; i++) {
                NpkEntry entry = entries.get(i);
                
                try {
                    byte[] data = NpkTool.extractFile(parsedFile, entry, null);
                    if (data != null && data.length > 0) {
                        String fileName = entry.getFileName();
                        File outputFile = new File(testDir, fileName);
                        
                        java.nio.file.Files.write(outputFile.toPath(), data);
                        successCount++;
                        
                        System.out.println("  提取: " + fileName + " (" + data.length + " 字节)");
                    }
                } catch (Exception e) {
                    System.out.println("  提取失败: " + entry.getFileName() + " - " + e.getMessage());
                }
            }
            
            System.out.println("成功提取 " + successCount + " 个文件到 pack_test 目录");
            
        } catch (Exception e) {
            System.out.println("提取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试打包
     */
    private static void testPacking() {
        System.out.println("\n步骤2: 测试NPK打包...");
        
        try {
            File testDir = new File("pack_test");
            if (!testDir.exists()) {
                System.out.println("测试目录不存在，无法进行打包测试");
                return;
            }
            
            // 列出要打包的文件
            File[] files = testDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".png"));
            if (files == null || files.length == 0) {
                System.out.println("测试目录中没有PNG文件");
                return;
            }
            
            System.out.println("找到 " + files.length + " 个PNG文件:");
            for (File file : files) {
                System.out.println("  - " + file.getName() + " (" + file.length() + " 字节)");
            }
            
            // 执行打包
            String outputNpk = "test_packed.npk";
            System.out.println("\n开始打包到: " + outputNpk);
            
            boolean success = NpkPacker.packFolder(
                "pack_test",
                outputNpk,
                1, // 使用Deflate压缩
                (progress, message) -> {
                    System.out.println("  [" + progress + "%] " + message);
                }
            );
            
            if (success) {
                File packedFile = new File(outputNpk);
                if (packedFile.exists()) {
                    System.out.println("✓ 打包成功!");
                    System.out.println("  输出文件: " + outputNpk);
                    System.out.println("  文件大小: " + formatFileSize(packedFile.length()));
                    
                    // 尝试解析打包的文件
                    testParsingPackedFile(outputNpk);
                } else {
                    System.out.println("✗ 打包失败: 输出文件不存在");
                }
            } else {
                System.out.println("✗ 打包失败");
            }
            
        } catch (Exception e) {
            System.out.println("打包测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试解析打包的文件
     */
    private static void testParsingPackedFile(String npkPath) {
        System.out.println("\n步骤3: 测试解析打包的NPK文件...");
        
        try {
            File npkFile = new File(npkPath);
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            List<NpkEntry> entries = parsedFile.getEntries();
            
            System.out.println("解析结果:");
            System.out.println("  条目数: " + entries.size());
            
            if (!entries.isEmpty()) {
                System.out.println("  文件列表:");
                for (int i = 0; i < Math.min(10, entries.size()); i++) {
                    NpkEntry entry = entries.get(i);
                    System.out.println("    " + (i+1) + ". " + entry.getFileName() + 
                                     " (偏移: 0x" + Long.toHexString(entry.getOffset()) + 
                                     ", 大小: " + entry.getCompressedSize() + " 字节)");
                }
                
                // 尝试提取第一个文件
                if (entries.size() > 0) {
                    System.out.println("\n尝试提取第一个文件...");
                    NpkEntry firstEntry = entries.get(0);
                    
                    try {
                        byte[] data = NpkTool.extractFile(parsedFile, firstEntry, null);
                        if (data != null && data.length > 0) {
                            System.out.println("✓ 提取成功: " + firstEntry.getFileName() + 
                                             " (" + data.length + " 字节)");
                            
                            // 检查是否是有效的PNG
                            if (isPngData(data)) {
                                System.out.println("✓ 文件是有效的PNG格式");
                            } else {
                                System.out.println("⚠ 文件不是有效的PNG格式");
                            }
                        } else {
                            System.out.println("✗ 提取失败: 数据为空");
                        }
                    } catch (Exception e) {
                        System.out.println("✗ 提取失败: " + e.getMessage());
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("解析打包文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查是否是PNG数据
     */
    private static boolean isPngData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }
        
        return data[0] == (byte)0x89 && 
               data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && 
               data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && 
               data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && 
               data[7] == (byte)0x0A;
    }
    
    /**
     * 删除目录
     */
    private static void deleteDirectory(File dir) {
        if (dir.exists()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            dir.delete();
        }
    }
    
    /**
     * 格式化文件大小
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
}
