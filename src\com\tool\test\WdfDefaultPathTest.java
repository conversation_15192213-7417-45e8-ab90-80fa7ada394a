package com.tool.test;

import java.io.File;
import java.io.IOException;

/**
 * WDF默认路径功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class WdfDefaultPathTest {
    
    private static final String DEFAULT_PATH = "G:\\JXy2o\\GameClient3.0\\res";
    
    /**
     * 测试默认路径功能
     */
    public void testDefaultPath() {
        System.out.println("=== 测试WDF默认路径功能 ===");
        
        System.out.println("默认路径: " + DEFAULT_PATH);
        
        // 测试路径存在性
        File defaultDir = new File(DEFAULT_PATH);
        if (defaultDir.exists() && defaultDir.isDirectory()) {
            System.out.println("✓ 默认路径存在");
            System.out.println("  路径: " + defaultDir.getAbsolutePath());
            System.out.println("  可读: " + defaultDir.canRead());
            System.out.println("  可写: " + defaultDir.canWrite());
        } else {
            System.out.println("✗ 默认路径不存在，尝试创建...");
            
            try {
                if (defaultDir.mkdirs()) {
                    System.out.println("✓ 默认路径创建成功");
                    System.out.println("  路径: " + defaultDir.getAbsolutePath());
                } else {
                    System.out.println("✗ 默认路径创建失败");
                }
            } catch (Exception e) {
                System.out.println("✗ 创建默认路径时发生异常: " + e.getMessage());
            }
        }
        
        // 测试文件创建
        testFileCreation(defaultDir);
    }
    
    /**
     * 测试在默认路径中创建文件
     */
    private void testFileCreation(File defaultDir) {
        System.out.println("\n--- 测试文件创建 ---");
        
        if (!defaultDir.exists() || !defaultDir.isDirectory()) {
            System.out.println("✗ 默认路径不可用，跳过文件创建测试");
            return;
        }
        
        String[] testFiles = {
            "test_item.wdf",
            "custom_resources.wdf", 
            "game_data.wdf"
        };
        
        for (String fileName : testFiles) {
            File testFile = new File(defaultDir, fileName);
            
            try {
                if (testFile.createNewFile()) {
                    System.out.println("✓ 创建测试文件: " + fileName);
                    
                    // 删除测试文件
                    if (testFile.delete()) {
                        System.out.println("  ✓ 删除测试文件: " + fileName);
                    } else {
                        System.out.println("  ✗ 删除测试文件失败: " + fileName);
                    }
                } else {
                    System.out.println("✗ 创建测试文件失败: " + fileName + " (文件可能已存在)");
                }
            } catch (IOException e) {
                System.out.println("✗ 创建测试文件异常: " + fileName + " - " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试WDF文件路径处理
     */
    public void testWdfPathHandling() {
        System.out.println("\n=== 测试WDF文件路径处理 ===");
        
        String[] testPaths = {
            DEFAULT_PATH + "\\item.wdf",
            DEFAULT_PATH + "\\custom_item",
            DEFAULT_PATH + "\\resources.WDF",
            DEFAULT_PATH + "\\game_data.wdf"
        };
        
        for (String testPath : testPaths) {
            System.out.println("\n测试路径: " + testPath);
            
            // 模拟WDF文件路径处理逻辑
            File targetFile = testPath.toLowerCase().endsWith(".wdf") ? 
                new File(testPath) : new File(testPath + ".wdf");
            
            System.out.println("处理后路径: " + targetFile.getAbsolutePath());
            System.out.println("文件名: " + targetFile.getName());
            System.out.println("父目录: " + targetFile.getParent());
            
            // 检查父目录是否存在
            File parentDir = targetFile.getParentFile();
            if (parentDir != null && parentDir.exists()) {
                System.out.println("✓ 父目录存在");
            } else {
                System.out.println("✗ 父目录不存在");
            }
        }
    }
    
    /**
     * 测试浏览器默认路径功能
     */
    public void testBrowserDefaultPath() {
        System.out.println("\n=== 测试浏览器默认路径功能 ===");
        
        File defaultDir = new File(DEFAULT_PATH);
        
        System.out.println("模拟文件浏览器行为:");
        System.out.println("默认目录: " + DEFAULT_PATH);
        
        if (defaultDir.exists() && defaultDir.isDirectory()) {
            System.out.println("✓ 默认目录存在，浏览器将打开此目录");
            
            // 列出目录中的WDF文件
            File[] wdfFiles = defaultDir.listFiles((dir, name) -> 
                name.toLowerCase().endsWith(".wdf"));
            
            if (wdfFiles != null && wdfFiles.length > 0) {
                System.out.println("目录中现有的WDF文件:");
                for (File wdfFile : wdfFiles) {
                    System.out.println("  - " + wdfFile.getName() + 
                                     " (" + formatFileSize(wdfFile.length()) + ")");
                }
            } else {
                System.out.println("目录中暂无WDF文件");
            }
        } else {
            System.out.println("✗ 默认目录不存在");
            System.out.println("尝试创建默认目录...");
            
            try {
                if (defaultDir.mkdirs()) {
                    System.out.println("✓ 默认目录创建成功，浏览器将打开此目录");
                } else {
                    System.out.println("✗ 默认目录创建失败，浏览器将使用当前目录");
                }
            } catch (Exception e) {
                System.out.println("✗ 创建默认目录异常: " + e.getMessage());
                System.out.println("浏览器将使用当前目录");
            }
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        System.out.println("=== WDF默认路径功能测试 ===");
        System.out.println("测试目标:");
        System.out.println("1. 验证默认路径设置正确");
        System.out.println("2. 测试路径创建和访问权限");
        System.out.println("3. 验证文件浏览器默认路径功能");
        System.out.println("4. 测试WDF文件路径处理逻辑");
        
        testDefaultPath();
        testWdfPathHandling();
        testBrowserDefaultPath();
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("主要功能:");
        System.out.println("✓ 输出目录不再固定，支持浏览选择");
        System.out.println("✓ 浏览时默认打开游戏资源目录");
        System.out.println("✓ 去除了WDF文件名输入框");
        System.out.println("✓ 恢复了完整的文件路径选择功能");
        System.out.println("\n用户体验:");
        System.out.println("- 点击'浏览'按钮时自动打开: " + DEFAULT_PATH);
        System.out.println("- 用户可以选择任意路径和文件名");
        System.out.println("- 保持了操作的灵活性和便利性");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        WdfDefaultPathTest test = new WdfDefaultPathTest();
        test.runAllTests();
    }
}
