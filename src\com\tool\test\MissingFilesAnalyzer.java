package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 缺失文件分析器
 * 目标：找出为什么只提取了5845个文件而不是5872个
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class MissingFilesAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 分析缺失的文件
     */
    public void analyzeMissingFiles() {
        System.out.println("=== 缺失文件分析 ===");
        System.out.println("目标: 找出缺失的27个文件 (5872 - 5845 = 27)");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            
            // 1. 分析解析失败的条目
            analyzeFailedEntries(raf);
            
            // 2. 检查PNG文件过滤逻辑
            analyzePngFiltering(raf);
            
            // 3. 搜索所有PNG文件头并对比
            compareWithDirectPngSearch(raf);
            
            // 4. 分析文件大小和偏移的合理性
            analyzeDataValidity(raf);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析解析失败的条目
     */
    private void analyzeFailedEntries(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 分析解析失败的条目 ---");
        
        long indexOffset = 0x7354149L;
        int totalEntries = 6165;
        int failedCount = 0;
        List<Integer> failedIndices = new ArrayList<>();
        
        for (int i = 0; i < totalEntries; i++) {
            try {
                long entryPos = indexOffset + (i * 28);
                raf.seek(entryPos);
                
                // 读取条目数据
                long field0 = readInt32LE(raf) & 0xFFFFFFFFL;
                long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                long compressedSize = readInt32LE(raf) & 0xFFFFFFFFL;
                long originalSize = readInt32LE(raf) & 0xFFFFFFFFL;
                
                // 检查数据合理性
                if (offset >= raf.length() || compressedSize <= 0 || compressedSize > 100 * 1024 * 1024) {
                    failedCount++;
                    failedIndices.add(i);
                    
                    if (failedCount <= 10) {
                        System.out.println("失败条目 " + i + ": 偏移=0x" + Long.toHexString(offset) + 
                                         ", 压缩大小=" + compressedSize + ", 原始大小=" + originalSize);
                    }
                }
                
            } catch (Exception e) {
                failedCount++;
                failedIndices.add(i);
            }
        }
        
        System.out.println("解析失败的条目数: " + failedCount);
        System.out.println("成功解析的条目数: " + (totalEntries - failedCount));
        
        if (failedCount > 0) {
            System.out.println("失败条目的分布:");
            analyzeFailurePattern(failedIndices);
        }
    }
    
    /**
     * 分析失败模式
     */
    private void analyzeFailurePattern(List<Integer> failedIndices) {
        if (failedIndices.isEmpty()) return;
        
        System.out.println("前10个失败条目: " + failedIndices.subList(0, Math.min(10, failedIndices.size())));
        System.out.println("后10个失败条目: " + failedIndices.subList(Math.max(0, failedIndices.size() - 10), failedIndices.size()));
        
        // 检查是否有连续的失败区域
        int consecutiveStart = -1;
        int maxConsecutive = 0;
        int currentConsecutive = 0;
        
        for (int i = 1; i < failedIndices.size(); i++) {
            if (failedIndices.get(i) == failedIndices.get(i-1) + 1) {
                if (currentConsecutive == 0) {
                    consecutiveStart = failedIndices.get(i-1);
                }
                currentConsecutive++;
            } else {
                if (currentConsecutive > maxConsecutive) {
                    maxConsecutive = currentConsecutive;
                }
                currentConsecutive = 0;
            }
        }
        
        if (maxConsecutive > 0) {
            System.out.println("最大连续失败数: " + (maxConsecutive + 1));
            System.out.println("连续失败开始位置: " + consecutiveStart);
        }
    }
    
    /**
     * 分析PNG文件过滤
     */
    private void analyzePngFiltering(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 分析PNG文件过滤 ---");
        
        try {
            // 使用我们的解析方法
            NpkFile npkFile = NpkTool.parseNpkFile(new File(REAL_NPK_PATH), null);
            List<NpkEntry> allEntries = npkFile.getEntries();
            List<NpkEntry> pngEntries = NpkTool.filterPngFiles(allEntries);
            
            System.out.println("总条目数: " + allEntries.size());
            System.out.println("PNG条目数: " + pngEntries.size());
            System.out.println("非PNG条目数: " + (allEntries.size() - pngEntries.size()));
            
            // 分析非PNG条目
            int nonPngCount = 0;
            for (NpkEntry entry : allEntries) {
                if (!entry.getFileName().toLowerCase().endsWith(".png")) {
                    nonPngCount++;
                    if (nonPngCount <= 10) {
                        System.out.println("非PNG文件 " + nonPngCount + ": " + entry.getFileName());
                    }
                }
            }
            
            System.out.println("实际非PNG文件数: " + nonPngCount);
            
        } catch (Exception e) {
            System.out.println("PNG过滤分析失败: " + e.getMessage());
        }
    }
    
    /**
     * 与直接PNG搜索对比
     */
    private void compareWithDirectPngSearch(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 与直接PNG搜索对比 ---");
        
        // 直接搜索PNG文件头
        Set<Long> pngOffsets = new HashSet<>();
        byte[] buffer = new byte[8192];
        long position = 0;
        
        while (position < raf.length()) {
            raf.seek(position);
            int bytesRead = raf.read(buffer);
            
            for (int i = 0; i <= bytesRead - 8; i++) {
                if (isPngHeader(buffer, i)) {
                    long pngOffset = position + i;
                    pngOffsets.add(pngOffset);
                }
            }
            
            position += bytesRead - 7;
        }
        
        System.out.println("直接搜索找到的PNG文件头数: " + pngOffsets.size());
        
        // 获取我们解析的偏移
        try {
            NpkFile npkFile = NpkTool.parseNpkFile(new File(REAL_NPK_PATH), null);
            List<NpkEntry> pngEntries = NpkTool.filterPngFiles(npkFile.getEntries());
            
            Set<Long> parsedOffsets = new HashSet<>();
            for (NpkEntry entry : pngEntries) {
                parsedOffsets.add(entry.getOffset());
            }
            
            System.out.println("解析得到的PNG偏移数: " + parsedOffsets.size());
            
            // 找出差异
            Set<Long> missingInParsed = new HashSet<>(pngOffsets);
            missingInParsed.removeAll(parsedOffsets);
            
            Set<Long> extraInParsed = new HashSet<>(parsedOffsets);
            extraInParsed.removeAll(pngOffsets);
            
            System.out.println("解析中缺失的PNG偏移数: " + missingInParsed.size());
            System.out.println("解析中多余的偏移数: " + extraInParsed.size());
            
            if (!missingInParsed.isEmpty()) {
                System.out.println("前10个缺失的PNG偏移:");
                int count = 0;
                for (Long offset : missingInParsed) {
                    if (count++ >= 10) break;
                    System.out.println("  0x" + Long.toHexString(offset));
                }
            }
            
        } catch (Exception e) {
            System.out.println("偏移对比失败: " + e.getMessage());
        }
    }
    
    /**
     * 分析数据有效性
     */
    private void analyzeDataValidity(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 分析数据有效性标准 ---");
        
        long indexOffset = 0x7354149L;
        int totalEntries = 6165;
        
        // 统计不同的失败原因
        int offsetTooLarge = 0;
        int sizeZero = 0;
        int sizeTooLarge = 0;
        int validEntries = 0;
        
        for (int i = 0; i < totalEntries; i++) {
            try {
                long entryPos = indexOffset + (i * 28);
                raf.seek(entryPos + 4); // 跳到偏移字段
                
                long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                long compressedSize = readInt32LE(raf) & 0xFFFFFFFFL;
                long originalSize = readInt32LE(raf) & 0xFFFFFFFFL;
                
                if (offset >= raf.length()) {
                    offsetTooLarge++;
                } else if (compressedSize <= 0) {
                    sizeZero++;
                } else if (compressedSize > 100 * 1024 * 1024) {
                    sizeTooLarge++;
                } else {
                    validEntries++;
                }
                
            } catch (Exception e) {
                // 读取错误
            }
        }
        
        System.out.println("数据有效性统计:");
        System.out.println("  有效条目: " + validEntries);
        System.out.println("  偏移过大: " + offsetTooLarge);
        System.out.println("  大小为零: " + sizeZero);
        System.out.println("  大小过大: " + sizeTooLarge);
        System.out.println("  总计: " + (validEntries + offsetTooLarge + sizeZero + sizeTooLarge));
        
        // 分析是否需要调整验证标准
        if (validEntries < 5872) {
            System.out.println("\n建议调整验证标准:");
            System.out.println("当前标准可能过于严格，导致有效文件被过滤");
            
            // 尝试更宽松的标准
            analyzeWithRelaxedStandards(raf, indexOffset, totalEntries);
        }
    }
    
    /**
     * 使用更宽松的标准分析
     */
    private void analyzeWithRelaxedStandards(RandomAccessFile raf, long indexOffset, int totalEntries) throws IOException {
        System.out.println("\n--- 使用更宽松的标准重新分析 ---");
        
        int validWithRelaxed = 0;
        
        for (int i = 0; i < totalEntries; i++) {
            try {
                long entryPos = indexOffset + (i * 28);
                raf.seek(entryPos + 4);
                
                long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                long compressedSize = readInt32LE(raf) & 0xFFFFFFFFL;
                
                // 更宽松的标准
                if (offset < raf.length() && compressedSize > 0 && compressedSize < 500 * 1024 * 1024) {
                    validWithRelaxed++;
                }
                
            } catch (Exception e) {
                // 忽略
            }
        }
        
        System.out.println("使用宽松标准的有效条目数: " + validWithRelaxed);
        System.out.println("差异: " + (validWithRelaxed - 5845));
        
        if (validWithRelaxed >= 5872) {
            System.out.println("✓ 宽松标准可以达到目标数量!");
            System.out.println("建议调整验证逻辑以包含更多有效文件");
        }
    }
    
    /**
     * 检查PNG文件头
     */
    private boolean isPngHeader(byte[] data, int offset) {
        if (data.length < offset + 8) return false;
        return data[offset] == (byte)0x89 && data[offset+1] == (byte)0x50 && 
               data[offset+2] == (byte)0x4E && data[offset+3] == (byte)0x47 && 
               data[offset+4] == (byte)0x0D && data[offset+5] == (byte)0x0A && 
               data[offset+6] == (byte)0x1A && data[offset+7] == (byte)0x0A;
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        MissingFilesAnalyzer analyzer = new MissingFilesAnalyzer();
        analyzer.analyzeMissingFiles();
    }
}
