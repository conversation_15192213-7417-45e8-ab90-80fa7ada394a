package com.tool.test;

import com.tool.npk.NpkEntry;
import com.tool.npk.NpkTool;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试NPK查看功能对WAS文件的支持
 */
public class NpkViewSupportTest {
    
    public static void main(String[] args) {
        System.out.println("=== NPK查看WAS文件支持测试 ===");
        
        // 创建测试条目
        List<NpkEntry> testEntries = createTestEntries();
        
        // 测试过滤功能
        System.out.println("原始条目数: " + testEntries.size());
        
        List<NpkEntry> supportedFiles = NpkTool.filterSupportedFiles(testEntries);
        System.out.println("支持的文件数: " + supportedFiles.size());
        
        List<NpkEntry> pngFiles = NpkTool.filterPngFiles(testEntries);
        System.out.println("PNG文件数（向后兼容）: " + pngFiles.size());
        
        // 显示支持的文件
        System.out.println("\n支持的文件列表:");
        for (NpkEntry entry : supportedFiles) {
            System.out.println("  - " + entry.getFileName() + " (大小: " + entry.getOriginalSize() + " 字节)");
        }
        
        System.out.println("\n✓ NPK查看WAS文件支持测试完成！");
    }
    
    private static List<NpkEntry> createTestEntries() {
        List<NpkEntry> entries = new ArrayList<>();
        
        // 创建PNG文件条目
        NpkEntry pngEntry = new NpkEntry();
        pngEntry.setFileName("test_image.png");
        pngEntry.setOriginalSize(1024);
        entries.add(pngEntry);
        
        // 创建WAS文件条目
        NpkEntry wasEntry = new NpkEntry();
        wasEntry.setFileName("2024-C173F129.was");
        wasEntry.setOriginalSize(2048);
        entries.add(wasEntry);
        
        // 创建其他类型文件条目
        NpkEntry otherEntry = new NpkEntry();
        otherEntry.setFileName("config.txt");
        otherEntry.setOriginalSize(512);
        entries.add(otherEntry);
        
        // 创建无扩展名文件条目
        NpkEntry noExtEntry = new NpkEntry();
        noExtEntry.setFileName("unknown_file");
        noExtEntry.setOriginalSize(256);
        entries.add(noExtEntry);
        
        return entries;
    }
}
