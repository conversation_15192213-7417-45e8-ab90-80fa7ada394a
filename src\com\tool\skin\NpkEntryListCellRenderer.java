package com.tool.skin;

import com.tool.npk.NpkEntry;
import javax.swing.*;
import java.awt.*;

/**
 * NPK条目列表单元格渲染器
 * 自定义显示NPK文件条目的信息
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkEntryListCellRenderer extends DefaultListCellRenderer {
    
    @Override
    public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                                                boolean isSelected, boolean cellHasFocus) {
        
        super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);
        
        if (value instanceof NpkEntry) {
            NpkEntry entry = (NpkEntry) value;
            
            // 创建显示文本
            String displayText = createDisplayText(entry, index);
            setText(displayText);
            
            // 设置图标
            setIcon(getEntryIcon(entry));
            
            // 设置工具提示
            setToolTipText(createTooltipText(entry));
        }
        
        return this;
    }
    
    /**
     * 创建显示文本
     */
    private String createDisplayText(NpkEntry entry, int index) {
        StringBuilder sb = new StringBuilder();

        // 序号
        sb.append(String.format("[%04d] ", index + 1));

        // 文件名
        String fileName = entry.getFileName();
        if (fileName != null && fileName.length() > 25) {
            fileName = fileName.substring(0, 22) + "...";
        }
        sb.append(fileName);

        // 文件大小 - 更详细的显示
        long originalSize = entry.getOriginalSize();
        long compressedSize = entry.getCompressedSize();
        sb.append(" [").append(formatFileSize(originalSize));

        // 如果有压缩，显示压缩信息
        if (compressedSize < originalSize && compressedSize > 0) {
            double compressionRatio = (1.0 - (double)compressedSize / originalSize) * 100;
            sb.append(" → ").append(formatFileSize(compressedSize));
            sb.append(" (").append(String.format("%.1f%%", compressionRatio)).append(")");
        }

        sb.append("]");

        return sb.toString();
    }
    
    /**
     * 获取条目图标
     */
    private Icon getEntryIcon(NpkEntry entry) {
        // 根据文件类型返回不同图标
        String fileName = entry.getFileName();
        if (fileName != null && fileName.toLowerCase().endsWith(".png")) {
            return createColorIcon(Color.GREEN, 12, 12);
        } else {
            return createColorIcon(Color.GRAY, 12, 12);
        }
    }
    
    /**
     * 创建工具提示文本
     */
    private String createTooltipText(NpkEntry entry) {
        StringBuilder sb = new StringBuilder("<html>");
        
        sb.append("<b>文件信息:</b><br>");
        sb.append("文件名: ").append(entry.getFileName()).append("<br>");
        sb.append("偏移: 0x").append(Long.toHexString(entry.getOffset())).append("<br>");
        sb.append("原始大小: ").append(formatFileSize(entry.getOriginalSize())).append("<br>");
        sb.append("压缩大小: ").append(formatFileSize(entry.getCompressedSize())).append("<br>");
        sb.append("压缩类型: ").append(getCompressionTypeName(entry.getCompressionType())).append("<br>");
        
        // 计算压缩率
        if (entry.getOriginalSize() > 0) {
            double compressionRatio = (1.0 - (double)entry.getCompressedSize() / entry.getOriginalSize()) * 100;
            sb.append("压缩率: ").append(String.format("%.1f%%", compressionRatio)).append("<br>");
        }
        
        sb.append("</html>");
        
        return sb.toString();
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取压缩类型名称
     */
    private String getCompressionTypeName(int compressionType) {
        switch (compressionType) {
            case 0: return "无压缩";
            case 1: return "Deflate";
            case 2: return "LZ4";
            case 3: return "ZSTD";
            default: return "未知(" + compressionType + ")";
        }
    }
    
    /**
     * 创建颜色图标
     */
    private Icon createColorIcon(Color color, int width, int height) {
        return new Icon() {
            @Override
            public void paintIcon(Component c, Graphics g, int x, int y) {
                Graphics2D g2d = (Graphics2D) g.create();
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                
                // 绘制圆形图标
                g2d.setColor(color);
                g2d.fillOval(x, y, width, height);
                
                g2d.setColor(color.darker());
                g2d.drawOval(x, y, width, height);
                
                g2d.dispose();
            }
            
            @Override
            public int getIconWidth() {
                return width;
            }
            
            @Override
            public int getIconHeight() {
                return height;
            }
        };
    }
}
