package com.tool.test;

import com.tool.wdf.WdfPacker;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 主题和WDF功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class ThemeAndWdfTest {
    
    private static final String TEST_FOLDER = "wdf_test";
    private static final String TEST_WDF = "test.wdf";
    
    /**
     * 创建测试文件
     */
    public void createTestFiles() {
        System.out.println("=== 创建WDF测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                // 清理旧的测试文件
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
            
            Files.createDirectories(testDir);
            System.out.println("创建测试目录: " + TEST_FOLDER);
            
            // 创建子目录
            Path subDir = testDir.resolve("images");
            Files.createDirectories(subDir);
            
            // 创建测试文件
            String[] testFiles = {
                "readme.txt",
                "config.ini",
                "images/icon.png",
                "images/background.jpg",
                "data/items.dat"
            };
            
            for (String fileName : testFiles) {
                Path filePath = testDir.resolve(fileName);
                Files.createDirectories(filePath.getParent());
                
                // 创建文件内容
                String content = "Test file: " + fileName + "\n";
                content += "Created for WDF packing test\n";
                content += "File size: " + (fileName.length() * 10) + " bytes\n";
                
                Files.write(filePath, content.getBytes("UTF-8"));
                System.out.println("创建测试文件: " + fileName);
            }
            
            System.out.println("测试文件创建完成");
            
        } catch (IOException e) {
            System.out.println("创建测试文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WDF打包功能
     */
    public void testWdfPacking() {
        System.out.println("\n=== 测试WDF打包功能 ===");
        
        Path testDir = Paths.get(TEST_FOLDER);
        if (!Files.exists(testDir)) {
            System.out.println("测试目录不存在，请先创建测试文件");
            return;
        }
        
        try {
            // 创建进度回调
            WdfPacker.ProgressCallback callback = new WdfPacker.ProgressCallback() {
                @Override
                public void onFileProcessed(String fileName, long size) {
                    System.out.println("处理文件: " + fileName + " (" + WdfPacker.formatFileSize(size) + ")");
                }
                
                @Override
                public void onFileWritten(String fileName, long size) {
                    System.out.println("写入文件: " + fileName);
                }
                
                @Override
                public void onCompleted(String outputFile, int fileCount) {
                    System.out.println("\n=== 打包完成 ===");
                    System.out.println("输出文件: " + outputFile);
                    System.out.println("文件数量: " + fileCount);
                    
                    try {
                        long fileSize = new File(outputFile).length();
                        System.out.println("WDF文件大小: " + WdfPacker.formatFileSize(fileSize));
                    } catch (Exception e) {
                        System.out.println("无法获取WDF文件大小");
                    }
                }
                
                @Override
                public void onError(String message) {
                    System.out.println("错误: " + message);
                }
            };
            
            // 执行打包
            WdfPacker.packFolder(TEST_FOLDER, TEST_WDF, callback);
            
        } catch (Exception e) {
            System.out.println("WDF打包测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WDF文件分析
     */
    public void testWdfAnalysis() {
        System.out.println("\n=== 测试WDF文件分析 ===");
        
        File wdfFile = new File(TEST_WDF);
        if (!wdfFile.exists()) {
            System.out.println("WDF文件不存在，请先执行打包测试");
            return;
        }
        
        try {
            WdfPacker.WdfInfo info = WdfPacker.getWdfInfo(TEST_WDF);
            System.out.println("WDF文件信息:");
            System.out.println("  文件路径: " + TEST_WDF);
            System.out.println("  文件数量: " + info.fileCount);
            System.out.println("  文件大小: " + WdfPacker.formatFileSize(info.totalSize));
            System.out.println("WDF文件分析完成");
            
        } catch (Exception e) {
            System.out.println("WDF文件分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试主题颜色
     */
    public void testThemeColors() {
        System.out.println("\n=== 测试主题颜色 ===");
        
        // 测试深色主题颜色
        System.out.println("深色主题颜色:");
        System.out.println("  背景色: 纯黑色 (0, 0, 0)");
        System.out.println("  前景色: 纯白色 (255, 255, 255)");
        System.out.println("  选择色: 深灰色 (64, 64, 64)");
        System.out.println("  边框色: 中灰色 (128, 128, 128)");
        
        // 测试浅色主题颜色
        System.out.println("\n浅色主题颜色:");
        System.out.println("  背景色: 纯白色 (255, 255, 255)");
        System.out.println("  前景色: 纯黑色 (0, 0, 0)");
        System.out.println("  选择色: 浅蓝色 (184, 207, 229)");
        System.out.println("  边框色: 浅灰色 (192, 192, 192)");
        
        System.out.println("\n主题颜色测试完成");
    }
    
    /**
     * 测试文件大小格式化
     */
    public void testFileSizeFormatting() {
        System.out.println("\n=== 测试文件大小格式化 ===");
        
        long[] testSizes = {
            100L,           // 100 B
            1536L,          // 1.5 KB
            2097152L,       // 2.0 MB
            1073741824L,    // 1.0 GB
            5368709120L     // 5.0 GB
        };
        
        for (long size : testSizes) {
            String formatted = WdfPacker.formatFileSize(size);
            System.out.printf("%12d 字节 -> %s%n", size, formatted);
        }
        
        System.out.println("文件大小格式化测试完成");
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        try {
            // 删除测试目录
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            System.out.println("删除: " + path.getFileName());
                        } catch (IOException e) {
                            System.out.println("删除失败: " + path.getFileName());
                        }
                    });
            }
            
            // 删除WDF文件
            File wdfFile = new File(TEST_WDF);
            if (wdfFile.exists()) {
                if (wdfFile.delete()) {
                    System.out.println("删除: " + TEST_WDF);
                } else {
                    System.out.println("删除失败: " + TEST_WDF);
                }
            }
            
            System.out.println("测试文件清理完成");
            
        } catch (IOException e) {
            System.out.println("清理测试文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        createTestFiles();
        testWdfPacking();
        testWdfAnalysis();
        testThemeColors();
        testFileSizeFormatting();
        
        System.out.println("\n=== 所有测试完成 ===");
        System.out.println("如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        ThemeAndWdfTest test = new ThemeAndWdfTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllTests();
        }
    }
}
