package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.List;

/**
 * 提取失败分析器
 * 目标：分析为什么320个文件提取失败
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class ExtractionFailureAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 分析提取失败的原因
     */
    public void analyzeExtractionFailures() {
        System.out.println("=== 提取失败分析 ===");
        System.out.println("目标: 分析320个文件提取失败的原因");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try {
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            List<NpkEntry> allEntries = parsedFile.getEntries();
            
            System.out.println("解析的总条目数: " + allEntries.size());
            
            // 逐个测试提取
            testIndividualExtractions(parsedFile, allEntries);
            
        } catch (Exception e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试单个文件提取
     */
    private void testIndividualExtractions(NpkFile npkFile, List<NpkEntry> entries) {
        System.out.println("\n--- 测试单个文件提取 ---");
        
        int successCount = 0;
        int failureCount = 0;
        
        // 分类统计失败原因
        int offsetError = 0;
        int sizeError = 0;
        int compressionError = 0;
        int dataError = 0;
        int otherError = 0;
        
        for (int i = 0; i < entries.size(); i++) {
            NpkEntry entry = entries.get(i);
            
            try {
                byte[] data = NpkTool.extractFile(npkFile, entry, null);
                
                if (data != null && data.length > 0) {
                    // 验证是否是有效的PNG数据
                    if (isPngData(data)) {
                        successCount++;
                    } else {
                        failureCount++;
                        dataError++;
                        
                        if (failureCount <= 10) {
                            System.out.println("数据错误 " + failureCount + ": " + entry.getFileName() + 
                                             " (大小: " + data.length + ", 不是PNG数据)");
                        }
                    }
                } else {
                    failureCount++;
                    otherError++;
                    
                    if (failureCount <= 10) {
                        System.out.println("提取失败 " + failureCount + ": " + entry.getFileName() + " (数据为空)");
                    }
                }
                
            } catch (Exception e) {
                failureCount++;
                
                // 分析失败原因
                String errorMsg = e.getMessage().toLowerCase();
                if (errorMsg.contains("offset") || errorMsg.contains("seek")) {
                    offsetError++;
                } else if (errorMsg.contains("size") || errorMsg.contains("length")) {
                    sizeError++;
                } else if (errorMsg.contains("compress") || errorMsg.contains("decompress")) {
                    compressionError++;
                } else {
                    otherError++;
                }
                
                if (failureCount <= 10) {
                    System.out.println("异常失败 " + failureCount + ": " + entry.getFileName() + 
                                     " - " + e.getMessage());
                }
            }
            
            // 每1000个文件报告一次进度
            if ((i + 1) % 1000 == 0) {
                System.out.println("进度: " + (i + 1) + "/" + entries.size() + 
                                 ", 成功: " + successCount + ", 失败: " + failureCount);
            }
        }
        
        System.out.println("\n=== 提取结果统计 ===");
        System.out.println("成功提取: " + successCount + " 个");
        System.out.println("提取失败: " + failureCount + " 个");
        System.out.println("成功率: " + (successCount * 100.0 / entries.size()) + "%");
        
        System.out.println("\n=== 失败原因分析 ===");
        System.out.println("偏移错误: " + offsetError + " 个");
        System.out.println("大小错误: " + sizeError + " 个");
        System.out.println("压缩错误: " + compressionError + " 个");
        System.out.println("数据错误: " + dataError + " 个");
        System.out.println("其他错误: " + otherError + " 个");
        
        // 分析失败文件的特征
        analyzeFailurePatterns(npkFile, entries, successCount);
    }
    
    /**
     * 分析失败模式
     */
    private void analyzeFailurePatterns(NpkFile npkFile, List<NpkEntry> entries, int successCount) {
        System.out.println("\n--- 分析失败模式 ---");
        
        // 分析失败文件的偏移分布
        System.out.println("分析失败文件的特征...");
        
        int failureCount = 0;
        long minFailedOffset = Long.MAX_VALUE;
        long maxFailedOffset = 0;
        long totalFailedSize = 0;
        
        for (NpkEntry entry : entries) {
            try {
                byte[] data = NpkTool.extractFile(npkFile, entry, null);
                if (data == null || data.length == 0 || !isPngData(data)) {
                    failureCount++;
                    
                    long offset = entry.getOffset();
                    long size = entry.getCompressedSize();
                    
                    minFailedOffset = Math.min(minFailedOffset, offset);
                    maxFailedOffset = Math.max(maxFailedOffset, offset);
                    totalFailedSize += size;
                    
                    if (failureCount <= 5) {
                        System.out.println("失败文件 " + failureCount + ":");
                        System.out.println("  文件名: " + entry.getFileName());
                        System.out.println("  偏移: 0x" + Long.toHexString(offset));
                        System.out.println("  压缩大小: " + size);
                        System.out.println("  原始大小: " + entry.getOriginalSize());
                        System.out.println("  压缩类型: " + entry.getCompressionType());
                    }
                }
            } catch (Exception e) {
                // 已在上面统计过
            }
        }
        
        if (failureCount > 0) {
            System.out.println("\n失败文件特征统计:");
            System.out.println("失败文件数: " + failureCount);
            System.out.println("最小失败偏移: 0x" + Long.toHexString(minFailedOffset));
            System.out.println("最大失败偏移: 0x" + Long.toHexString(maxFailedOffset));
            System.out.println("平均失败文件大小: " + (totalFailedSize / failureCount) + " 字节");
            
            // 检查是否有特定的偏移范围问题
            if (maxFailedOffset - minFailedOffset < 1000000) { // 1MB范围内
                System.out.println("⚠ 失败文件集中在特定偏移范围，可能是数据损坏区域");
            }
        }
        
        // 建议解决方案
        suggestSolutions(failureCount, successCount);
    }
    
    /**
     * 建议解决方案
     */
    private void suggestSolutions(int failureCount, int successCount) {
        System.out.println("\n--- 建议解决方案 ---");
        
        double failureRate = failureCount * 100.0 / (failureCount + successCount);
        
        if (failureRate < 10) {
            System.out.println("✓ 失败率较低 (" + String.format("%.1f", failureRate) + "%)，当前实现已经很好");
            System.out.println("建议: 可以接受当前结果，或者:");
            System.out.println("1. 调整数据验证标准，允许更多边缘情况");
            System.out.println("2. 改进压缩算法支持");
        } else if (failureRate < 20) {
            System.out.println("⚠ 失败率中等 (" + String.format("%.1f", failureRate) + "%)，需要优化");
            System.out.println("建议:");
            System.out.println("1. 检查压缩算法实现");
            System.out.println("2. 放宽数据验证标准");
            System.out.println("3. 添加更多错误恢复机制");
        } else {
            System.out.println("✗ 失败率较高 (" + String.format("%.1f", failureRate) + "%)，需要重大改进");
            System.out.println("建议:");
            System.out.println("1. 重新检查NPK格式解析");
            System.out.println("2. 实现专业的压缩库");
            System.out.println("3. 参考其他成功的NPK提取器");
        }
        
        System.out.println("\n当前成功提取: " + successCount + " 个文件");
        System.out.println("目标: 5872 个PNG文件");
        System.out.println("差距: " + Math.max(0, 5872 - successCount) + " 个文件");
        
        if (successCount >= 5872) {
            System.out.println("🎉 已经达到或超过目标数量!");
        } else {
            System.out.println("还需要提取 " + (5872 - successCount) + " 个文件才能达到目标");
        }
    }
    
    /**
     * 验证PNG数据
     */
    private boolean isPngData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }
        
        // PNG文件头: 89 50 4E 47 0D 0A 1A 0A
        return data[0] == (byte)0x89 && 
               data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && 
               data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && 
               data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && 
               data[7] == (byte)0x0A;
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        ExtractionFailureAnalyzer analyzer = new ExtractionFailureAnalyzer();
        analyzer.analyzeExtractionFailures();
    }
}
