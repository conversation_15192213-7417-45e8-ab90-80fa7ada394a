package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/**
 * 完整6165条目提取器
 * 尝试找到所有6165个PNG文件
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class Full6165EntryExtractor {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 尝试提取所有6165个条目
     */
    public void extractAll6165Entries() {
        System.out.println("=== 尝试提取所有6165个条目 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            long indexOffset = 0x7354149L;
            long fileSize = raf.length();
            
            System.out.println("文件大小: " + fileSize);
            System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
            
            // 策略1: 尝试不同的条目结构
            tryAlternativeStructures(raf, indexOffset, fileSize);
            
            // 策略2: 搜索所有PNG文件头，反推条目
            searchAllPngHeaders(raf);
            
        } catch (IOException e) {
            System.out.println("提取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 尝试不同的条目结构
     */
    private void tryAlternativeStructures(RandomAccessFile raf, long indexOffset, long fileSize) throws IOException {
        System.out.println("\n--- 尝试不同的条目结构 ---");
        
        // 策略1: 前3083个用56字节步长，后面的用不同步长
        System.out.println("策略1: 混合步长解析");
        
        List<EntryInfo> entries = new ArrayList<>();
        
        // 前3083个条目用56字节步长
        for (int i = 0; i < 3083; i++) {
            long entryPos = indexOffset + (i * 56);
            EntryInfo entry = parseEntry(raf, entryPos, fileSize);
            if (entry != null) {
                entries.add(entry);
            }
        }
        
        System.out.println("前3083个条目解析完成: " + entries.size() + " 个有效");
        
        // 尝试在剩余空间中找到其他条目
        long remainingStart = indexOffset + (3083 * 56);
        long remainingSize = fileSize - remainingStart;
        
        System.out.println("剩余空间: " + remainingSize + " 字节");
        
        // 策略2: 在剩余空间中搜索可能的条目
        System.out.println("在剩余空间中搜索条目...");
        
        for (long pos = remainingStart; pos < fileSize - 16; pos += 4) {
            EntryInfo entry = parseEntry(raf, pos, fileSize);
            if (entry != null && !isDuplicateOffset(entries, entry.offset)) {
                entries.add(entry);
                if (entries.size() % 100 == 0) {
                    System.out.println("找到 " + entries.size() + " 个条目...");
                }
                if (entries.size() >= 6165) {
                    break;
                }
            }
        }
        
        System.out.println("总共找到 " + entries.size() + " 个条目");
        
        if (entries.size() >= 6000) {
            System.out.println("接近目标6165个条目！");
            testExtractSomeEntries(raf, entries);
        }
    }
    
    /**
     * 搜索所有PNG文件头
     */
    private void searchAllPngHeaders(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 搜索所有PNG文件头 ---");
        
        List<Long> pngOffsets = new ArrayList<>();
        byte[] buffer = new byte[8192];
        long position = 0;
        
        while (position < raf.length()) {
            raf.seek(position);
            int bytesRead = raf.read(buffer);
            
            for (int i = 0; i <= bytesRead - 8; i++) {
                if (isPngHeader(buffer, i)) {
                    long pngOffset = position + i;
                    pngOffsets.add(pngOffset);
                    
                    if (pngOffsets.size() % 100 == 0) {
                        System.out.println("找到 " + pngOffsets.size() + " 个PNG文件头...");
                    }
                    
                    if (pngOffsets.size() >= 6165) {
                        break;
                    }
                }
            }
            
            if (pngOffsets.size() >= 6165) {
                break;
            }
            
            position += bytesRead - 7; // 重叠7字节
        }
        
        System.out.println("总共找到 " + pngOffsets.size() + " 个PNG文件头");
        
        if (pngOffsets.size() >= 6000) {
            System.out.println("找到足够的PNG文件！");
            
            // 测试提取前几个
            System.out.println("测试提取前10个PNG文件:");
            for (int i = 0; i < Math.min(10, pngOffsets.size()); i++) {
                long offset = pngOffsets.get(i);
                System.out.println("PNG #" + i + " 偏移: 0x" + Long.toHexString(offset));
                
                // 尝试确定文件大小
                long size = estimatePngSize(raf, offset);
                System.out.println("  估计大小: " + size + " 字节");
                
                if (size > 0 && size < 10 * 1024 * 1024) {
                    String fileName = "0x" + Long.toHexString(offset).toLowerCase() + ".png";
                    System.out.println("  文件名: " + fileName);
                }
            }
        }
    }
    
    /**
     * 解析条目
     */
    private EntryInfo parseEntry(RandomAccessFile raf, long pos, long fileSize) throws IOException {
        if (pos + 16 > fileSize) {
            return null;
        }
        
        raf.seek(pos);
        long f1 = readInt32LE(raf) & 0xFFFFFFFFL;
        long f2 = readInt32LE(raf) & 0xFFFFFFFFL;
        long f3 = readInt32LE(raf) & 0xFFFFFFFFL;
        long f4 = readInt32LE(raf) & 0xFFFFFFFFL;
        
        // 验证f2是否是合理的偏移
        if (f2 < fileSize && f3 > 0 && f3 < 100 * 1024 * 1024) {
            return new EntryInfo(f2, f3, f4);
        }
        
        return null;
    }
    
    /**
     * 检查是否是重复的偏移
     */
    private boolean isDuplicateOffset(List<EntryInfo> entries, long offset) {
        for (EntryInfo entry : entries) {
            if (entry.offset == offset) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 测试提取一些条目
     */
    private void testExtractSomeEntries(RandomAccessFile raf, List<EntryInfo> entries) throws IOException {
        System.out.println("\n测试提取前10个条目:");
        
        for (int i = 0; i < Math.min(10, entries.size()); i++) {
            EntryInfo entry = entries.get(i);
            
            try {
                raf.seek(entry.offset);
                byte[] data = new byte[8];
                raf.read(data);
                
                System.out.print("条目 " + i + " (偏移: 0x" + Long.toHexString(entry.offset) + "): ");
                for (byte b : data) {
                    System.out.printf("%02X ", b & 0xFF);
                }
                
                if (isPngHeader(data, 0)) {
                    System.out.print(" <- PNG!");
                } else if (isZlibHeader(data)) {
                    System.out.print(" <- zlib压缩!");
                }
                
                System.out.println();
                
            } catch (Exception e) {
                System.out.println("条目 " + i + " 读取失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 估计PNG文件大小
     */
    private long estimatePngSize(RandomAccessFile raf, long offset) throws IOException {
        // 简单估计：读取到下一个PNG文件头或文件末尾
        raf.seek(offset + 8); // 跳过PNG文件头
        
        byte[] buffer = new byte[1024];
        long currentPos = offset + 8;
        long maxSize = 10 * 1024 * 1024; // 最大10MB
        
        while (currentPos < raf.length() && (currentPos - offset) < maxSize) {
            int toRead = (int)Math.min(buffer.length, raf.length() - currentPos);
            int bytesRead = raf.read(buffer, 0, toRead);
            
            if (bytesRead <= 0) break;
            
            // 查找下一个PNG文件头
            for (int i = 0; i <= bytesRead - 8; i++) {
                if (isPngHeader(buffer, i)) {
                    return currentPos + i - offset;
                }
            }
            
            currentPos += bytesRead;
        }
        
        // 如果没找到下一个PNG，返回一个合理的估计值
        return Math.min(100 * 1024, raf.length() - offset); // 最多100KB
    }
    
    /**
     * 检查PNG文件头
     */
    private boolean isPngHeader(byte[] data, int offset) {
        if (data.length < offset + 8) return false;
        return data[offset] == (byte)0x89 && data[offset+1] == (byte)0x50 && 
               data[offset+2] == (byte)0x4E && data[offset+3] == (byte)0x47 && 
               data[offset+4] == (byte)0x0D && data[offset+5] == (byte)0x0A && 
               data[offset+6] == (byte)0x1A && data[offset+7] == (byte)0x0A;
    }
    
    /**
     * 检查zlib文件头
     */
    private boolean isZlibHeader(byte[] data) {
        if (data.length < 2) return false;
        return (data[0] & 0xFF) == 0x78 && (data[1] & 0xFF) == 0x9C;
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 条目信息类
     */
    private static class EntryInfo {
        long offset;
        long compressedSize;
        long originalSize;
        
        EntryInfo(long offset, long compressedSize, long originalSize) {
            this.offset = offset;
            this.compressedSize = compressedSize;
            this.originalSize = originalSize;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        Full6165EntryExtractor extractor = new Full6165EntryExtractor();
        extractor.extractAll6165Entries();
    }
}
