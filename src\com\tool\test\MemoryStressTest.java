package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 内存压力测试工具 - 模拟大量文件扫描
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class MemoryStressTest {
    
    private static final int BATCH_SIZE = 500;
    private static List<String> fileRecords = new ArrayList<>(1000);
    
    public static void main(String[] args) {
        System.out.println("=== 内存压力测试 - 模拟文件扫描 ===");
        
        // 设置较小的堆内存进行测试
        printMemoryUsage("测试开始");
        
        // 测试1：模拟扫描大量文件
        testMassiveFileScan();
        
        // 测试2：模拟分批处理
        testBatchProcessing();
        
        // 测试3：模拟内存压力下的处理
        testMemoryPressureHandling();
        
        System.out.println("\n=== 压力测试完成 ===");
    }
    
    /**
     * 测试大量文件扫描
     */
    private static void testMassiveFileScan() {
        System.out.println("\n--- 测试1：大量文件扫描 ---");
        printMemoryUsage("扫描前");
        
        // 模拟扫描50000个文件
        List<String> fileList = new ArrayList<>(50000);
        for (int i = 0; i < 50000; i++) {
            String fileName = "2025-" + String.format("%08X", i) + ".png";
            fileList.add(fileName);
            
            // 每1000个文件检查一次内存
            if (i % 1000 == 0) {
                checkMemoryPressure("扫描第 " + i + " 个文件");
            }
        }
        
        printMemoryUsage("扫描50000个文件后");
        
        // 清理
        fileList.clear();
        fileList = null;
        System.gc();
        
        printMemoryUsage("清理后");
    }
    
    /**
     * 测试分批处理
     */
    private static void testBatchProcessing() {
        System.out.println("\n--- 测试2：分批处理 ---");
        printMemoryUsage("分批处理前");
        
        AtomicInteger totalProcessed = new AtomicInteger(0);
        
        // 模拟10批处理，每批500个文件
        for (int batch = 1; batch <= 10; batch++) {
            List<String> currentBatch = new ArrayList<>(BATCH_SIZE);
            
            // 填充当前批次
            for (int i = 0; i < BATCH_SIZE; i++) {
                String fileName = "batch" + batch + "_" + String.format("%08X", i) + ".png";
                currentBatch.add(fileName);
            }
            
            // 处理当前批次
            processBatch(currentBatch, batch, totalProcessed);
            
            // 清理当前批次
            currentBatch.clear();
            
            // 每批后进行垃圾回收
            System.gc();
            
            if (batch % 3 == 0) {
                printMemoryUsage("处理第 " + batch + " 批后");
            }
        }
        
        printMemoryUsage("分批处理完成");
    }
    
    /**
     * 处理一批文件
     */
    private static void processBatch(List<String> batch, int batchNumber, AtomicInteger totalProcessed) {
        System.out.println("处理第 " + batchNumber + " 批文件 (" + batch.size() + " 个)...");
        
        for (String fileName : batch) {
            // 模拟文件处理
            String processedName = fileName.replace(".png", "_processed.png");
            fileRecords.add(processedName);
            
            totalProcessed.incrementAndGet();
            
            // 模拟一些计算
            if (fileName.contains("batch5")) {
                // 模拟复杂处理
                for (int i = 0; i < 100; i++) {
                    String temp = fileName + "_temp_" + i;
                    // 创建临时对象
                }
            }
        }
    }
    
    /**
     * 测试内存压力处理
     */
    private static void testMemoryPressureHandling() {
        System.out.println("\n--- 测试3：内存压力处理 ---");
        printMemoryUsage("压力测试前");
        
        List<List<String>> memoryConsumer = new ArrayList<>();
        
        try {
            // 逐步增加内存使用直到接近限制
            for (int i = 0; i < 100; i++) {
                List<String> chunk = new ArrayList<>(10000);
                
                // 填充大量数据
                for (int j = 0; j < 10000; j++) {
                    chunk.add("压力测试数据_" + i + "_" + j + "_" + System.currentTimeMillis());
                }
                
                memoryConsumer.add(chunk);
                
                // 每10次检查内存压力
                if (i % 10 == 0) {
                    checkMemoryPressure("压力测试第 " + i + " 轮");
                    
                    // 如果内存使用率过高，停止测试
                    if (getMemoryUsagePercent() > 80) {
                        System.out.println("内存使用率过高，停止压力测试");
                        break;
                    }
                }
            }
        } catch (OutOfMemoryError e) {
            System.out.println("捕获到内存溢出错误：" + e.getMessage());
        }
        
        printMemoryUsage("压力测试后");
        
        // 清理
        memoryConsumer.clear();
        System.gc();
        
        printMemoryUsage("压力测试清理后");
    }
    
    /**
     * 检查内存压力
     */
    private static void checkMemoryPressure(String operation) {
        double usagePercent = getMemoryUsagePercent();
        
        if (usagePercent > 85) {
            System.out.println("⚠️ " + operation + " - 内存压力过大 (" + String.format("%.1f", usagePercent) + "%)，执行垃圾回收...");
            System.gc();
            System.runFinalization();
            
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            usagePercent = getMemoryUsagePercent();
            System.out.println("垃圾回收后内存使用率: " + String.format("%.1f", usagePercent) + "%");
            
        } else if (usagePercent > 70) {
            System.out.println("⚠️ " + operation + " - 内存使用率较高 (" + String.format("%.1f", usagePercent) + "%)");
        }
    }
    
    /**
     * 获取内存使用百分比
     */
    private static double getMemoryUsagePercent() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        return (usedMemory * 100.0) / maxMemory;
    }
    
    /**
     * 打印内存使用情况
     */
    private static void printMemoryUsage(String operation) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        double usedMB = usedMemory / 1024.0 / 1024.0;
        double maxMB = maxMemory / 1024.0 / 1024.0;
        double usagePercent = (usedMemory * 100.0) / maxMemory;
        
        System.out.printf("%s: %.1fMB / %.1fMB (%.1f%%)\n", 
            operation, usedMB, maxMB, usagePercent);
        
        if (usagePercent > 80) {
            System.out.println("  ⚠️ 内存使用率过高！");
        } else if (usagePercent > 60) {
            System.out.println("  ⚠️ 内存使用率中等");
        } else {
            System.out.println("  ✅ 内存使用率正常");
        }
    }
}
