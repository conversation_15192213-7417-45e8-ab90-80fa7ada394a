package com.tool.test;

import com.tool.npk.NpkTool;
import com.tool.npk.NpkFile;
import com.tool.npk.NpkEntry;
import java.io.File;
import java.util.List;

/**
 * 调试NPK文件读取过程
 */
public class NpkReadDebugTest {
    
    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("用法: java NpkReadDebugTest <npk文件路径>");
            System.out.println("例如: java NpkReadDebugTest test.npk");
            return;
        }
        
        String npkFilePath = args[0];
        File npkFile = new File(npkFilePath);
        
        if (!npkFile.exists()) {
            System.err.println("NPK文件不存在: " + npkFilePath);
            return;
        }
        
        System.out.println("=== NPK文件读取调试 ===");
        System.out.println("文件路径: " + npkFilePath);
        System.out.println("文件大小: " + npkFile.length() + " 字节");
        System.out.println();
        
        try {
            // 解析NPK文件
            NpkFile parsedNpk = NpkTool.parseNpkFile(npkFile, (progress, message) -> {
                System.out.println("[" + progress + "%] " + message);
            });
            
            if (parsedNpk == null) {
                System.err.println("NPK文件解析失败！");
                return;
            }
            
            System.out.println("\n=== 解析结果 ===");
            List<NpkEntry> entries = parsedNpk.getEntries();
            System.out.println("总条目数: " + entries.size());
            
            System.out.println("\n=== 文件名列表 ===");
            for (int i = 0; i < Math.min(20, entries.size()); i++) {
                NpkEntry entry = entries.get(i);
                System.out.println(String.format("[%03d] %s (原始大小: %d, 压缩大小: %d, 压缩类型: %d)", 
                    i, entry.getFileName(), entry.getOriginalSize(), 
                    entry.getCompressedSize(), entry.getCompressionType()));
            }
            
            if (entries.size() > 20) {
                System.out.println("... 还有 " + (entries.size() - 20) + " 个文件");
            }
            
            System.out.println("\n✓ 调试完成！");
            
        } catch (Exception e) {
            System.err.println("调试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
