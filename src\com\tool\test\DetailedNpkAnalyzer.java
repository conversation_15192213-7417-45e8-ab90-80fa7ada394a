package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;

/**
 * 详细的NPK文件结构分析器
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class DetailedNpkAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 详细分析NPK文件结构
     */
    public void analyzeDetailedStructure() {
        System.out.println("=== 详细NPK文件结构分析 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            System.out.println("文件大小: " + formatFileSize(raf.length()));
            
            // 详细分析文件头
            analyzeDetailedHeader(raf);
            
            // 分析索引区域的实际结构
            analyzeActualIndexStructure(raf);
            
            // 尝试计算正确的条目大小
            calculateEntrySize(raf);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 详细分析文件头
     */
    private void analyzeDetailedHeader(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 详细文件头分析 ---");
        
        raf.seek(0);
        
        // 显示前64字节的完整内容
        byte[] header = new byte[64];
        raf.read(header);
        
        System.out.println("文件头64字节内容:");
        for (int i = 0; i < header.length; i += 16) {
            System.out.printf("%08X: ", i);
            for (int j = 0; j < 16 && i + j < header.length; j++) {
                System.out.printf("%02X ", header[i + j] & 0xFF);
            }
            System.out.print(" | ");
            for (int j = 0; j < 16 && i + j < header.length; j++) {
                char c = (char)(header[i + j] & 0xFF);
                System.out.print(c >= 32 && c < 127 ? c : '.');
            }
            System.out.println();
        }
        
        // 逐字段分析
        raf.seek(0);
        String signature = readString(raf, 4);
        int entryCount = readInt32LE(raf);
        int field1 = readInt32LE(raf);
        int field2 = readInt32LE(raf);
        int field3 = readInt32LE(raf);
        int field4 = readInt32LE(raf);
        int field5 = readInt32LE(raf);
        
        System.out.println("\n字段解析:");
        System.out.println("偏移 0: 签名 = " + signature);
        System.out.println("偏移 4: 条目数 = " + entryCount);
        System.out.println("偏移 8: 字段1 = 0x" + Integer.toHexString(field1) + " (" + field1 + ")");
        System.out.println("偏移12: 字段2 = 0x" + Integer.toHexString(field2) + " (" + field2 + ")");
        System.out.println("偏移16: 字段3 = 0x" + Integer.toHexString(field3) + " (" + field3 + ")");
        System.out.println("偏移20: 字段4 = 0x" + Integer.toHexString(field4) + " (" + field4 + ")");
        System.out.println("偏移24: 字段5 = 0x" + Integer.toHexString(field5) + " (" + field5 + ")");
        
        // 检查哪个字段可能是索引偏移
        long fileSize = raf.length();
        System.out.println("\n可能的索引偏移:");
        if (field3 > 0 && field3 < fileSize) {
            System.out.println("字段3可能是索引偏移: 0x" + Integer.toHexString(field3));
        }
        if (field4 > 0 && field4 < fileSize) {
            System.out.println("字段4可能是索引偏移: 0x" + Integer.toHexString(field4));
        }
        if (field5 > 0 && field5 < fileSize) {
            System.out.println("字段5可能是索引偏移: 0x" + Integer.toHexString(field5));
        }
    }
    
    /**
     * 分析实际的索引结构
     */
    private void analyzeActualIndexStructure(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 实际索引结构分析 ---");
        
        // 尝试不同的索引偏移
        long[] possibleOffsets = {
            readInt32LEAt(raf, 16) & 0xFFFFFFFFL,
            readInt32LEAt(raf, 20) & 0xFFFFFFFFL,
            readInt32LEAt(raf, 24) & 0xFFFFFFFFL
        };
        
        for (int i = 0; i < possibleOffsets.length; i++) {
            long offset = possibleOffsets[i];
            if (offset > 0 && offset < raf.length()) {
                System.out.println("\n尝试索引偏移 0x" + Long.toHexString(offset) + ":");
                analyzeIndexAtOffset(raf, offset);
            }
        }
    }
    
    /**
     * 分析指定偏移处的索引
     */
    private void analyzeIndexAtOffset(RandomAccessFile raf, long offset) throws IOException {
        raf.seek(offset);
        
        System.out.println("索引区域前128字节:");
        byte[] indexData = new byte[128];
        int bytesRead = raf.read(indexData);
        
        for (int i = 0; i < bytesRead; i += 16) {
            System.out.printf("%08X: ", (int)(offset + i));
            for (int j = 0; j < 16 && i + j < bytesRead; j++) {
                System.out.printf("%02X ", indexData[i + j] & 0xFF);
            }
            System.out.print(" | ");
            for (int j = 0; j < 16 && i + j < bytesRead; j++) {
                char c = (char)(indexData[i + j] & 0xFF);
                System.out.print(c >= 32 && c < 127 ? c : '.');
            }
            System.out.println();
        }
        
        // 尝试解析前几个条目
        raf.seek(offset);
        System.out.println("\n尝试解析条目:");
        
        for (int i = 0; i < 3; i++) {
            try {
                long pos = raf.getFilePointer();
                System.out.println("条目 " + i + " (位置: 0x" + Long.toHexString(pos) + "):");
                
                // 尝试不同的解析方式
                tryParseAsVariableLength(raf, pos);
                
                // 重置位置，尝试固定长度
                raf.seek(pos);
                tryParseAsFixedLength(raf, pos);
                
                // 移动到下一个可能的条目位置
                raf.seek(pos + 32); // 假设32字节一个条目
                
            } catch (Exception e) {
                System.out.println("  解析失败: " + e.getMessage());
                break;
            }
        }
    }
    
    /**
     * 尝试按变长结构解析
     */
    private void tryParseAsVariableLength(RandomAccessFile raf, long pos) throws IOException {
        System.out.println("  尝试变长结构:");
        
        // 读取可能的文件名长度
        int nameLen1 = raf.read() | (raf.read() << 8); // 小端序2字节
        raf.seek(pos);
        int nameLen2 = readInt32LE(raf); // 4字节
        
        System.out.println("    可能的文件名长度(2字节): " + nameLen1);
        System.out.println("    可能的文件名长度(4字节): " + nameLen2);
        
        if (nameLen1 > 0 && nameLen1 < 256) {
            raf.seek(pos + 2);
            byte[] nameBytes = new byte[nameLen1];
            raf.read(nameBytes);
            String fileName = new String(nameBytes, "UTF-8");
            System.out.println("    文件名(2字节长度): " + fileName);
        }
        
        if (nameLen2 > 0 && nameLen2 < 256) {
            raf.seek(pos + 4);
            byte[] nameBytes = new byte[nameLen2];
            raf.read(nameBytes);
            String fileName = new String(nameBytes, "UTF-8");
            System.out.println("    文件名(4字节长度): " + fileName);
        }
    }
    
    /**
     * 尝试按固定长度结构解析
     */
    private void tryParseAsFixedLength(RandomAccessFile raf, long pos) throws IOException {
        System.out.println("  尝试固定长度结构:");
        
        raf.seek(pos);
        long field1 = readInt32LE(raf) & 0xFFFFFFFFL;
        long field2 = readInt32LE(raf) & 0xFFFFFFFFL;
        long field3 = readInt32LE(raf) & 0xFFFFFFFFL;
        long field4 = readInt32LE(raf) & 0xFFFFFFFFL;
        
        System.out.println("    字段1: 0x" + Long.toHexString(field1) + " (" + field1 + ")");
        System.out.println("    字段2: 0x" + Long.toHexString(field2) + " (" + field2 + ")");
        System.out.println("    字段3: 0x" + Long.toHexString(field3) + " (" + field3 + ")");
        System.out.println("    字段4: 0x" + Long.toHexString(field4) + " (" + field4 + ")");
        
        // 判断哪些可能是偏移和大小
        if (field1 < raf.length() && field2 < 100 * 1024 * 1024) {
            System.out.println("    可能: 偏移=" + field1 + ", 大小=" + field2);
        }
    }
    
    /**
     * 计算条目大小
     */
    private void calculateEntrySize(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 计算条目大小 ---");
        
        int entryCount = readInt32LEAt(raf, 4);
        long indexOffset = readInt32LEAt(raf, 20) & 0xFFFFFFFFL;
        
        if (indexOffset > 0 && indexOffset < raf.length()) {
            long indexSize = raf.length() - indexOffset;
            double avgEntrySize = (double)indexSize / entryCount;
            
            System.out.println("条目数量: " + entryCount);
            System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
            System.out.println("索引大小: " + indexSize + " 字节");
            System.out.println("平均条目大小: " + String.format("%.2f", avgEntrySize) + " 字节");
            
            if (avgEntrySize < 50) {
                System.out.println("可能是固定长度结构，每个条目约 " + Math.round(avgEntrySize) + " 字节");
            } else {
                System.out.println("可能是变长结构，包含文件名");
            }
        }
    }
    
    /**
     * 在指定位置读取32位小端序整数
     */
    private int readInt32LEAt(RandomAccessFile raf, long pos) throws IOException {
        raf.seek(pos);
        return readInt32LE(raf);
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 读取字符串
     */
    private String readString(RandomAccessFile raf, int length) throws IOException {
        byte[] bytes = new byte[length];
        raf.read(bytes);
        return new String(bytes, "UTF-8");
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        DetailedNpkAnalyzer analyzer = new DetailedNpkAnalyzer();
        analyzer.analyzeDetailedStructure();
    }
}
