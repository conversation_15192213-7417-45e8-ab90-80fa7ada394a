package com.tool.skin;

import java.util.HashSet;
import java.util.Random;
import java.util.Set;

/**
 * 测试随机十六进制ID生成功能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class RandomHexIdTest {
    
    private Random random = new Random();
    
    /**
     * 生成随机的8位十六进制ID
     */
    private String generateRandomHexId() {
        // 生成一个随机的32位整数，确保结果是8位十六进制
        long randomValue = random.nextLong() & 0xFFFFFFFFL; // 确保是正数且在32位范围内
        // 确保生成的十六进制至少以5-9或A-F开头（避免与小数值冲突）
        if (randomValue < 0x50000000L) {
            randomValue += 0x50000000L;
        }
        return String.format("%08X", randomValue);
    }
    
    /**
     * 测试随机ID生成
     */
    public void testRandomIdGeneration() {
        System.out.println("=== 随机十六进制ID生成测试 ===");
        
        Set<String> generatedIds = new HashSet<>();
        int testCount = 20;
        
        System.out.println("生成 " + testCount + " 个随机ID：");
        
        for (int i = 0; i < testCount; i++) {
            String hexId = generateRandomHexId();
            String fileName = "2025-" + hexId + ".png";
            String skinId = "0x" + hexId;
            
            System.out.printf("%2d. 文件名: %-20s 皮肤ID: %s%n", 
                i + 1, fileName, skinId);
            
            // 检查是否有重复
            if (generatedIds.contains(hexId)) {
                System.out.println("    警告: 发现重复ID!");
            } else {
                generatedIds.add(hexId);
            }
        }
        
        System.out.println("\n=== 测试结果 ===");
        System.out.println("生成的唯一ID数量: " + generatedIds.size() + "/" + testCount);
        System.out.println("重复率: " + ((testCount - generatedIds.size()) * 100.0 / testCount) + "%");
        
        // 验证ID格式
        System.out.println("\n=== ID格式验证 ===");
        for (String id : generatedIds) {
            if (id.length() != 8) {
                System.out.println("错误: ID长度不是8位: " + id);
            }
            
            char firstChar = id.charAt(0);
            if (firstChar < '5' && firstChar < 'A') {
                System.out.println("警告: ID首位可能与小数值冲突: " + id);
            }
        }
        
        System.out.println("格式验证完成");
    }
    
    /**
     * 测试文件名转换示例
     */
    public void testFileNameConversion() {
        System.out.println("\n=== 文件名转换示例 ===");
        
        String[] testFileNames = {
            "weapon_sword.png",
            "armor_001.png", 
            "9134.png",
            "item_potion_health_large.png",
            "skill_fireball.png",
            "npc_merchant.png",
            "background_forest.png"
        };
        
        for (String originalFileName : testFileNames) {
            String hexId = generateRandomHexId();
            String newFileName = "2025-" + hexId + ".png";
            String oldSkinId = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
            String newSkinId = "0x" + hexId;
            
            System.out.printf("原文件名: %-30s -> 新文件名: %s%n", originalFileName, newFileName);
            System.out.printf("原皮肤ID: %-29s -> 新皮肤ID: %s%n", oldSkinId, newSkinId);
            System.out.println();
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        RandomHexIdTest test = new RandomHexIdTest();
        test.testRandomIdGeneration();
        test.testFileNameConversion();
    }
}
