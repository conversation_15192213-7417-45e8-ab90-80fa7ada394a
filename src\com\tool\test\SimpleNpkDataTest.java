package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;

/**
 * 简单的NPK数据验证测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SimpleNpkDataTest {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 测试数据提取
     */
    public void testDataExtraction() {
        System.out.println("=== 简单NPK数据提取测试 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            
            // 跳转到索引位置
            long indexOffset = 0x7354149L;
            raf.seek(indexOffset);
            
            System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
            System.out.println("文件大小: " + formatFileSize(raf.length()));
            
            // 分析前几个条目，寻找PNG文件头
            for (int i = 0; i < 10; i++) {
                long entryPos = indexOffset + (i * 56);
                raf.seek(entryPos);
                
                System.out.println("\n--- 条目 " + i + " (位置: 0x" + Long.toHexString(entryPos) + ") ---");
                
                // 读取32字节的条目数据
                byte[] entryData = new byte[32];
                raf.read(entryData);
                
                // 显示条目数据
                System.out.println("条目数据:");
                for (int j = 0; j < entryData.length; j += 8) {
                    System.out.printf("  ");
                    for (int k = 0; k < 8 && j + k < entryData.length; k++) {
                        System.out.printf("%02X ", entryData[j + k] & 0xFF);
                    }
                    System.out.println();
                }
                
                // 尝试解析为不同的字段组合
                raf.seek(entryPos);
                long f1 = readInt32LE(raf) & 0xFFFFFFFFL;
                long f2 = readInt32LE(raf) & 0xFFFFFFFFL;
                long f3 = readInt32LE(raf) & 0xFFFFFFFFL;
                long f4 = readInt32LE(raf) & 0xFFFFFFFFL;
                
                System.out.println("字段解析:");
                System.out.println("  字段1: 0x" + Long.toHexString(f1) + " (" + f1 + ")");
                System.out.println("  字段2: 0x" + Long.toHexString(f2) + " (" + f2 + ")");
                System.out.println("  字段3: 0x" + Long.toHexString(f3) + " (" + f3 + ")");
                System.out.println("  字段4: 0x" + Long.toHexString(f4) + " (" + f4 + ")");
                
                // 测试不同的偏移和大小组合
                testOffsetSizeCombination(raf, "f1+f2", f1, f2);
                testOffsetSizeCombination(raf, "f2+f3", f2, f3);
                testOffsetSizeCombination(raf, "f1+f3", f1, f3);
                testOffsetSizeCombination(raf, "f2+f4", f2, f4);
            }
            
        } catch (IOException e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试偏移和大小组合
     */
    private void testOffsetSizeCombination(RandomAccessFile raf, String name, long offset, long size) throws IOException {
        if (offset <= 0 || offset >= raf.length() || size <= 0 || size > 10 * 1024 * 1024) {
            return; // 跳过不合理的组合
        }
        
        try {
            raf.seek(offset);
            byte[] data = new byte[(int)Math.min(size, 16)]; // 只读取前16字节
            int bytesRead = raf.read(data);
            
            System.out.println("  " + name + " (偏移: 0x" + Long.toHexString(offset) + ", 大小: " + size + "):");
            System.out.print("    数据: ");
            for (int i = 0; i < bytesRead; i++) {
                System.out.printf("%02X ", data[i] & 0xFF);
            }
            
            // 检查是否是PNG文件头
            if (bytesRead >= 8 && isPngHeader(data)) {
                System.out.print(" <- PNG文件头!");
            }
            
            System.out.println();
            
        } catch (Exception e) {
            // 忽略读取错误
        }
    }
    
    /**
     * 检查是否是PNG文件头
     */
    private boolean isPngHeader(byte[] data) {
        if (data.length < 8) {
            return false;
        }
        
        return data[0] == (byte)0x89 && 
               data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && 
               data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && 
               data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && 
               data[7] == (byte)0x0A;
    }
    
    /**
     * 搜索PNG文件头
     */
    public void searchPngHeaders() {
        System.out.println("\n=== 搜索PNG文件头 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            
            byte[] buffer = new byte[8192];
            long position = 0;
            int pngCount = 0;
            
            System.out.println("搜索PNG文件头 (89 50 4E 47 0D 0A 1A 0A)...");
            
            while (position < raf.length()) {
                raf.seek(position);
                int bytesRead = raf.read(buffer);
                
                for (int i = 0; i <= bytesRead - 8; i++) {
                    if (buffer[i] == (byte)0x89 && 
                        buffer[i+1] == (byte)0x50 && 
                        buffer[i+2] == (byte)0x4E && 
                        buffer[i+3] == (byte)0x47 && 
                        buffer[i+4] == (byte)0x0D && 
                        buffer[i+5] == (byte)0x0A && 
                        buffer[i+6] == (byte)0x1A && 
                        buffer[i+7] == (byte)0x0A) {
                        
                        long pngOffset = position + i;
                        pngCount++;
                        
                        System.out.println("找到PNG #" + pngCount + " 在偏移: 0x" + Long.toHexString(pngOffset) + " (" + pngOffset + ")");
                        
                        if (pngCount >= 10) {
                            System.out.println("已找到10个PNG文件头，停止搜索...");
                            return;
                        }
                    }
                }
                
                position += bytesRead - 7; // 重叠7字节以防PNG头跨越缓冲区边界
            }
            
            System.out.println("搜索完成，总共找到 " + pngCount + " 个PNG文件头");
            
        } catch (IOException e) {
            System.out.println("搜索失败: " + e.getMessage());
        }
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        testDataExtraction();
        searchPngHeaders();
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        SimpleNpkDataTest test = new SimpleNpkDataTest();
        test.runAllTests();
    }
}
