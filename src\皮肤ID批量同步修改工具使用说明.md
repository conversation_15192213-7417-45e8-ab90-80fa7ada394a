# 皮肤ID批量同步修改工具使用说明 v2.0.0

## 概述

本工具用于批量同步修改游戏皮肤ID格式，同时处理文件夹中的PNG文件名和XLS配置文件中的皮肤ID。v2.0.0版本新增了文件管理功能，提供随机重命名和文件删除等操作。

## 新版本特性

### v2.0.0 新增功能
- **标签页界面**：将功能分为"皮肤ID批量转换"和"文件管理"两个标签页
- **文件管理功能**：支持文件扫描、多选、随机重命名、批量删除
- **多选操作**：支持复选框多选和全选功能
- **安全确认**：所有危险操作都有确认对话框

## 转换规则

### 文件名转换
- **旧格式**: 任意文件名.png（如：`weapon_sword.png`, `armor_001.png`, `9134.png`）
- **新格式**: `2025-随机8位十六进制.png`（如：`2025-A1B2C3D4.png`）

### XLS皮肤ID转换
- **旧格式**: 对应的原始值（可能是文件名、数字等）
- **新格式**: `0x随机8位十六进制`（如：`0xA1B2C3D4`）

### 转换逻辑
1. 扫描XLS文件，为每个唯一的原始ID生成对应的8位十六进制ID
2. **相同的原始ID使用相同的转换后ID**（确保一致性）
3. 文件名格式：`2025-{8位十六进制}.png`
4. XLS格式：`0x{8位十六进制}`
5. 支持任意格式的原始文件名，不限制为纯数字
6. 同时更新XLS中所有匹配的行，确保共享皮肤文件

## 工具介绍

### 1. SkinIdSyncTool - 完整版工具

**功能特点**:
- 图形化界面，操作直观
- 支持预览修改内容
- 详细的操作日志
- 进度显示
- 支持已转换文件的识别

**使用步骤**:
1. 启动工具：`java com.tool.skin.SkinIdSyncTool`
2. 配置路径：
   - 物品文件夹：`G:\JXy2o\GameClient3.0\res\item`
   - XLS文件：`res/config/item.xls`
3. 点击"扫描文件"按钮
4. 查看预览表格，确认修改内容
5. 点击"执行修改"按钮
6. 确认操作，等待完成

**界面说明**:
- **路径配置区**: 设置文件夹和XLS文件路径
- **标签页区域**:
  - **皮肤ID批量转换**: 原有的ID转换功能
  - **文件管理**: 新增的文件管理功能
- **日志区域**: 显示详细的操作信息

### 文件管理功能使用方法

**使用步骤**:
1. 设置物品文件夹路径
2. 切换到"文件管理"标签页
3. 点击"扫描文件"按钮
4. 在文件列表中选择要操作的文件（支持多选）
5. 执行相应操作：
   - **随机重命名**: 点击"随机重命名选中文件"
   - **删除文件**: 点击"删除选中文件"

**功能特点**:
- **多选操作**: 支持复选框多选和全选
- **随机重命名**: 生成`2025-随机8位十六进制.png`格式的文件名
- **安全删除**: 删除前会弹出确认对话框
- **实时更新**: 操作后立即更新文件列表
- **状态显示**: 显示文件大小、修改时间、操作状态

### 2. QuickSkinConverter - 快速转换工具

**功能特点**:
- 简化界面，快速操作
- 自动批量转换
- 实时日志显示
- 一键完成所有操作

**使用步骤**:
1. 启动工具：`java com.tool.skin.QuickSkinConverter`
2. 确认路径配置
3. 点击"开始转换"按钮
4. 等待转换完成

## 详细功能说明

### 文件扫描
- 自动扫描指定文件夹中的PNG文件
- 识别所有未转换的PNG文件（任意文件名格式）
- 识别新格式文件（2025-xxxxxxxx.png，已转换）
- 显示文件存在状态

### XLS处理
- 读取Excel文件中的皮肤ID配置
- 查找对应的皮肤ID行
- 更新皮肤ID格式
- 保存修改后的Excel文件

### 文件重命名
- 将旧格式文件名重命名为新格式
- 检查目标文件是否已存在
- 避免重复转换

### 错误处理
- 文件不存在的处理
- XLS中找不到对应ID的文件会被自动跳过
- 文件重命名失败的处理
- 详细的错误日志记录
- 跳过文件的统计和日志记录

## 转换示例

### 示例1: 武器文件转换
```
原文件名: weapon_sword.png
新文件名: 2025-A1B2C3D4.png
原皮肤ID: weapon_sword
新皮肤ID: 0xA1B2C3D4
```

### 示例2: 装备文件转换
```
原文件名: armor_001.png
新文件名: 2025-F5E6D7C8.png
原皮肤ID: armor_001
新皮肤ID: 0xF5E6D7C8
```

### 示例3: 数字文件转换
```
原文件名: 9134.png
新文件名: 2025-B9A8C7D6.png
原皮肤ID: 9134
新皮肤ID: 0xB9A8C7D6
```

### 示例4: 复杂文件名转换
```
原文件名: item_potion_health_large.png
新文件名: 2025-E4F3A2B1.png
原皮肤ID: item_potion_health_large
新皮肤ID: 0xE4F3A2B1
```

### 示例5: 相同ID的一致性转换
```
XLS中的多个条目:
多情环    6120  -> 0xDDFAFD8E
多情环    6120  -> 0xDDFAFD8E  (相同ID，相同转换结果)
多情环    6120  -> 0xDDFAFD8E  (相同ID，相同转换结果)

对应的文件:
6120.png -> 2025-DDFAFD8E.png (所有6120都共享这个皮肤文件)
```

## 注意事项

### 1. 备份重要性
- **强烈建议**在使用工具前备份以下文件：
  - 整个`res/item`文件夹
  - `res/config/item.xls`文件
- 转换操作不可逆，备份是唯一的恢复方式

### 2. 路径配置
- 确保物品文件夹路径正确
- 确保XLS文件路径正确且文件存在
- 路径中不要包含特殊字符

### 3. 文件权限
- 确保对文件夹有读写权限
- 确保XLS文件没有被其他程序占用
- 以管理员权限运行可能更安全

### 4. 转换检查
- 转换前仔细查看预览表格
- 确认文件数量和ID范围合理
- 注意已转换文件的标识

### 5. 错误处理
- 如果转换失败，查看详细日志
- 部分失败不影响其他文件的转换
- 可以多次运行工具处理失败的文件

## 故障排除

### 1. 工具无法启动
- 检查Java环境是否正确安装
- 确认classpath包含必要的依赖
- 查看控制台错误信息

### 2. 文件扫描失败
- 检查文件夹路径是否正确
- 确认文件夹存在且可访问
- 检查文件夹权限

### 3. XLS读取失败
- 确认XLS文件存在
- 检查文件是否被其他程序占用
- 验证文件格式是否正确

### 4. 文件重命名失败
- 检查文件权限
- 确认目标文件名不存在
- 检查磁盘空间是否充足

### 5. XLS保存失败
- 确认文件没有被Excel等程序打开
- 检查磁盘空间
- 验证文件权限

## 技术细节

### 数据结构
```java
public class SkinModifyRecord {
    private String oldFileName;      // 原文件名
    private String newFileName;      // 新文件名
    private String oldSkinId;        // 原皮肤ID
    private String newSkinId;        // 新皮肤ID
    private boolean fileExists;     // 文件是否存在
    private int xlsRowIndex;         // XLS中的行号
    private boolean alreadyConverted; // 是否已转换
}
```

### 转换算法
```java
// 生成随机8位十六进制ID
private String generateRandomHexId() {
    long randomValue = random.nextLong() & 0xFFFFFFFFL; // 确保是正数且在32位范围内
    // 确保生成的十六进制至少以5-9或A-F开头（避免与小数值冲突）
    if (randomValue < 0x50000000L) {
        randomValue += 0x50000000L;
    }
    return String.format("%08X", randomValue);
}

// 应用转换
String randomHexId = generateRandomHexId();
String newFileName = "2025-" + randomHexId + ".png";
String newSkinId = "0x" + randomHexId;
```

### 文件操作
```java
// 文件重命名
Path oldFilePath = Paths.get(itemFolder, oldFileName);
Path newFilePath = Paths.get(itemFolder, newFileName);
Files.move(oldFilePath, newFilePath);
```

## 版本历史

- **v1.0.0**: 基础转换功能
- **v1.1.0**: 添加预览功能
- **v1.2.0**: 增加快速转换工具
- **v1.3.0**: 优化错误处理和日志

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。

## 许可证

本工具仅供内部使用，请勿外传。
