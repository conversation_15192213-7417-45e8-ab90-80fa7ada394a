package com.tool.test;

import com.tool.wdf.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * WDF性能专项测试
 * 模拟真实场景：1005个文件的打包性能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class WdfPerformanceTest {
    
    private static final String TEST_FOLDER = "wdf_performance_test";
    private static final String TEST_WDF = "performance_test.wdf";
    
    /**
     * 创建1005个测试文件模拟真实场景
     */
    public void createRealScaleTestFiles() {
        System.out.println("=== 创建1005个测试文件模拟真实场景 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                // 清理旧的测试文件
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
            
            Files.createDirectories(testDir);
            System.out.println("创建测试目录: " + TEST_FOLDER);
            
            // 创建1005个文件，模拟真实的游戏资源
            int fileCount = 1005;
            System.out.println("正在创建 " + fileCount + " 个测试文件...");
            
            long totalSize = 0;
            for (int i = 0; i < fileCount; i++) {
                String hexId = String.format("%08X", i + 0x10000000);
                String fileName = "2025-" + hexId + (i % 3 == 0 ? ".png" : ".was");
                Path filePath = testDir.resolve(fileName);
                
                // 创建不同大小的文件内容，模拟真实的图像/动画文件
                StringBuilder content = new StringBuilder();
                content.append("Game Resource File: ").append(fileName).append("\n");
                content.append("Resource ID: 0x").append(hexId).append("\n");
                content.append("Resource Type: ").append(i % 3 == 0 ? "PNG Texture" : "WAS Animation").append("\n");
                content.append("Created for performance testing\n");
                
                // 模拟真实文件大小：1KB到10KB
                int contentSize = 1000 + (i % 9000); // 1KB到10KB的文件
                for (int j = 0; j < contentSize / 100; j++) {
                    content.append("Resource data line ").append(j).append(" for ").append(fileName).append("\n");
                }
                
                byte[] fileData = content.toString().getBytes("UTF-8");
                Files.write(filePath, fileData);
                totalSize += fileData.length;
                
                // 每100个文件显示一次进度
                if ((i + 1) % 100 == 0) {
                    System.out.println("已创建 " + (i + 1) + " 个文件...");
                }
            }
            
            System.out.println("测试文件创建完成");
            System.out.println("文件数量: " + fileCount);
            System.out.println("总大小: " + DataTool.formatFileSize(totalSize));
            
        } catch (IOException e) {
            System.out.println("创建测试文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试优化后的WDF打包性能
     */
    public void testOptimizedWdfPerformance() {
        System.out.println("\n=== 测试优化后的WDF打包性能 ===");
        
        Path testDir = Paths.get(TEST_FOLDER);
        if (!Files.exists(testDir)) {
            System.out.println("测试目录不存在，请先创建测试文件");
            return;
        }
        
        try {
            File sourceDir = testDir.toFile();
            File targetFile = new File(TEST_WDF);
            
            System.out.println("开始性能测试...");
            System.out.println("源目录: " + sourceDir.getAbsolutePath());
            System.out.println("目标文件: " + targetFile.getAbsolutePath());
            
            long totalStartTime = System.currentTimeMillis();
            
            // 阶段1: 文件收集
            System.out.println("\n--- 阶段1: 文件收集 ---");
            long collectStartTime = System.currentTimeMillis();
            
            List<WasData> wasDataList = collectWdfFiles(sourceDir);
            
            long collectEndTime = System.currentTimeMillis();
            long collectTime = collectEndTime - collectStartTime;
            
            System.out.println("文件收集完成");
            System.out.println("收集耗时: " + collectTime + "ms");
            System.out.println("找到文件: " + wasDataList.size() + " 个");
            System.out.println("平均每文件: " + (collectTime / (double)wasDataList.size()) + "ms");
            
            if (wasDataList.isEmpty()) {
                System.out.println("没有找到符合条件的文件");
                return;
            }
            
            // 阶段2: WDF打包
            System.out.println("\n--- 阶段2: WDF打包 ---");
            long packStartTime = System.currentTimeMillis();
            
            WdfTool.packWdfFile(sourceDir, targetFile, wasDataList, (progress, status) -> {
                if (progress % 100 == 0 || progress == wasDataList.size()) {
                    System.out.println("打包进度: " + progress + "/" + wasDataList.size() + " - " + status);
                }
            });
            
            long packEndTime = System.currentTimeMillis();
            long packTime = packEndTime - packStartTime;
            long totalTime = packEndTime - totalStartTime;
            
            System.out.println("\n=== 性能测试结果 ===");
            System.out.println("文件收集耗时: " + collectTime + "ms");
            System.out.println("WDF打包耗时: " + packTime + "ms");
            System.out.println("总耗时: " + totalTime + "ms (" + (totalTime / 1000.0) + "秒)");
            System.out.println("文件数量: " + wasDataList.size());
            System.out.println("输出文件大小: " + DataTool.formatFileSize(targetFile.length()));
            System.out.println("平均每文件耗时: " + (totalTime / (double)wasDataList.size()) + "ms");
            System.out.println("处理速度: " + (wasDataList.size() * 1000.0 / totalTime) + " 文件/秒");
            
            // 性能评估
            System.out.println("\n=== 性能评估 ===");
            if (totalTime < 30000) { // 30秒
                System.out.println("性能评级: ⭐⭐⭐⭐⭐ 优秀 (< 30秒)");
            } else if (totalTime < 60000) { // 1分钟
                System.out.println("性能评级: ⭐⭐⭐⭐ 良好 (< 1分钟)");
            } else if (totalTime < 90000) { // 1.5分钟
                System.out.println("性能评级: ⭐⭐⭐ 一般 (< 1.5分钟)");
            } else {
                System.out.println("性能评级: ⭐⭐ 需要优化 (> 1.5分钟)");
            }
            
            // 与原始性能对比
            long originalTime = 108000; // 1分48秒 = 108秒
            double improvement = ((double)(originalTime - totalTime) / originalTime) * 100;
            System.out.println("相比原始版本(1分48秒)性能提升: " + String.format("%.1f%%", improvement));
            
        } catch (Exception e) {
            System.out.println("WDF性能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 收集WDF文件
     */
    private List<WasData> collectWdfFiles(File sourceDir) {
        List<WasData> wasDataList = new ArrayList<>();
        collectWdfFilesRecursive(sourceDir, wasDataList);
        
        // 按ID排序
        wasDataList.sort((a, b) -> Long.compare(a.getId(), b.getId()));
        
        return wasDataList;
    }
    
    /**
     * 递归收集WDF文件
     */
    private void collectWdfFilesRecursive(File dir, List<WasData> wasDataList) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    collectWdfFilesRecursive(file, wasDataList);
                } else {
                    String name = file.getName();
                    if (name.startsWith("2025-") && (name.endsWith(".png") || name.endsWith(".was"))) {
                        try {
                            String hexPart = name.substring(5, name.lastIndexOf('.'));
                            if (hexPart.length() == 8) {
                                long id = Long.parseLong(hexPart, 16);
                                
                                WasData wasData = new WasData();
                                wasData.setId(id);
                                wasData.setFileSize(file.length());
                                wasData.setFileSpace(file.length());
                                
                                wasDataList.add(wasData);
                            }
                        } catch (NumberFormatException e) {
                            // 忽略无效文件名
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        try {
            // 删除测试目录
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
                System.out.println("删除测试目录: " + TEST_FOLDER);
            }
            
            // 删除WDF文件
            File wdfFile = new File(TEST_WDF);
            if (wdfFile.exists()) {
                if (wdfFile.delete()) {
                    System.out.println("删除: " + TEST_WDF);
                }
            }
            
            System.out.println("测试文件清理完成");
            
        } catch (IOException e) {
            System.out.println("清理测试文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行完整的性能测试
     */
    public void runFullPerformanceTest() {
        System.out.println("=== WDF性能专项测试 ===");
        System.out.println("目标: 优化1005个文件的打包速度");
        System.out.println("原始性能: 1分48秒");
        System.out.println("优化目标: < 1分钟");
        
        createRealScaleTestFiles();
        testOptimizedWdfPerformance();
        
        System.out.println("\n=== 性能测试完成 ===");
        System.out.println("如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        WdfPerformanceTest test = new WdfPerformanceTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runFullPerformanceTest();
        }
    }
}
