package com.tool.test;

import com.tool.npk.*;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.util.List;

/**
 * NPK排序功能测试工具
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkSortTest extends JFrame {
    
    private JList<NpkEntry> testList;
    private DefaultListModel<NpkEntry> testModel;
    private JComboBox<String> sortCombo;
    private JLabel statusLabel;
    private NpkFile currentNpkFile;
    
    public NpkSortTest() {
        initializeUI();
        loadTestData();
    }
    
    private void initializeUI() {
        setTitle("NPK排序功能测试");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // 创建控制面板
        JPanel controlPanel = new JPanel(new FlowLayout());
        
        controlPanel.add(new JLabel("排序方式:"));
        sortCombo = new JComboBox<>(NpkSortHelper.getSupportedSortTypes());
        controlPanel.add(sortCombo);
        
        JButton sortBtn = new JButton("应用排序");
        JButton statisticsBtn = new JButton("统计信息");
        JButton resetBtn = new JButton("重置");
        
        controlPanel.add(sortBtn);
        controlPanel.add(statisticsBtn);
        controlPanel.add(resetBtn);
        
        add(controlPanel, BorderLayout.NORTH);
        
        // 创建列表
        testModel = new DefaultListModel<>();
        testList = new JList<>(testModel);
        testList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        testList.setCellRenderer(new com.tool.skin.NpkEntryListCellRenderer());
        
        JScrollPane scrollPane = new JScrollPane(testList);
        scrollPane.setPreferredSize(new Dimension(700, 500));
        add(scrollPane, BorderLayout.CENTER);
        
        // 状态栏
        statusLabel = new JLabel("就绪");
        add(statusLabel, BorderLayout.SOUTH);
        
        // 事件处理
        sortBtn.addActionListener(e -> testSort());
        statisticsBtn.addActionListener(e -> showStatistics());
        resetBtn.addActionListener(e -> resetList());
        
        // 排序选择变化时自动应用
        sortCombo.addActionListener(e -> testSort());
        
        pack();
        setLocationRelativeTo(null);
    }
    
    private void loadTestData() {
        statusLabel.setText("正在加载测试数据...");
        
        SwingWorker<NpkFile, Void> worker = new SwingWorker<NpkFile, Void>() {
            @Override
            protected NpkFile doInBackground() throws Exception {
                File npkFile = new File("item.npk");
                if (!npkFile.exists()) {
                    throw new RuntimeException("item.npk文件不存在");
                }
                
                return NpkTool.parseNpkFile(npkFile, null);
            }
            
            @Override
            protected void done() {
                try {
                    currentNpkFile = get();
                    resetList();
                    statusLabel.setText("测试数据加载完成");
                    
                } catch (Exception e) {
                    statusLabel.setText("加载失败: " + e.getMessage());
                    JOptionPane.showMessageDialog(NpkSortTest.this, 
                        "加载测试数据失败:\n" + e.getMessage(), 
                        "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };
        
        worker.execute();
    }
    
    private void resetList() {
        if (currentNpkFile == null) {
            return;
        }
        
        testModel.clear();
        
        // 加载前50个条目用于测试
        List<NpkEntry> entries = currentNpkFile.getEntries();
        int loadCount = Math.min(50, entries.size());
        
        for (int i = 0; i < loadCount; i++) {
            testModel.addElement(entries.get(i));
        }
        
        statusLabel.setText("显示 " + loadCount + " 个条目 (默认顺序)");
        sortCombo.setSelectedIndex(0); // 重置为默认顺序
    }
    
    private void testSort() {
        String sortType = (String) sortCombo.getSelectedItem();
        if (sortType == null || currentNpkFile == null) {
            return;
        }
        
        statusLabel.setText("正在排序: " + sortType);
        
        SwingWorker<List<NpkEntry>, Void> worker = new SwingWorker<List<NpkEntry>, Void>() {
            @Override
            protected List<NpkEntry> doInBackground() throws Exception {
                // 获取当前列表中的条目
                java.util.List<NpkEntry> currentEntries = new java.util.ArrayList<>();
                for (int i = 0; i < testModel.getSize(); i++) {
                    currentEntries.add(testModel.getElementAt(i));
                }
                
                // 执行排序
                return NpkSortHelper.sortEntries(currentEntries, sortType);
            }
            
            @Override
            protected void done() {
                try {
                    List<NpkEntry> sortedEntries = get();
                    
                    // 更新列表
                    testModel.clear();
                    for (NpkEntry entry : sortedEntries) {
                        testModel.addElement(entry);
                    }
                    
                    statusLabel.setText("排序完成: " + sortedEntries.size() + " 个条目 (按" + sortType + ")");
                    
                    // 显示排序效果
                    if (sortedEntries.size() > 0) {
                        testList.setSelectedIndex(0);
                        testList.ensureIndexIsVisible(0);
                    }
                    
                } catch (Exception e) {
                    statusLabel.setText("排序失败: " + e.getMessage());
                }
            }
        };
        
        worker.execute();
    }
    
    private void showStatistics() {
        if (currentNpkFile == null) {
            JOptionPane.showMessageDialog(this, "请先加载NPK文件!", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }
        
        // 获取当前显示的条目
        java.util.List<NpkEntry> currentEntries = new java.util.ArrayList<>();
        for (int i = 0; i < testModel.getSize(); i++) {
            currentEntries.add(testModel.getElementAt(i));
        }
        
        // 生成统计信息
        String statistics = NpkSortHelper.getSortStatistics(currentEntries);
        
        // 显示统计信息对话框
        JTextArea textArea = new JTextArea(statistics);
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(350, 250));
        
        JOptionPane.showMessageDialog(this, scrollPane, "文件统计信息", JOptionPane.INFORMATION_MESSAGE);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new NpkSortTest().setVisible(true);
        });
    }
}
