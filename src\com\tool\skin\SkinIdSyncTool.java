package com.tool.skin;

import com.tool.config.PathConfig;
import com.tool.npk.*;
import xsl.ExcelUtil;
import xsl.ReadExelTool;

import javax.swing.*;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableRowSorter;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import javax.swing.tree.DefaultMutableTreeNode;
import javax.swing.tree.DefaultTreeModel;
import javax.swing.tree.TreePath;
import javax.swing.event.TreeSelectionListener;
import javax.swing.event.TreeSelectionEvent;
import com.tool.wdf.WdfPacker;
import com.tool.wdf.WdfTool;
import com.tool.wdf.WasData;
import com.tool.wdf.WdfHead;
import com.tool.wdf.DataTool;

import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

/**
 * 皮肤ID批量同步修改工具
 * 同时修改文件夹中的PNG文件名和XLS文件中对应的皮肤ID
 *
 * 规则：
 * - 文件名：任意文件名.png -> 2025-随机8位十六进制.png
 * - XLS第3列：对应值 -> 0x随机8位十六进制
 *
 * 示例：
 * - 文件名：weapon_sword.png -> 2025-A1B2C3D4.png
 * - XLS第3列：weapon_sword -> 0xA1B2C3D4
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SkinIdSyncTool extends JFrame {

    // 路径配置管理器
    private static final PathConfig pathConfig = PathConfig.getInstance();

    // UI组件 - 主界面
    private JTabbedPane tabbedPane;
    private JTextField itemFolderField;
    private JTextField xlsPathField;
    private JTextArea logArea;

    // UI组件 - 皮肤ID转换标签页
    private JTable previewTable;
    private DefaultTableModel tableModel;
    private JButton scanButton;
    private JButton executeButton;
    private JProgressBar progressBar;

    // UI组件 - 文件管理标签页
    private JTable fileManageTable;
    private DefaultTableModel fileManageTableModel;
    private JButton scanFilesButton;
    private JButton renameSelectedButton;
    private JButton deleteSelectedButton;
    private JCheckBox selectAllCheckBox;

    // UI组件 - WAS解包标签页
    private JTextField wasSourceFolderField;
    private JTextField wasOutputFolderField;
    private JCheckBox extractStaticCheckBox;
    private JCheckBox extractAnimatedCheckBox;
    private JCheckBox createSubfoldersCheckBox;
    private JButton startWasExtractButton;
    private JTextArea wasLogArea;

    // 数据存储
    private List<SkinModifyRecord> modifyRecords;
    private String[][] xlsData;
    private int skinColumnIndex = -1; // 皮肤列的索引位置
    private Random random;
    private Map<String, String> idMappingCache; // 缓存原始ID到新ID的映射

    // 主题和UI相关
    private JLabel imagePreviewLabel;
    private JLabel imageInfoLabel;

    /**
     * 皮肤修改记录
     */
    public static class SkinModifyRecord {
        private String oldFileName;
        private String newFileName;
        private String oldSkinId;
        private String newSkinId;
        private boolean fileExists;
        private int xlsRowIndex;
        private boolean alreadyConverted;

        public SkinModifyRecord(String oldFileName, String newFileName, String oldSkinId, String newSkinId) {
            this.oldFileName = oldFileName;
            this.newFileName = newFileName;
            this.oldSkinId = oldSkinId;
            this.newSkinId = newSkinId;
            this.fileExists = false;
            this.xlsRowIndex = -1;
            this.alreadyConverted = false;
        }

        // Getters and Setters
        public String getOldFileName() { return oldFileName; }
        public String getNewFileName() { return newFileName; }
        public String getOldSkinId() { return oldSkinId; }
        public String getNewSkinId() { return newSkinId; }
        public boolean isFileExists() { return fileExists; }
        public void setFileExists(boolean fileExists) { this.fileExists = fileExists; }
        public int getXlsRowIndex() { return xlsRowIndex; }
        public void setXlsRowIndex(int xlsRowIndex) { this.xlsRowIndex = xlsRowIndex; }
        public boolean isAlreadyConverted() { return alreadyConverted; }
        public void setAlreadyConverted(boolean alreadyConverted) { this.alreadyConverted = alreadyConverted; }
    }

    public SkinIdSyncTool() {
        initializeUI();
        initializeData();
    }

    /**
     * 初始化UI界面工具 作者
     */
    private void initializeUI() {
        setTitle("NpkPackTool_v3.0.3 作者：Simu 联系方式：289557289  本程序只限于个人学习研究，如有侵犯请及时联系删除");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 尝试设置应用程序图标，如果资源不存在则跳过
        try {
            java.net.URL iconUrl = SkinIdSyncTool.class.getResource("/resource/app-icon.png");
            if (iconUrl == null) {
                iconUrl = SkinIdSyncTool.class.getResource("resource/app-icon.png");
            }
            if (iconUrl != null) {
                ImageIcon icon = new ImageIcon(iconUrl);
                setIconImage(icon.getImage());
            }
        } catch (Exception e) {
            // 如果图标加载失败，继续运行程序，只是没有自定义图标
            System.out.println("警告: 无法加载应用程序图标 - " + e.getMessage());
        }
        // 创建菜单栏
        createMenuBar();

        // 创建顶部配置面板
        JPanel configPanel = createConfigPanel();
        add(configPanel, BorderLayout.NORTH);

        // 创建标签页面板
        tabbedPane = createTabbedPane();
        add(tabbedPane, BorderLayout.CENTER);

        // 创建底部日志面板
        JPanel logPanel = createLogPanel();
        add(logPanel, BorderLayout.SOUTH);

        setSize(1200, 800);
        setLocationRelativeTo(null);
    }

    /**
     * 创建菜单栏
     */
    private void createMenuBar() {
        JMenuBar menuBar = new JMenuBar();

        // 文件菜单
        JMenu fileMenu = new JMenu("文件");
        JMenuItem exitItem = new JMenuItem("退出");
        exitItem.addActionListener(e -> System.exit(0));
        fileMenu.add(exitItem);

        // 视图菜单
        JMenu viewMenu = new JMenu("视图");
        JMenu themeMenu = new JMenu("主题");

        // 主题选项已移除 - 使用系统默认主题

        viewMenu.add(themeMenu);

        // 帮助菜单
        JMenu helpMenu = new JMenu("帮助");
        JMenuItem aboutItem = new JMenuItem("关于");
        aboutItem.addActionListener(e -> showAboutDialog());
        helpMenu.add(aboutItem);

        menuBar.add(fileMenu);
        menuBar.add(viewMenu);
        menuBar.add(helpMenu);
        setJMenuBar(menuBar);
    }

    /**
     * 创建配置面板
     */
    private JPanel createConfigPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new TitledBorder("路径配置"));
        GridBagConstraints gbc = new GridBagConstraints();

        // 物品文件夹路径
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("物品文件夹:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        itemFolderField = new JTextField(pathConfig.getItemFolder());
        panel.add(itemFolderField, gbc);

        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseItemButton = new JButton("浏览");
        browseItemButton.addActionListener(e -> browseFolder(itemFolderField));
        panel.add(browseItemButton, gbc);

        // XLS文件路径
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("XLS文件:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        xlsPathField = new JTextField(pathConfig.getXlsPath());
        panel.add(xlsPathField, gbc);

        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseXlsButton = new JButton("浏览");
        browseXlsButton.addActionListener(e -> browseFile(xlsPathField));
        panel.add(browseXlsButton, gbc);


        return panel;
    }

    /**
     * 创建预览面板
     */
    private JPanel createPreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("修改预览"));

        // 创建表格
        String[] columnNames = {"原文件名", "新文件名", "原皮肤ID", "新皮肤ID", "文件存在", "XLS行号", "状态"};
        tableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false; // 表格不可编辑
            }
        };
        previewTable = new JTable(tableModel);
        previewTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        JScrollPane tableScrollPane = new JScrollPane(previewTable);
        tableScrollPane.setPreferredSize(new Dimension(800, 300));

        panel.add(tableScrollPane, BorderLayout.CENTER);

        return panel;
    }



    /**
     * 创建标签页面板
     */
    private JTabbedPane createTabbedPane() {
        JTabbedPane tabbedPane = new JTabbedPane();

        // 皮肤ID转换标签页
        JPanel skinIdPanel = createSkinIdConversionPanel();
        tabbedPane.addTab("皮肤ID批量转换", skinIdPanel);

        // 文件管理标签页
        JPanel fileManagePanel = createFileManagementPanel();
        tabbedPane.addTab("文件管理", fileManagePanel);

        // WAS解包标签页
        JPanel wasExtractPanel = createWasExtractPanel();
        tabbedPane.addTab("WAS解包", wasExtractPanel);

        // WDF处理标签页
        JPanel wdfPanel = createWdfProcessPanel();
        tabbedPane.addTab("WDF处理", wdfPanel);

        // NPK处理标签页
        JPanel npkPanel = createNpkProcessPanel();
        tabbedPane.addTab("NPK处理", npkPanel);

        // NPK打包标签页
        JPanel npkPackPanel = createNpkPackPanel();
        tabbedPane.addTab("NPK打包", npkPackPanel);

        // NPK查看标签页
        JPanel npkViewPanel = createNpkViewPanel();
        tabbedPane.addTab("NPK查看", npkViewPanel);

        // 文件改名标签页
        JPanel fileRenamePanel = createFileRenamePanel();
        tabbedPane.addTab("文件改名", fileRenamePanel);

        return tabbedPane;
    }

    /**
     * 创建皮肤ID转换面板
     */
    private JPanel createSkinIdConversionPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 预览面板
        JPanel previewPanel = createPreviewPanel();
        panel.add(previewPanel, BorderLayout.CENTER);

        // 操作按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        scanButton = new JButton("扫描文件");
        scanButton.addActionListener(e -> performScanInBackground());
        buttonPanel.add(scanButton);

        JButton clearButton = new JButton("清空预览");
        clearButton.addActionListener(e -> clearPreview());
        buttonPanel.add(clearButton);

        executeButton = new JButton("执行修改");
        executeButton.setEnabled(false);
        executeButton.addActionListener(e -> performModificationInBackground());
        buttonPanel.add(executeButton);

        // 进度条
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("就绪");

        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.add(buttonPanel, BorderLayout.NORTH);
        bottomPanel.add(progressBar, BorderLayout.CENTER);

        panel.add(bottomPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建文件管理面板
     */
    private JPanel createFileManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 文件列表表格
        String[] columnNames = {"选择", "文件名", "XLS需要的ID", "文件大小", "修改时间", "状态"};
        fileManageTableModel = new DefaultTableModel(columnNames, 0) {
            @Override
            public Class<?> getColumnClass(int columnIndex) {
                if (columnIndex == 0) {
                    return Boolean.class; // 复选框列
                }
                return String.class;
            }

            @Override
            public boolean isCellEditable(int row, int column) {
                return column == 0; // 只有复选框列可编辑
            }
        };

        fileManageTable = new JTable(fileManageTableModel);
        fileManageTable.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);
        fileManageTable.getColumnModel().getColumn(0).setMaxWidth(50);   // 选择
        fileManageTable.getColumnModel().getColumn(2).setMaxWidth(120);  // XLS需要的ID
        fileManageTable.getColumnModel().getColumn(3).setMaxWidth(100);  // 文件大小
        fileManageTable.getColumnModel().getColumn(4).setMaxWidth(150);  // 修改时间
        fileManageTable.getColumnModel().getColumn(5).setMaxWidth(100);  // 状态

        // 添加表格排序功能
        TableRowSorter<DefaultTableModel> sorter = new TableRowSorter<>(fileManageTableModel);
        fileManageTable.setRowSorter(sorter);

        // 自定义排序器
        sorter.setComparator(3, new FileSizeComparator()); // 文件大小列
        sorter.setComparator(4, new DateTimeComparator()); // 修改时间列

        // 添加表格数据变化监听器
        fileManageTableModel.addTableModelListener(e -> updateFileManageButtons());

        // 添加右键复制和单击预览功能
        fileManageTable.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseClicked(java.awt.event.MouseEvent e) {
                int row = fileManageTable.getSelectedRow();
                if (row >= 0) {
                    String fileName = (String) fileManageTableModel.getValueAt(row, 1);
                    // 单击显示图像预览
                    showImageInPanel(fileName);
                }

                // 右键显示复制菜单
                if (e.getButton() == java.awt.event.MouseEvent.BUTTON3) {
                    int row2 = fileManageTable.rowAtPoint(e.getPoint());
                    if (row2 >= 0) {
                        fileManageTable.setRowSelectionInterval(row2, row2);
                        showCopyMenu(e, row2);
                    }
                }
            }
        });

        JScrollPane scrollPane = new JScrollPane(fileManageTable);
        scrollPane.setBorder(new TitledBorder("文件列表 (支持PNG/WAS文件，单击预览图像，右键复制ID)"));

        // 创建分割面板，左侧文件列表，右侧图像预览
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setLeftComponent(scrollPane);

        // 图像预览面板
        JPanel imagePreviewPanel = createImagePreviewPanel();
        splitPane.setRightComponent(imagePreviewPanel);
        splitPane.setDividerLocation(600);
        splitPane.setResizeWeight(0.7);

        panel.add(splitPane, BorderLayout.CENTER);

        // 操作按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        scanFilesButton = new JButton("扫描文件");
        scanFilesButton.addActionListener(e -> scanFilesForManagement());
        buttonPanel.add(scanFilesButton);

        selectAllCheckBox = new JCheckBox("全选");
        selectAllCheckBox.addActionListener(e -> toggleSelectAll());
        buttonPanel.add(selectAllCheckBox);

        JButton copyAllXlsIdsButton = new JButton("全选名称复制");
        copyAllXlsIdsButton.addActionListener(e -> copyAllXlsIds());
        buttonPanel.add(copyAllXlsIdsButton);

        // 重命名前缀选择
        buttonPanel.add(new JLabel("前缀:"));
        JComboBox<String> prefixComboBox = new JComboBox<>(new String[]{"2025-", "0x"});
        buttonPanel.add(prefixComboBox);

        renameSelectedButton = new JButton("随机重命名选中文件");
        renameSelectedButton.setEnabled(false);
        renameSelectedButton.addActionListener(e -> renameSelectedFiles(prefixComboBox));
        buttonPanel.add(renameSelectedButton);

        deleteSelectedButton = new JButton("删除选中文件");
        deleteSelectedButton.setEnabled(false);
        deleteSelectedButton.addActionListener(e -> deleteSelectedFiles());
        buttonPanel.add(deleteSelectedButton);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建WAS解包面板
     */
    private JPanel createWasExtractPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("WAS文件解包"));

        // 输入区域
        JPanel inputPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // WAS文件夹选择
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        inputPanel.add(new JLabel("WAS文件夹:"), gbc);

        wasSourceFolderField = new JTextField(30);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(wasSourceFolderField, gbc);

        JButton browseWasSourceButton = new JButton("浏览");
        browseWasSourceButton.addActionListener(e -> browseWasSourceFolder());
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        inputPanel.add(browseWasSourceButton, gbc);

        // 输出文件夹选择
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        inputPanel.add(new JLabel("输出文件夹:"), gbc);

        wasOutputFolderField = new JTextField(30);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(wasOutputFolderField, gbc);

        JButton browseWasOutputButton = new JButton("浏览");
        browseWasOutputButton.addActionListener(e -> browseWasOutputFolder());
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        inputPanel.add(browseWasOutputButton, gbc);

        // 选项区域
        JPanel optionsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));

        extractStaticCheckBox = new JCheckBox("提取静态图像", true);
        optionsPanel.add(extractStaticCheckBox);

        extractAnimatedCheckBox = new JCheckBox("提取动态图像", true);
        optionsPanel.add(extractAnimatedCheckBox);

        createSubfoldersCheckBox = new JCheckBox("为每个WAS文件创建子文件夹", false);
        optionsPanel.add(createSubfoldersCheckBox);

        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3;
        inputPanel.add(optionsPanel, gbc);

        // 操作按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());

        startWasExtractButton = new JButton("开始解包");
        startWasExtractButton.addActionListener(e -> startWasExtraction());
        buttonPanel.add(startWasExtractButton);

        JButton analyzeWasButton = new JButton("分析WAS文件");
        analyzeWasButton.addActionListener(e -> analyzeWasFile());
        buttonPanel.add(analyzeWasButton);

        JButton searchTgaButton = new JButton("搜索TGA数据");
        searchTgaButton.addActionListener(e -> searchTgaInWas());
        buttonPanel.add(searchTgaButton);

        JButton clearWasLogButton = new JButton("清空日志");
        clearWasLogButton.addActionListener(e -> wasLogArea.setText(""));
        buttonPanel.add(clearWasLogButton);

        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 3;
        inputPanel.add(buttonPanel, gbc);

        panel.add(inputPanel, BorderLayout.NORTH);

        // 日志区域
        wasLogArea = new JTextArea(15, 50);
        wasLogArea.setEditable(false);
        wasLogArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane wasLogScrollPane = new JScrollPane(wasLogArea);
        wasLogScrollPane.setBorder(new TitledBorder("解包日志"));
        panel.add(wasLogScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建日志面板
     */
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        logArea = new JTextArea(6, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setBorder(new TitledBorder("操作日志"));
        panel.add(logScrollPane, BorderLayout.CENTER);

        return panel;
    }



    /**
     * 创建图像预览面板
     */
    private JPanel createImagePreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(new TitledBorder("图像预览"));
        panel.setPreferredSize(new Dimension(200, 400));

        // 图像显示标签
        imagePreviewLabel = new JLabel("选择文件查看预览", SwingConstants.CENTER);
        imagePreviewLabel.setPreferredSize(new Dimension(180, 180));
        imagePreviewLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        panel.add(imagePreviewLabel, BorderLayout.CENTER);

        // 图像信息标签
        imageInfoLabel = new JLabel("<html><center>文件信息将在此显示</center></html>", SwingConstants.CENTER);
        imageInfoLabel.setBorder(BorderFactory.createEmptyBorder(10, 5, 10, 5));
        panel.add(imageInfoLabel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建WDF处理面板
     */
    private JPanel createWdfProcessPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 配置面板
        JPanel configPanel = new JPanel(new GridBagLayout());
        configPanel.setBorder(new TitledBorder("WDF打包配置"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // 源文件夹路径
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        configPanel.add(new JLabel("源文件夹:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField sourceFolderField = new JTextField(pathConfig.getWdfSource());
        configPanel.add(sourceFolderField, gbc);

        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseSourceButton = new JButton("浏览");
        browseSourceButton.addActionListener(e -> browseFolder(sourceFolderField));
        configPanel.add(browseSourceButton, gbc);

        // 输出WDF文件路径 - 默认路径
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        configPanel.add(new JLabel("输出WDF:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField outputWdfField = new JTextField(pathConfig.getWdfOutput());
        configPanel.add(outputWdfField, gbc);

        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputButton = new JButton("浏览");
        browseOutputButton.addActionListener(e -> browseWdfFile(outputWdfField));
        configPanel.add(browseOutputButton, gbc);

        panel.add(configPanel, BorderLayout.NORTH);

        // 中间信息显示面板
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setBorder(new TitledBorder("打包信息"));

        JTextArea wdfInfoArea = new JTextArea(10, 50);
        wdfInfoArea.setEditable(false);
        wdfInfoArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane infoScrollPane = new JScrollPane(wdfInfoArea);
        infoPanel.add(infoScrollPane, BorderLayout.CENTER);

        panel.add(infoPanel, BorderLayout.CENTER);

        // 操作按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton packButton = new JButton("开始打包");
        packButton.addActionListener(e -> {
            String sourceFolder = sourceFolderField.getText().trim();
            String outputWdf = outputWdfField.getText().trim();

            // 在后台线程中执行打包操作
            SwingUtilities.invokeLater(() -> wdfInfoArea.setText(""));
            new Thread(() -> packToWdf(sourceFolder, outputWdf, wdfInfoArea)).start();
        });
        buttonPanel.add(packButton);

        JButton analyzeButton = new JButton("分析WDF文件");
        analyzeButton.addActionListener(e -> analyzeWdfFile(outputWdfField.getText(), wdfInfoArea));
        buttonPanel.add(analyzeButton);

        JButton clearButton = new JButton("清空信息");
        clearButton.addActionListener(e -> wdfInfoArea.setText(""));
        buttonPanel.add(clearButton);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建NPK处理面板
     */
    private JPanel createNpkProcessPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 配置面板
        JPanel configPanel = new JPanel(new GridBagLayout());
        configPanel.setBorder(new TitledBorder("NPK文件处理配置"));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // NPK文件路径
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        configPanel.add(new JLabel("NPK文件:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField npkFileField = new JTextField(pathConfig.getNpkFile()); // 显示默认NPK文件目录
        configPanel.add(npkFileField, gbc);

        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseNpkButton = new JButton("浏览");
        browseNpkButton.addActionListener(e -> browseNpkFile(npkFileField));
        configPanel.add(browseNpkButton, gbc);

        // 输出目录
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        configPanel.add(new JLabel("输出目录:"), gbc);

        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField outputDirField = new JTextField(pathConfig.getNpkOutput());
        configPanel.add(outputDirField, gbc);

        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputButton = new JButton("浏览");
        browseOutputButton.addActionListener(e -> browseFolder(outputDirField));
        configPanel.add(browseOutputButton, gbc);

        panel.add(configPanel, BorderLayout.NORTH);

        // 中间信息显示面板
        JPanel infoPanel = new JPanel(new BorderLayout());
        infoPanel.setBorder(new TitledBorder("NPK文件信息"));

        JTextArea npkInfoArea = new JTextArea(15, 60);
        npkInfoArea.setEditable(false);
        npkInfoArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane infoScrollPane = new JScrollPane(npkInfoArea);
        infoPanel.add(infoScrollPane, BorderLayout.CENTER);

        panel.add(infoPanel, BorderLayout.CENTER);

        // 操作按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton analyzeButton = new JButton("分析NPK文件");
        analyzeButton.addActionListener(e -> analyzeNpkFile(npkFileField.getText(), npkInfoArea));
        buttonPanel.add(analyzeButton);

        JButton extractPngButton = new JButton("提取PNG文件");
        extractPngButton.addActionListener(e -> extractPngFiles(npkFileField.getText(), outputDirField.getText(), npkInfoArea));
        buttonPanel.add(extractPngButton);

        JButton clearButton = new JButton("清空信息");
        clearButton.addActionListener(e -> npkInfoArea.setText(""));
        buttonPanel.add(clearButton);

        panel.add(buttonPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建NPK打包面板
     */
    private JPanel createNpkPackPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("NPK文件打包"));

        // 创建输入区域
        JPanel inputPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // 源文件夹选择
        gbc.gridx = 0; gbc.gridy = 0;
        inputPanel.add(new JLabel("源文件夹:"), gbc);

        JTextField sourceFolderField = new JTextField(pathConfig.getNpkSource(), 30);
        gbc.gridx = 1; gbc.gridy = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(sourceFolderField, gbc);

        JButton browseSourceBtn = new JButton("浏览");
        gbc.gridx = 2; gbc.gridy = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        inputPanel.add(browseSourceBtn, gbc);

        // 输出NPK文件选择
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0;
        inputPanel.add(new JLabel("输出NPK:"), gbc);

        JTextField outputNpkField = new JTextField(pathConfig.getNpkOutput(), 30);
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        inputPanel.add(outputNpkField, gbc);

        JButton browseOutputBtn = new JButton("浏览");
        gbc.gridx = 2; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        inputPanel.add(browseOutputBtn, gbc);

        // 打包选项
        JPanel optionsPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JCheckBox compressCheckBox = new JCheckBox("启用压缩", true);
        JComboBox<String> compressionTypeCombo = new JComboBox<>(new String[]{"LZ4", "ZSTD", "Deflate"});
        optionsPanel.add(compressCheckBox);
        optionsPanel.add(new JLabel("压缩类型:"));
        optionsPanel.add(compressionTypeCombo);

        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3;
        inputPanel.add(optionsPanel, gbc);

        // 操作按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton packBtn = new JButton("开始打包");
        JButton clearBtn = new JButton("清空");
        buttonPanel.add(packBtn);
        buttonPanel.add(clearBtn);

        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 3;
        inputPanel.add(buttonPanel, gbc);

        // 进度和日志区域
        JPanel progressPanel = new JPanel(new BorderLayout());
        JProgressBar packProgressBar = new JProgressBar(0, 100);
        packProgressBar.setStringPainted(true);
        packProgressBar.setString("就绪");

        JTextArea packLogArea = new JTextArea(10, 50);
        packLogArea.setEditable(false);
        packLogArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane packLogScroll = new JScrollPane(packLogArea);

        progressPanel.add(packProgressBar, BorderLayout.NORTH);
        progressPanel.add(packLogScroll, BorderLayout.CENTER);

        // 事件处理
        browseSourceBtn.addActionListener(e -> {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);

            // 设置默认目录
            String currentPath = sourceFolderField.getText().trim();
            if (!currentPath.isEmpty() && new File(currentPath).exists()) {
                chooser.setCurrentDirectory(new File(currentPath));
            } else {
                // 使用默认NPK源文件夹
                File defaultDir = new File(pathConfig.getNpkSource());
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }

            if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
                sourceFolderField.setText(chooser.getSelectedFile().getAbsolutePath());
            }
        });

        browseOutputBtn.addActionListener(e -> {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("NPK文件", "npk"));

            // 设置默认目录
            String currentPath = outputNpkField.getText().trim();
            if (!currentPath.isEmpty() && new File(currentPath).exists() && new File(currentPath).isDirectory()) {
                // 如果当前路径是目录，直接使用
                chooser.setCurrentDirectory(new File(currentPath));
            } else if (!currentPath.isEmpty() && new File(currentPath).getParentFile() != null && new File(currentPath).getParentFile().exists()) {
                // 如果当前路径是文件，使用其父目录
                chooser.setCurrentDirectory(new File(currentPath).getParentFile());
            } else {
                // 使用默认NPK输出目录
                File defaultDir = new File(pathConfig.getNpkOutput());
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }

            if (chooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
                String path = chooser.getSelectedFile().getAbsolutePath();
                if (!path.toLowerCase().endsWith(".npk")) {
                    path += ".npk";
                }
                outputNpkField.setText(path);
            }
        });

        packBtn.addActionListener(e -> {
            String sourceFolder = sourceFolderField.getText().trim();
            String outputNpk = outputNpkField.getText().trim();

            if (sourceFolder.isEmpty() || outputNpk.isEmpty()) {
                JOptionPane.showMessageDialog(this, "请选择源文件夹和输出NPK文件!", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // 执行NPK打包
            performNpkPacking(sourceFolder, outputNpk, compressCheckBox.isSelected(),
                    (String)compressionTypeCombo.getSelectedItem(),
                    packProgressBar, packLogArea, packBtn);
        });

        clearBtn.addActionListener(e -> {
            sourceFolderField.setText("");
            outputNpkField.setText("");
            packLogArea.setText("");
            packProgressBar.setValue(0);
            packProgressBar.setString("就绪");
        });

        panel.add(inputPanel, BorderLayout.NORTH);
        panel.add(progressPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 执行NPK打包
     */
    private void performNpkPacking(String sourceFolder, String outputNpk, boolean enableCompression,
                                   String compressionType, JProgressBar progressBar,
                                   JTextArea logArea, JButton packBtn) {

        // 在后台线程执行打包
        SwingWorker<Boolean, String> worker = new SwingWorker<Boolean, String>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                try {
                    packBtn.setEnabled(false);
                    progressBar.setValue(0);
                    progressBar.setString("准备打包...");

                    publish("=== 开始NPK打包 ===");
                    publish("源文件夹: " + sourceFolder);
                    publish("输出文件: " + outputNpk);
                    publish("压缩设置: " + (enableCompression ? "启用(" + compressionType + ")" : "禁用"));
                    publish("");

                    // 确定压缩类型
                    int compType = 0; // 默认无压缩
                    if (enableCompression) {
                        switch (compressionType) {
                            case "LZ4":
                                compType = 2;
                                break;
                            case "ZSTD":
                                compType = 3;
                                break;
                            case "Deflate":
                            default:
                                compType = 1;
                                break;
                        }
                    }

                    // 执行打包
                    boolean success = com.tool.npk.NpkPacker.packFolder(
                            sourceFolder,
                            outputNpk,
                            compType,
                            (progress, message) -> {
                                if (progress >= 0) {
                                    SwingUtilities.invokeLater(() -> {
                                        progressBar.setValue(progress);
                                        progressBar.setString(progress + "%");
                                    });
                                }
                                publish(message);
                            }
                    );

                    return success;

                } catch (Exception e) {
                    publish("打包异常: " + e.getMessage());
                    e.printStackTrace();
                    return false;
                }
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    logArea.append(message + "\n");
                }
                logArea.setCaretPosition(logArea.getDocument().getLength());
            }

            @Override
            protected void done() {
                try {
                    boolean success = get();

                    if (success) {
                        progressBar.setValue(100);
                        progressBar.setString("打包完成");
                        logArea.append("\n✓ NPK打包成功完成!\n");
                        logArea.append("输出文件: " + outputNpk + "\n");

                        // 显示文件信息
                        java.io.File outputFile = new java.io.File(outputNpk);
                        if (outputFile.exists()) {
                            long fileSize = outputFile.length();
                            logArea.append("文件大小: " + formatFileSize(fileSize) + "\n");
                        }

                        JOptionPane.showMessageDialog(SkinIdSyncTool.this,
                                "NPK文件打包成功!\n输出文件: " + outputNpk,
                                "打包完成", JOptionPane.INFORMATION_MESSAGE);
                    } else {
                        progressBar.setString("打包失败");
                        logArea.append("\n✗ NPK打包失败!\n");

                        JOptionPane.showMessageDialog(SkinIdSyncTool.this,
                                "NPK文件打包失败!\n请检查日志信息。",
                                "打包失败", JOptionPane.ERROR_MESSAGE);
                    }

                } catch (Exception e) {
                    progressBar.setString("打包异常");
                    logArea.append("\n✗ 打包过程发生异常: " + e.getMessage() + "\n");
                    e.printStackTrace();
                } finally {
                    packBtn.setEnabled(true);
                }
            }
        };

        worker.execute();
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }

    // NPK查看相关的组件
    private JTextField npkViewFileField;
    private JList<com.tool.npk.NpkEntry> npkFileList;
    private DefaultListModel<com.tool.npk.NpkEntry> npkListModel;
    private JLabel npkImagePreviewLabel; // 重命名避免冲突
    private JLabel npkStatusLabel;
    private JProgressBar npkViewProgressBar;
    private com.tool.npk.NpkFile currentNpkFile;

    // 文件改名相关的组件
    private JTextField renamePathField;
    private JTree folderTree;
    private DefaultTreeModel folderTreeModel;
    private JList<File> pngFileList;
    private DefaultListModel<File> pngFileListModel;
    private JLabel renamePreviewLabel;
    private JTextArea renameLogArea;
    private JButton scanFoldersButton;
    private JButton previewRenameButton;
    private JButton executeRenameButton;
    private JProgressBar renameProgressBar;

    /**
     * 创建NPK查看面板
     */
    private JPanel createNpkViewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("NPK文件查看器"));

        // 创建顶部控制面板
        JPanel controlPanel = createNpkViewControlPanel();
        panel.add(controlPanel, BorderLayout.NORTH);

        // 创建主要内容面板（分割面板）
        JSplitPane splitPane = createNpkViewSplitPane();
        panel.add(splitPane, BorderLayout.CENTER);

        // 创建底部状态面板
        JPanel statusPanel = createNpkViewStatusPanel();
        panel.add(statusPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建NPK查看控制面板
     */
    private JPanel createNpkViewControlPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // NPK文件选择
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("NPK文件:"), gbc);

        npkViewFileField = new JTextField(pathConfig.getNpkFile(), 40);
        gbc.gridx = 1; gbc.gridy = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(npkViewFileField, gbc);

        JButton browseNpkBtn = new JButton("浏览");
        gbc.gridx = 2; gbc.gridy = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(browseNpkBtn, gbc);

        JButton loadNpkBtn = new JButton("加载");
        gbc.gridx = 3; gbc.gridy = 0;
        panel.add(loadNpkBtn, gbc);

        JButton refreshBtn = new JButton("刷新");
        gbc.gridx = 4; gbc.gridy = 0;
        panel.add(refreshBtn, gbc);

        // 搜索功能
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("搜索:"), gbc);

        JTextField searchField = new JTextField(20);
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(searchField, gbc);

        // 添加Enter键支持
        searchField.addActionListener(e -> searchNpkFiles(searchField.getText().trim()));

        JButton searchBtn = new JButton("搜索");
        gbc.gridx = 2; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(searchBtn, gbc);

        JButton clearSearchBtn = new JButton("清空");
        gbc.gridx = 3; gbc.gridy = 1;
        panel.add(clearSearchBtn, gbc);

        // 排序选项
        gbc.gridx = 0; gbc.gridy = 2;
        panel.add(new JLabel("排序:"), gbc);

        JComboBox<String> sortCombo = new JComboBox<>(new String[]{
                "默认顺序", "文件名(A-Z)", "文件名(Z-A)",
                "文件大小(小到大)", "文件大小(大到小)",
                "压缩率(低到高)", "压缩率(高到低)"
        });
        gbc.gridx = 1; gbc.gridy = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(sortCombo, gbc);

        JButton sortBtn = new JButton("应用排序");
        gbc.gridx = 2; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(sortBtn, gbc);

        // 事件处理
        browseNpkBtn.addActionListener(e -> {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("NPK文件", "npk"));

            // 设置默认目录
            String currentPath = npkViewFileField.getText().trim();
            if (!currentPath.isEmpty()) {
                File currentFile = new File(currentPath);
                if (currentFile.exists()) {
                    if (currentFile.isDirectory()) {
                        // 如果是目录，直接使用
                        chooser.setCurrentDirectory(currentFile);
                    } else if (currentFile.getParentFile() != null && currentFile.getParentFile().exists()) {
                        // 如果是文件，使用其父目录
                        chooser.setCurrentDirectory(currentFile.getParentFile());
                    }
                } else {
                    // 文件不存在，尝试使用其父目录
                    File parentDir = currentFile.getParentFile();
                    if (parentDir != null && parentDir.exists()) {
                        chooser.setCurrentDirectory(parentDir);
                    }
                }
            }

            // 如果上面都没有设置成功，使用默认NPK文件目录
            if (chooser.getCurrentDirectory() == null) {
                File defaultDir = new File(pathConfig.getNpkFile());
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }

            if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
                npkViewFileField.setText(chooser.getSelectedFile().getAbsolutePath());
            }
        });

        loadNpkBtn.addActionListener(e -> loadNpkFile());
        refreshBtn.addActionListener(e -> refreshNpkView());

        searchBtn.addActionListener(e -> searchNpkFiles(searchField.getText().trim()));
        clearSearchBtn.addActionListener(e -> {
            searchField.setText("");
            // 清空搜索相当于搜索空字符串，显示所有文件
            searchNpkFiles("");
        });

        sortBtn.addActionListener(e -> applySorting((String)sortCombo.getSelectedItem()));

        // 排序选择变化时自动应用
        sortCombo.addActionListener(e -> applySorting((String)sortCombo.getSelectedItem()));

        return panel;
    }

    /**
     * 创建NPK查看分割面板
     */
    private JSplitPane createNpkViewSplitPane() {
        // 创建文件列表面板
        JPanel listPanel = createNpkFileListPanel();

        // 创建图像预览面板
        JPanel previewPanel = createNpkImagePreviewPanel();

        // 创建分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, listPanel, previewPanel);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.5);

        return splitPane;
    }

    /**
     * 创建NPK文件列表面板
     */
    private JPanel createNpkFileListPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("文件列表"));

        // 创建列表模型和列表
        npkListModel = new DefaultListModel<>();
        npkFileList = new JList<>(npkListModel);
        npkFileList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);

        // 自定义列表渲染器
        npkFileList.setCellRenderer(new NpkEntryListCellRenderer());

        // 添加选择监听器
        npkFileList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
                if (selectedEntry != null) {
                    previewImage(selectedEntry);
                }
            }
        });

        // 添加键盘快捷键支持
        npkFileList.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyPressed(java.awt.event.KeyEvent e) {
                com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
                if (selectedEntry == null) return;

                // Ctrl+C 复制文件名
                if (e.getKeyCode() == java.awt.event.KeyEvent.VK_C && e.isControlDown()) {
                    copySelectedFileName();
                }
                // Ctrl+S 保存图像
                else if (e.getKeyCode() == java.awt.event.KeyEvent.VK_S && e.isControlDown()) {
                    saveSelectedImage();
                }
                // Enter 预览图像
                else if (e.getKeyCode() == java.awt.event.KeyEvent.VK_ENTER) {
                    previewImage(selectedEntry);
                }
                // F5 刷新
                else if (e.getKeyCode() == java.awt.event.KeyEvent.VK_F5) {
                    refreshNpkView();
                }
            }
        });

        JScrollPane listScrollPane = new JScrollPane(npkFileList);
        listScrollPane.setPreferredSize(new Dimension(380, 400));
        panel.add(listScrollPane, BorderLayout.CENTER);

        // 添加右键菜单
        JPopupMenu popupMenu = createNpkFilePopupMenu();
        npkFileList.setComponentPopupMenu(popupMenu);

        return panel;
    }

    /**
     * 创建NPK图像预览面板
     */
    private JPanel createNpkImagePreviewPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("图像预览"));

        // 创建预览标签
        npkImagePreviewLabel = new JLabel("请选择文件进行预览", SwingConstants.CENTER);
        npkImagePreviewLabel.setPreferredSize(new Dimension(300, 300));
        npkImagePreviewLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        npkImagePreviewLabel.setBackground(Color.WHITE);
        npkImagePreviewLabel.setOpaque(true);

        JScrollPane previewScrollPane = new JScrollPane(npkImagePreviewLabel);
        previewScrollPane.setPreferredSize(new Dimension(320, 400));
        panel.add(previewScrollPane, BorderLayout.CENTER);

        // 创建预览控制面板
        JPanel previewControlPanel = new JPanel(new FlowLayout());

        JButton saveImageBtn = new JButton("保存图像");
        JButton copyImageBtn = new JButton("复制图像");
        JButton actualSizeBtn = new JButton("实际大小");

        previewControlPanel.add(saveImageBtn);
        previewControlPanel.add(copyImageBtn);
        previewControlPanel.add(actualSizeBtn);

        panel.add(previewControlPanel, BorderLayout.SOUTH);

        // 事件处理
        saveImageBtn.addActionListener(e -> saveSelectedImage());
        copyImageBtn.addActionListener(e -> copySelectedImage());
        actualSizeBtn.addActionListener(e -> showActualSize());

        return panel;
    }

    /**
     * 创建NPK查看状态面板
     */
    private JPanel createNpkViewStatusPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        npkStatusLabel = new JLabel("就绪");
        panel.add(npkStatusLabel, BorderLayout.WEST);

        npkViewProgressBar = new JProgressBar(0, 100);
        npkViewProgressBar.setStringPainted(true);
        npkViewProgressBar.setString("就绪");
        npkViewProgressBar.setPreferredSize(new Dimension(200, 20));
        panel.add(npkViewProgressBar, BorderLayout.EAST);

        return panel;
    }

    /**
     * 加载NPK文件
     */
    private void loadNpkFile() {
        String npkFilePath = npkViewFileField.getText().trim();
        if (npkFilePath.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请选择NPK文件!", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 在后台线程加载NPK文件
        SwingWorker<com.tool.npk.NpkFile, String> worker = new SwingWorker<com.tool.npk.NpkFile, String>() {
            @Override
            protected com.tool.npk.NpkFile doInBackground() throws Exception {
                return com.tool.npk.NpkViewerHelper.loadNpkFileAsync(npkFilePath,
                        (progress, message) -> {
                            SwingUtilities.invokeLater(() -> {
                                if (progress >= 0) {
                                    npkViewProgressBar.setValue(progress);
                                    npkViewProgressBar.setString(progress + "%");
                                }
                                npkStatusLabel.setText(message);
                            });
                        }).get();
            }

            @Override
            protected void done() {
                try {
                    currentNpkFile = get();

                    // 更新文件列表
                    updateNpkFileList(currentNpkFile.getEntries());

                    npkStatusLabel.setText("加载完成: " + currentNpkFile.getEntries().size() + " 个文件");
                    npkViewProgressBar.setValue(100);
                    npkViewProgressBar.setString("完成");

                } catch (Exception e) {
                    npkStatusLabel.setText("加载失败: " + e.getMessage());
                    npkViewProgressBar.setString("失败");
                    JOptionPane.showMessageDialog(SkinIdSyncTool.this,
                            "加载NPK文件失败:\n" + e.getMessage(),
                            "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };

        worker.execute();
    }

    /**
     * 更新NPK文件列表 - 优化版本
     */
    private void updateNpkFileList(java.util.List<com.tool.npk.NpkEntry> entries) {
        // 在EDT线程中安全地更新列表
        SwingUtilities.invokeLater(() -> {
            try {
                // 暂时禁用列表事件，提高性能
                npkFileList.clearSelection();
                npkFileList.setEnabled(false);

                // 清空并重新填充列表
                npkListModel.clear();

                // 批量添加元素
                for (com.tool.npk.NpkEntry entry : entries) {
                    npkListModel.addElement(entry);
                }

                // 重新启用列表
                npkFileList.setEnabled(true);

            } catch (Exception e) {
                npkStatusLabel.setText("更新列表失败: " + e.getMessage());
            }
        });
    }

    /**
     * 刷新NPK查看
     */
    private void refreshNpkView() {
        if (currentNpkFile == null) {
            npkStatusLabel.setText("请先加载NPK文件");
            return;
        }

        // 在后台线程执行刷新，避免UI卡死
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                SwingUtilities.invokeLater(() -> {
                    npkStatusLabel.setText("正在刷新...");
                    npkViewProgressBar.setValue(0);
                    npkViewProgressBar.setString("刷新中...");
                });

                // 模拟进度
                Thread.sleep(100);

                return null;
            }

            @Override
            protected void done() {
                try {
                    // 清空当前列表
                    npkListModel.clear();

                    // 重新加载所有条目
                    java.util.List<com.tool.npk.NpkEntry> allEntries = currentNpkFile.getEntries();
                    for (com.tool.npk.NpkEntry entry : allEntries) {
                        npkListModel.addElement(entry);
                    }

                    // 清空预览
                    npkImagePreviewLabel.setIcon(null);
                    npkImagePreviewLabel.setText("请选择文件进行预览");

                    // 更新状态
                    npkStatusLabel.setText("刷新完成: " + allEntries.size() + " 个文件");
                    npkViewProgressBar.setValue(100);
                    npkViewProgressBar.setString("完成");

                } catch (Exception e) {
                    npkStatusLabel.setText("刷新失败: " + e.getMessage());
                    npkViewProgressBar.setString("失败");
                }
            }
        };

        worker.execute();
    }

    /**
     * 应用排序
     */
    private void applySorting(String sortType) {
        if (currentNpkFile == null) {
            npkStatusLabel.setText("请先加载NPK文件");
            return;
        }

        // 在后台线程执行排序，避免UI卡死
        SwingWorker<java.util.List<com.tool.npk.NpkEntry>, Void> worker =
                new SwingWorker<java.util.List<com.tool.npk.NpkEntry>, Void>() {

                    @Override
                    protected java.util.List<com.tool.npk.NpkEntry> doInBackground() throws Exception {
                        SwingUtilities.invokeLater(() -> {
                            npkStatusLabel.setText("正在排序...");
                            npkViewProgressBar.setValue(50);
                            npkViewProgressBar.setString("排序中...");
                        });

                        // 获取当前显示的条目
                        java.util.List<com.tool.npk.NpkEntry> currentEntries = new java.util.ArrayList<>();
                        for (int i = 0; i < npkListModel.getSize(); i++) {
                            currentEntries.add(npkListModel.getElementAt(i));
                        }

                        // 执行排序
                        java.util.List<com.tool.npk.NpkEntry> sortedEntries =
                                com.tool.npk.NpkSortHelper.sortEntries(currentEntries, sortType);

                        return sortedEntries;
                    }

                    @Override
                    protected void done() {
                        try {
                            java.util.List<com.tool.npk.NpkEntry> sortedEntries = get();

                            // 更新列表
                            updateNpkFileList(sortedEntries);

                            // 更新状态
                            npkStatusLabel.setText("排序完成: " + sortedEntries.size() + " 个文件 (按" + sortType + ")");
                            npkViewProgressBar.setValue(100);
                            npkViewProgressBar.setString("完成");

                        } catch (Exception e) {
                            npkStatusLabel.setText("排序失败: " + e.getMessage());
                            npkViewProgressBar.setString("失败");
                        }
                    }
                };

        worker.execute();
    }

    /**
     * 搜索NPK文件
     */
    private void searchNpkFiles(String searchText) {
        if (currentNpkFile == null) {
            JOptionPane.showMessageDialog(this, "请先加载NPK文件!", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        // 在后台线程执行搜索，避免UI卡死
        SwingWorker<java.util.List<com.tool.npk.NpkEntry>, Void> worker =
                new SwingWorker<java.util.List<com.tool.npk.NpkEntry>, Void>() {

                    @Override
                    protected java.util.List<com.tool.npk.NpkEntry> doInBackground() throws Exception {
                        SwingUtilities.invokeLater(() -> {
                            npkStatusLabel.setText("正在搜索...");
                            npkViewProgressBar.setValue(50);
                            npkViewProgressBar.setString("搜索中...");
                        });

                        // 执行搜索
                        java.util.List<com.tool.npk.NpkEntry> filteredEntries =
                                com.tool.npk.NpkViewerHelper.searchEntries(currentNpkFile.getEntries(), searchText);

                        return filteredEntries;
                    }

                    @Override
                    protected void done() {
                        try {
                            java.util.List<com.tool.npk.NpkEntry> filteredEntries = get();

                            // 清空当前列表
                            npkListModel.clear();

                            // 添加搜索结果
                            for (com.tool.npk.NpkEntry entry : filteredEntries) {
                                npkListModel.addElement(entry);
                            }

                            // 清空预览
                            npkImagePreviewLabel.setIcon(null);
                            npkImagePreviewLabel.setText("请选择文件进行预览");

                            // 更新状态
                            if (searchText == null || searchText.trim().isEmpty()) {
                                npkStatusLabel.setText("显示所有文件: " + filteredEntries.size() + " 个");
                            } else {
                                npkStatusLabel.setText("搜索结果: " + filteredEntries.size() + " 个文件 (关键词: " + searchText + ")");
                            }

                            npkViewProgressBar.setValue(100);
                            npkViewProgressBar.setString("完成");

                        } catch (Exception e) {
                            npkStatusLabel.setText("搜索失败: " + e.getMessage());
                            npkViewProgressBar.setString("失败");
                        }
                    }
                };

        worker.execute();
    }

    /**
     * 预览图像
     */
    private void previewImage(com.tool.npk.NpkEntry entry) {
        if (currentNpkFile == null) {
            return;
        }

        // 显示加载状态
        npkImagePreviewLabel.setIcon(null);
        npkImagePreviewLabel.setText("加载中...");

        // 异步加载图像
        com.tool.npk.NpkViewerHelper.previewImageAsync(currentNpkFile, entry, 280, 280)
                .thenAccept(icon -> {
                    SwingUtilities.invokeLater(() -> {
                        npkImagePreviewLabel.setIcon(icon);
                        npkImagePreviewLabel.setText("");

                        // 更新状态
                        Dimension imageDim = com.tool.npk.NpkViewerHelper.getImageDimensions(currentNpkFile, entry);
                        if (imageDim != null) {
                            npkStatusLabel.setText(String.format("预览: %s (%dx%d)",
                                    entry.getFileName(), imageDim.width, imageDim.height));
                        } else {
                            npkStatusLabel.setText("预览: " + entry.getFileName());
                        }
                    });
                })
                .exceptionally(throwable -> {
                    SwingUtilities.invokeLater(() -> {
                        npkImagePreviewLabel.setIcon(null);
                        npkImagePreviewLabel.setText("预览失败");
                        npkStatusLabel.setText("预览失败: " + throwable.getMessage());
                    });
                    return null;
                });
    }

    /**
     * 创建NPK文件右键菜单
     */
    private JPopupMenu createNpkFilePopupMenu() {
        JPopupMenu popupMenu = new JPopupMenu();

        JMenuItem previewItem = new JMenuItem("预览图像");
        JMenuItem copyFileNameItem = new JMenuItem("复制文件名");
        JMenuItem saveItem = new JMenuItem("保存图像");
        JMenuItem copyImageItem = new JMenuItem("复制图像");

        // 排序子菜单
        JMenu sortMenu = new JMenu("排序");
        JMenuItem sortByNameAscItem = new JMenuItem("按文件名(A-Z)");
        JMenuItem sortByNameDescItem = new JMenuItem("按文件名(Z-A)");
        JMenuItem sortBySizeAscItem = new JMenuItem("按大小(小到大)");
        JMenuItem sortBySizeDescItem = new JMenuItem("按大小(大到小)");
        JMenuItem sortByCompressionAscItem = new JMenuItem("按压缩率(低到高)");
        JMenuItem sortByCompressionDescItem = new JMenuItem("按压缩率(高到低)");
        JMenuItem sortDefaultItem = new JMenuItem("默认顺序");

        sortMenu.add(sortByNameAscItem);
        sortMenu.add(sortByNameDescItem);
        sortMenu.addSeparator();
        sortMenu.add(sortBySizeAscItem);
        sortMenu.add(sortBySizeDescItem);
        sortMenu.addSeparator();
        sortMenu.add(sortByCompressionAscItem);
        sortMenu.add(sortByCompressionDescItem);
        sortMenu.addSeparator();
        sortMenu.add(sortDefaultItem);

        JMenuItem statisticsItem = new JMenuItem("统计信息");
        JMenuItem propertiesItem = new JMenuItem("属性");

        popupMenu.add(previewItem);
        popupMenu.addSeparator();
        popupMenu.add(copyFileNameItem);
        popupMenu.addSeparator();
        popupMenu.add(saveItem);
        popupMenu.add(copyImageItem);
        popupMenu.addSeparator();
        popupMenu.add(sortMenu);
        popupMenu.add(statisticsItem);
        popupMenu.addSeparator();
        popupMenu.add(propertiesItem);

        // 事件处理
        previewItem.addActionListener(e -> {
            com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
            if (selectedEntry != null) {
                previewImage(selectedEntry);
            }
        });

        copyFileNameItem.addActionListener(e -> copySelectedFileName());
        saveItem.addActionListener(e -> saveSelectedImage());
        copyImageItem.addActionListener(e -> copySelectedImage());

        // 排序菜单事件处理
        sortByNameAscItem.addActionListener(e -> applySorting("文件名(A-Z)"));
        sortByNameDescItem.addActionListener(e -> applySorting("文件名(Z-A)"));
        sortBySizeAscItem.addActionListener(e -> applySorting("文件大小(小到大)"));
        sortBySizeDescItem.addActionListener(e -> applySorting("文件大小(大到小)"));
        sortByCompressionAscItem.addActionListener(e -> applySorting("压缩率(低到高)"));
        sortByCompressionDescItem.addActionListener(e -> applySorting("压缩率(高到低)"));
        sortDefaultItem.addActionListener(e -> applySorting("默认顺序"));

        statisticsItem.addActionListener(e -> showStatistics());
        propertiesItem.addActionListener(e -> showImageProperties());

        return popupMenu;
    }

    /**
     * 复制选中的文件名
     */
    private void copySelectedFileName() {
        com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
        if (selectedEntry == null) {
            JOptionPane.showMessageDialog(this, "请先选择一个文件!", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        try {
            String fileName = selectedEntry.getFileName();

            // 复制到剪贴板
            java.awt.datatransfer.StringSelection stringSelection =
                    new java.awt.datatransfer.StringSelection(fileName);
            java.awt.datatransfer.Clipboard clipboard =
                    java.awt.Toolkit.getDefaultToolkit().getSystemClipboard();
            clipboard.setContents(stringSelection, null);

            // 更新状态
            npkStatusLabel.setText("已复制文件名: " + fileName);

            // 显示提示
            JOptionPane.showMessageDialog(this,
                    "文件名已复制到剪贴板:\n" + fileName,
                    "复制成功", JOptionPane.INFORMATION_MESSAGE);

        } catch (Exception e) {
            npkStatusLabel.setText("复制文件名失败: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                    "复制文件名失败:\n" + e.getMessage(),
                    "复制失败", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 保存选中的图像
     */
    private void saveSelectedImage() {
        com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
        if (selectedEntry == null || currentNpkFile == null) {
            JOptionPane.showMessageDialog(this, "请先选择一个文件!", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        JFileChooser chooser = new JFileChooser();
        chooser.setSelectedFile(new java.io.File(selectedEntry.getFileName()));
        chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("PNG图像", "png"));

        if (chooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            java.io.File outputFile = chooser.getSelectedFile();

            // 确保文件扩展名
            if (!outputFile.getName().toLowerCase().endsWith(".png")) {
                outputFile = new java.io.File(outputFile.getAbsolutePath() + ".png");
            }

            boolean success = com.tool.npk.NpkViewerHelper.saveImage(currentNpkFile, selectedEntry, outputFile);

            if (success) {
                JOptionPane.showMessageDialog(this,
                        "图像保存成功!\n保存位置: " + outputFile.getAbsolutePath(),
                        "保存成功", JOptionPane.INFORMATION_MESSAGE);
            } else {
                JOptionPane.showMessageDialog(this,
                        "图像保存失败!",
                        "保存失败", JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 复制选中的图像
     */
    private void copySelectedImage() {
        com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
        if (selectedEntry == null || currentNpkFile == null) {
            JOptionPane.showMessageDialog(this, "请先选择一个文件!", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        boolean success = com.tool.npk.NpkViewerHelper.copyImageToClipboard(currentNpkFile, selectedEntry);

        if (success) {
            npkStatusLabel.setText("图像已复制到剪贴板");
        } else {
            npkStatusLabel.setText("复制图像失败");
        }
    }

    /**
     * 显示实际大小
     */
    private void showActualSize() {
        com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
        if (selectedEntry == null || currentNpkFile == null) {
            return;
        }

        // 重新预览，使用实际尺寸
        npkImagePreviewLabel.setIcon(null);
        npkImagePreviewLabel.setText("加载中...");

        com.tool.npk.NpkViewerHelper.previewImageAsync(currentNpkFile, selectedEntry, 1000, 1000)
                .thenAccept(icon -> {
                    SwingUtilities.invokeLater(() -> {
                        npkImagePreviewLabel.setIcon(icon);
                        npkImagePreviewLabel.setText("");
                        npkStatusLabel.setText("显示实际大小: " + selectedEntry.getFileName());
                    });
                });
    }

    /**
     * 显示图像属性
     */
    private void showImageProperties() {
        com.tool.npk.NpkEntry selectedEntry = npkFileList.getSelectedValue();
        if (selectedEntry == null) {
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append("文件属性\n\n");
        sb.append("文件名: ").append(selectedEntry.getFileName()).append("\n");
        sb.append("偏移: 0x").append(Long.toHexString(selectedEntry.getOffset())).append("\n");
        sb.append("原始大小: ").append(formatFileSize(selectedEntry.getOriginalSize())).append("\n");
        sb.append("压缩大小: ").append(formatFileSize(selectedEntry.getCompressedSize())).append("\n");
        sb.append("压缩类型: ").append(getCompressionTypeName(selectedEntry.getCompressionType())).append("\n");

        if (selectedEntry.getOriginalSize() > 0) {
            double compressionRatio = (1.0 - (double)selectedEntry.getCompressedSize() / selectedEntry.getOriginalSize()) * 100;
            sb.append("压缩率: ").append(String.format("%.1f%%", compressionRatio)).append("\n");
        }

        // 获取图像尺寸
        if (currentNpkFile != null) {
            Dimension imageDim = com.tool.npk.NpkViewerHelper.getImageDimensions(currentNpkFile, selectedEntry);
            if (imageDim != null) {
                sb.append("图像尺寸: ").append(imageDim.width).append(" x ").append(imageDim.height).append("\n");
            }
        }

        JOptionPane.showMessageDialog(this, sb.toString(), "文件属性", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * 显示统计信息
     */
    private void showStatistics() {
        if (currentNpkFile == null) {
            JOptionPane.showMessageDialog(this, "请先加载NPK文件!", "提示", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        // 获取当前显示的条目
        java.util.List<com.tool.npk.NpkEntry> currentEntries = new java.util.ArrayList<>();
        for (int i = 0; i < npkListModel.getSize(); i++) {
            currentEntries.add(npkListModel.getElementAt(i));
        }

        // 生成统计信息
        String statistics = com.tool.npk.NpkSortHelper.getSortStatistics(currentEntries);

        // 显示统计信息对话框
        JTextArea textArea = new JTextArea(statistics);
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(300, 200));

        JOptionPane.showMessageDialog(this, scrollPane, "文件统计信息", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * 获取压缩类型名称
     */
    private String getCompressionTypeName(int compressionType) {
        switch (compressionType) {
            case 0: return "无压缩";
            case 1: return "Deflate";
            case 2: return "LZ4";
            case 3: return "ZSTD";
            default: return "未知(" + compressionType + ")";
        }
    }

    /**
     * 初始化数据
     */
    private void initializeData() {
        // 使用初始容量优化内存分配
        modifyRecords = new ArrayList<>(5000);
        random = new Random();
        idMappingCache = new HashMap<>(1000);

        // 显示初始内存状态
        logMemoryUsage("初始化完成");
    }

    /**
     * 记录内存使用情况并进行内存压力检测
     */
    private void logMemoryUsage(String operation) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();

        double usagePercent = (usedMemory * 100.0) / maxMemory;

        String memoryInfo = String.format("%s - 内存使用: %.1fMB / %.1fMB (%.1f%%) [最大: %.1fMB]",
                operation,
                usedMemory / 1024.0 / 1024.0,
                totalMemory / 1024.0 / 1024.0,
                usagePercent,
                maxMemory / 1024.0 / 1024.0);

        log(memoryInfo);

        // 分级内存压力处理
        if (usagePercent > 90) {
            log("⚠️ 内存使用率极高 (" + String.format("%.1f", usagePercent) + "%)，执行强制垃圾回收...");
            System.gc();
            System.runFinalization();
            System.gc(); // 二次GC

            // 再次检查内存
            runtime = Runtime.getRuntime();
            usedMemory = runtime.totalMemory() - runtime.freeMemory();
            usagePercent = (usedMemory * 100.0) / maxMemory;
            log("垃圾回收后内存使用率: " + String.format("%.1f", usagePercent) + "%");

        } else if (usagePercent > 75) {
            log("⚠️ 内存使用率较高 (" + String.format("%.1f", usagePercent) + "%)，执行垃圾回收...");
            System.gc();

        } else if (usagePercent > 60) {
            log("ℹ️ 内存使用率中等 (" + String.format("%.1f", usagePercent) + "%)");

        } else {
            log("✅ 内存使用率正常 (" + String.format("%.1f", usagePercent) + "%)");
        }
    }

    /**
     * 检查内存压力并在必要时暂停处理
     */
    private void checkMemoryPressure() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        double usagePercent = (usedMemory * 100.0) / maxMemory;

        if (usagePercent > 85) {
            log("内存压力过大，暂停处理进行垃圾回收...");
            System.gc();
            System.runFinalization();

            try {
                Thread.sleep(100); // 等待GC完成
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 再次检查
            usedMemory = runtime.totalMemory() - runtime.freeMemory();
            usagePercent = (usedMemory * 100.0) / maxMemory;

            if (usagePercent > 85) {
                log("警告：垃圾回收后内存使用率仍然很高 (" + String.format("%.1f", usagePercent) + "%)");
            }
        }
    }

    /**
     * 浏览文件夹
     */
    private void browseFolder(JTextField textField) {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);

        // 设置默认目录
        String currentPath = textField.getText().trim();
        if (!currentPath.isEmpty() && new File(currentPath).exists()) {
            chooser.setCurrentDirectory(new File(currentPath));
        } else {
            // 使用默认游戏资源目录
            File defaultDir = new File(pathConfig.getWdfSource());
            if (defaultDir.exists() && defaultDir.isDirectory()) {
                chooser.setCurrentDirectory(defaultDir);
            }
        }

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            textField.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * 浏览文件
     */
    private void browseFile(JTextField textField) {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        chooser.setCurrentDirectory(new File(textField.getText()).getParentFile());

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            textField.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * 清空预览
     */
    private void clearPreview() {
        tableModel.setRowCount(0);
        modifyRecords.clear();
        executeButton.setEnabled(false);
        log("已清空预览列表");
    }

    /**
     * 记录日志
     */
    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            String timestamp = java.time.LocalTime.now().toString();
            logArea.append("[" + timestamp + "] " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }



    /**
     * 执行扫描
     */
    private void performScan() {
        log("开始扫描文件...");

        String itemFolder = itemFolderField.getText().trim();
        String xlsPath = xlsPathField.getText().trim();

        // 验证路径
        if (!new File(itemFolder).exists()) {
            log("错误: 物品文件夹不存在: " + itemFolder);
            return;
        }

        if (!new File(xlsPath).exists()) {
            log("错误: XLS文件不存在: " + xlsPath);
            return;
        }

        try {
            // 读取XLS文件
            log("正在读取XLS文件...");
            xlsData = ReadExelTool.getResult(xlsPath);
            if (xlsData == null) {
                log("错误: 无法读取XLS文件");
                return;
            }

            // 预处理XLS文件，建立ID映射
            log("正在分析XLS文件，建立ID映射...");
            logMemoryUsage("XLS分析前");
            buildIdMapping();
            logMemoryUsage("ID映射建立完成");

            // 扫描PNG文件
            log("正在扫描PNG文件...");
            logMemoryUsage("PNG扫描前");
            scanPngFiles(itemFolder);
            logMemoryUsage("PNG扫描完成");

            // 更新预览表格
            updatePreviewTable();

            // 统计文件状态
            long readyCount = modifyRecords.stream()
                    .filter(record -> !record.isAlreadyConverted() && record.getXlsRowIndex() >= 0)
                    .count();
            long skipCount = modifyRecords.stream()
                    .filter(record -> !record.isAlreadyConverted() && record.getXlsRowIndex() < 0)
                    .count();
            long convertedCount = modifyRecords.stream()
                    .filter(SkinModifyRecord::isAlreadyConverted)
                    .count();

            log("扫描完成，总计 " + modifyRecords.size() + " 个文件");
            log("  - 准备转换: " + readyCount + " 个");
            log("  - 将跳过: " + skipCount + " 个 (XLS中未找到)");
            log("  - 已转换: " + convertedCount + " 个");
            logMemoryUsage("扫描全部完成");

            if (readyCount > 0) {
                SwingUtilities.invokeLater(() -> executeButton.setEnabled(true));
            }

        } catch (Exception e) {
            log("扫描过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 建立ID映射表 - 确保相同的原始ID使用相同的转换后ID
     */
    private void buildIdMapping() {
        if (xlsData == null) return;
        idMappingCache.clear();

        // 首先检测皮肤列的位置
        this.skinColumnIndex = detectSkinColumn();
        if (this.skinColumnIndex == -1) {
            log("警告: 未能检测到皮肤列，使用默认第3列(索引2)");
            this.skinColumnIndex = 2;
        } else {
            log("检测到皮肤列位置: 第" + (this.skinColumnIndex + 1) + "列");
        }

        // 遍历XLS文件，收集所有需要转换的原始ID
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > skinColumnIndex) {
                String xlsValue = xlsData[i][skinColumnIndex];
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim();

                    // 跳过空值和标题行
                    if (xlsValue.isEmpty() || isHeaderRow(xlsValue)) {
                        continue;
                    }

                    // 从单元格内容中提取数字ID
                    String extractedId = extractNumericId(xlsValue);
                    if (extractedId != null && !extractedId.isEmpty()) {
                        // 如果这个原始ID还没有映射，为它生成一个新的十六进制ID
                        if (!idMappingCache.containsKey(extractedId)) {
                            String newHexId = generateRandomHexId();
                            String newSkinId = "0x" + newHexId;
                            idMappingCache.put(extractedId, newSkinId);
                            log("为原始ID " + extractedId + " (来源: \"" + xlsValue + "\") 生成新ID: " + newSkinId);
                        }
                    }
                }
            }
        }

        log("ID映射表建立完成，共 " + idMappingCache.size() + " 个映射");
    }

    /**
     * 检测皮肤列的位置
     */
    private int detectSkinColumn() {
        if (xlsData == null || xlsData.length == 0) {
            return -1;
        }

        // 检查前几行，寻找包含"皮肤"关键字的列
        for (int row = 0; row < Math.min(5, xlsData.length); row++) {
            if (xlsData[row] != null) {
                for (int col = 0; col < xlsData[row].length; col++) {
                    String cellValue = xlsData[row][col];
                    if (cellValue != null) {
                        cellValue = cellValue.trim().toLowerCase();
                        if (cellValue.contains("皮肤") || cellValue.contains("skin")) {
                            return col;
                        }
                    }
                }
            }
        }

        // 如果没找到明确的皮肤列标题，尝试通过数据特征判断
        // 寻找包含较多数字的列
        int bestColumn = -1;
        int maxNumericCount = 0;

        if (xlsData.length > 1 && xlsData[0] != null) {
            for (int col = 0; col < xlsData[0].length; col++) {
                int numericCount = 0;
                // 检查这一列中有多少行包含数字
                for (int row = 1; row < Math.min(20, xlsData.length); row++) {
                    if (xlsData[row] != null && xlsData[row].length > col) {
                        String cellValue = xlsData[row][col];
                        if (cellValue != null && extractNumericId(cellValue.trim()) != null) {
                            numericCount++;
                        }
                    }
                }

                if (numericCount > maxNumericCount) {
                    maxNumericCount = numericCount;
                    bestColumn = col;
                }
            }
        }

        return bestColumn;
    }

    /**
     * 判断是否为标题行
     */
    private boolean isHeaderRow(String cellValue) {
        if (cellValue == null) return false;
        String lower = cellValue.toLowerCase().trim();
        return lower.contains("皮肤") || lower.contains("skin") ||
               lower.contains("id") || lower.contains("编号") ||
               lower.contains("序号") || lower.contains("名称");
    }

    /**
     * 从单元格内容中提取数字ID
     */
    private String extractNumericId(String cellValue) {
        if (cellValue == null || cellValue.trim().isEmpty()) {
            return null;
        }

        cellValue = cellValue.trim();

        // 如果已经是十六进制格式，跳过
        if (cellValue.startsWith("0x") || cellValue.startsWith("0X")) {
            return null;
        }

        // 尝试提取纯数字
        if (cellValue.matches("\\d+")) {
            return cellValue;
        }

        // 尝试从包含其他字符的字符串中提取数字
        // 例如: "皮肤123", "ID_456", "skin789", "123abc", "abc123def"
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
        java.util.regex.Matcher matcher = pattern.matcher(cellValue);

        if (matcher.find()) {
            String numericPart = matcher.group();
            // 确保提取的数字有意义（不是太短或太长）
            if (numericPart.length() >= 1 && numericPart.length() <= 10) {
                return numericPart;
            }
        }

        return null;
    }

    /**
     * 扫描PNG文件
     */
    private void scanPngFiles(String itemFolder) throws IOException {
        modifyRecords.clear();

        // 使用更小的初始容量，避免一次性分配过多内存
        modifyRecords = new ArrayList<>(1000);

        // 正则表达式匹配不同格式的文件
        Pattern newFormatPattern = Pattern.compile("^0x([5-9A-Fa-f]{8})\\.png$");

        Path folderPath = Paths.get(itemFolder);

        log("开始分批扫描PNG文件...");
        logMemoryUsage("扫描开始前");

        // 分批处理文件以控制内存使用
        final int BATCH_SIZE = 500; // 每批处理500个文件
        final java.util.concurrent.atomic.AtomicInteger processedCount = new java.util.concurrent.atomic.AtomicInteger(0);
        final java.util.concurrent.atomic.AtomicInteger batchCount = new java.util.concurrent.atomic.AtomicInteger(0);

        // 使用try-with-resources确保流被正确关闭
        try (java.util.stream.Stream<Path> pathStream = Files.list(folderPath)) {
            java.util.List<Path> batch = new java.util.ArrayList<>(BATCH_SIZE);

            pathStream
                    .filter(path -> {
                        String fileName = path.getFileName().toString().toLowerCase();
                        return fileName.endsWith(".png");
                    })
                    .forEach(path -> {
                        batch.add(path);

                        // 当批次满了或者是最后一批时，处理这一批
                        if (batch.size() >= BATCH_SIZE) {
                            processBatch(batch, newFormatPattern, processedCount, batchCount);
                            batch.clear();

                            // 每处理一批后进行垃圾回收
                            System.gc();

                            // 短暂暂停让GC有时间工作
                            try {
                                Thread.sleep(10);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                        }
                    });

            // 处理最后一批
            if (!batch.isEmpty()) {
                processBatch(batch, newFormatPattern, processedCount, batchCount);
                batch.clear();
            }
        }

        logMemoryUsage("扫描完成后");
        log("PNG文件扫描完成，共处理 " + processedCount.get() + " 个文件");

        // 最终垃圾回收
        System.gc();
    }

    /**
     * 处理一批文件
     */
    private void processBatch(java.util.List<Path> batch, Pattern newFormatPattern,
                              java.util.concurrent.atomic.AtomicInteger processedCount,
                              java.util.concurrent.atomic.AtomicInteger batchCount) {
        int currentBatch = batchCount.incrementAndGet();
        log("处理第 " + currentBatch + " 批文件 (" + batch.size() + " 个)...");

        for (Path path : batch) {
            try {
                String fileName = path.getFileName().toString();

                // 检查是否是新格式（已经转换过的，仅用于显示）
                Matcher newMatcher = newFormatPattern.matcher(fileName);
                if (newMatcher.matches()) {
                    String hexId = newMatcher.group(1);
                    String oldSkinId = String.valueOf(Long.parseLong(hexId, 16));
                    String oldFileName = oldSkinId + ".png";
                    String newSkinId = "0x" + hexId.toUpperCase();

                    SkinModifyRecord record = new SkinModifyRecord(oldFileName, fileName, oldSkinId, newSkinId);
                    record.setFileExists(true);

                    // 查找XLS中对应的行
                    findXlsRowForNewFormat(record);

                    // 标记为已转换，不需要修改
                    record.setAlreadyConverted(true);
                    modifyRecords.add(record);
                } else {
                    // 所有其他PNG文件都视为需要转换的文件（不限制为纯数字文件名）
                    String oldSkinId = fileName.substring(0, fileName.lastIndexOf('.'));

                    // 检查是否在映射表中有对应的转换ID
                    String newSkinId = idMappingCache.get(oldSkinId);
                    String newFileName;

                    if (newSkinId != null) {
                        // 使用映射表中的ID
                        String hexPart = newSkinId.substring(2); // 去掉"0x"前缀
                        newFileName = "0x" + hexPart + ".png";
                    } else {
                        // 如果映射表中没有，生成新的随机ID
                        String randomHexId = generateRandomHexId();
                        newFileName = "0x" + randomHexId + ".png";
                        newSkinId = "0x" + randomHexId;
                        // 将新生成的映射添加到缓存中
                        idMappingCache.put(oldSkinId, newSkinId);
                    }

                    SkinModifyRecord record = new SkinModifyRecord(fileName, newFileName, oldSkinId, newSkinId);
                    record.setFileExists(true);

                    // 查找XLS中对应的行（基于原文件名）
                    findXlsRowByFileName(record);

                    modifyRecords.add(record);
                }

                processedCount.incrementAndGet();

                // 每处理100个文件检查一次内存压力
                if (processedCount.get() % 100 == 0) {
                    checkMemoryPressure();
                }

            } catch (Exception e) {
                // 记录错误但继续处理其他文件
                System.err.println("处理文件时出错: " + path.getFileName() + " - " + e.getMessage());
            }
        }

        // 每批处理完后记录内存使用
        if (currentBatch % 3 == 0) { // 每3批记录一次，增加监控频率
            logMemoryUsage("处理第 " + currentBatch + " 批后");
        }
    }

    /**
     * 生成随机的8位十六进制ID
     */
    private String generateRandomHexId() {
        // 生成一个随机的32位整数，确保结果是8位十六进制
        long randomValue = random.nextLong() & 0xFFFFFFFFL; // 确保是正数且在32位范围内
        // 确保生成的十六进制至少以5-9或A-F开头（避免与小数值冲突）
        if (randomValue < 0x50000000L) {
            randomValue += 0x50000000L;
        }
        return String.format("%08X", randomValue);
    }

    /**
     * 根据文件名查找XLS中对应的行
     */
    private void findXlsRowByFileName(SkinModifyRecord record) {
        if (xlsData == null) return;

        String originalFileName = record.getOldSkinId(); // 这里存储的是原文件名（不含扩展名）

        // 确保皮肤列索引已经设置
        if (skinColumnIndex == -1) {
            skinColumnIndex = detectSkinColumn();
            if (skinColumnIndex == -1) {
                skinColumnIndex = 2; // 默认第3列
            }
        }

        // 尝试多种匹配方式
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > skinColumnIndex) {
                String xlsValue = xlsData[i][skinColumnIndex]; // 使用动态检测的皮肤列
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim(); // 去除空格

                    // 从XLS单元格中提取数字ID进行比较
                    String extractedId = extractNumericId(xlsValue);

                    // 1. 直接匹配原始单元格内容
                    if (xlsValue.equals(originalFileName)) {
                        record.setXlsRowIndex(i);
                        return;
                    }

                    // 2. 如果从XLS中提取到了数字ID，与原文件名比较
                    if (extractedId != null && !extractedId.isEmpty()) {
                        // 2.1 直接匹配提取的数字ID
                        if (originalFileName.equals(extractedId)) {
                            record.setXlsRowIndex(i);
                            return;
                        }

                        // 2.2 如果原文件名是十六进制格式，转换后比较
                        if (originalFileName.startsWith("0x")) {
                            try {
                                String hexPart = originalFileName.substring(2);
                                long decimalValue = Long.parseLong(hexPart, 16);
                                if (extractedId.equals(String.valueOf(decimalValue))) {
                                    record.setXlsRowIndex(i);
                                    return;
                                }
                            } catch (Exception e) {
                                // 忽略转换错误
                            }
                        }
                    }

                    // 3. 如果原文件名是纯数字，尝试与XLS中的十六进制格式匹配
                    if (originalFileName.matches("\\d+") && xlsValue.startsWith("0x")) {
                        try {
                            String hexPart = xlsValue.substring(2);
                            long decimalValue = Long.parseLong(hexPart, 16);
                            if (originalFileName.equals(String.valueOf(decimalValue))) {
                                record.setXlsRowIndex(i);
                                return;
                            }
                        } catch (Exception e) {
                            // 忽略转换错误
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新XLS中所有匹配的行
     */
    private void updateAllMatchingXlsRows(String oldSkinId, String newSkinId) {
        if (xlsData == null) return;

        // 确保皮肤列索引已经设置
        if (skinColumnIndex == -1) {
            skinColumnIndex = detectSkinColumn();
            if (skinColumnIndex == -1) {
                skinColumnIndex = 2; // 默认第3列
            }
        }

        int updateCount = 0;

        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > skinColumnIndex) {
                String xlsValue = xlsData[i][skinColumnIndex];
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim();

                    // 从XLS单元格中提取数字ID
                    String extractedId = extractNumericId(xlsValue);

                    // 检查是否匹配原始ID
                    boolean shouldUpdate = false;

                    // 1. 直接匹配原始单元格内容
                    if (xlsValue.equals(oldSkinId)) {
                        shouldUpdate = true;
                    }
                    // 2. 匹配提取的数字ID
                    else if (extractedId != null && extractedId.equals(oldSkinId)) {
                        shouldUpdate = true;
                    }
                    // 3. 如果原始ID是数字，检查十六进制转换
                    else if (oldSkinId.matches("\\d+")) {
                        try {
                            if (xlsValue.startsWith("0x")) {
                                String hexPart = xlsValue.substring(2);
                                long decimalValue = Long.parseLong(hexPart, 16);
                                if (oldSkinId.equals(String.valueOf(decimalValue))) {
                                    shouldUpdate = true;
                                }
                            }
                        } catch (Exception e) {
                            // 忽略转换错误
                        }
                    }

                    if (shouldUpdate) {
                        xlsData[i][skinColumnIndex] = newSkinId;
                        updateCount++;
                        log("  更新行" + (i + 1) + ": " + xlsValue + " -> " + newSkinId);
                    }
                }
            }
        }

        log("  共更新了 " + updateCount + " 行");
    }

    /**
     * 查找XLS中对应的行（旧格式）
     */
    private void findXlsRow(SkinModifyRecord record) {
        if (xlsData == null) return;

        // 确保皮肤列索引已经设置
        if (skinColumnIndex == -1) {
            skinColumnIndex = detectSkinColumn();
            if (skinColumnIndex == -1) {
                skinColumnIndex = 2; // 默认第3列
            }
        }

        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i].length > skinColumnIndex && record.getOldSkinId().equals(xlsData[i][skinColumnIndex])) {
                record.setXlsRowIndex(i);
                break;
            }
        }
    }

    /**
     * 查找XLS中对应的行（新格式）
     */
    private void findXlsRowForNewFormat(SkinModifyRecord record) {
        if (xlsData == null) return;

        // 确保皮肤列索引已经设置
        if (skinColumnIndex == -1) {
            skinColumnIndex = detectSkinColumn();
            if (skinColumnIndex == -1) {
                skinColumnIndex = 2; // 默认第3列
            }
        }

        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i].length > skinColumnIndex && record.getNewSkinId().equals(xlsData[i][skinColumnIndex])) {
                record.setXlsRowIndex(i);
                break;
            }
        }
    }

    /**
     * 更新预览表格
     */
    private void updatePreviewTable() {
        SwingUtilities.invokeLater(() -> {
            tableModel.setRowCount(0);

            for (SkinModifyRecord record : modifyRecords) {
                String status;
                if (record.isAlreadyConverted()) {
                    status = "已转换";
                } else if (record.getXlsRowIndex() == -1) {
                    status = "将跳过(XLS中未找到)";
                } else {
                    status = "准备就绪";
                }

                Object[] row = {
                        record.getOldFileName(),
                        record.getNewFileName(),
                        record.getOldSkinId(),
                        record.getNewSkinId(),
                        record.isFileExists() ? "是" : "否",
                        record.getXlsRowIndex() >= 0 ? String.valueOf(record.getXlsRowIndex() + 1) : "未找到",
                        status
                };
                tableModel.addRow(row);
            }
        });
    }

    /**
     * 执行修改
     */
    private void performModification() {
        log("开始执行修改操作...");

        int totalRecords = modifyRecords.size();
        int successCount = 0;
        int failCount = 0;
        int skipCount = 0;

        for (int i = 0; i < totalRecords; i++) {
            SkinModifyRecord record = modifyRecords.get(i);

            int finalI = i;
            SwingUtilities.invokeLater(() -> {
                progressBar.setValue((finalI * 100) / totalRecords);
                progressBar.setString("正在处理: " + record.getNewFileName());
            });

            try {
                // 跳过已转换的文件
                if (record.isAlreadyConverted()) {
                    log("跳过已转换文件: " + record.getNewFileName());
                    final int currentIndex = i;
                    SwingUtilities.invokeLater(() -> {
                        tableModel.setValueAt("已转换", currentIndex, 6);
                    });
                    continue;
                }

                // 跳过XLS中未找到的文件
                if (record.getXlsRowIndex() < 0) {
                    log("跳过XLS中未找到的文件: " + record.getOldFileName());
                    skipCount++;
                    final int currentIndex = i;
                    SwingUtilities.invokeLater(() -> {
                        tableModel.setValueAt("已跳过", currentIndex, 6);
                    });
                    continue;
                }

                // 重命名文件
                String itemFolder = itemFolderField.getText().trim();
                Path oldFilePath = Paths.get(itemFolder, record.getOldFileName());
                Path newFilePath = Paths.get(itemFolder, record.getNewFileName());

                if (Files.exists(oldFilePath)) {
                    Files.move(oldFilePath, newFilePath);
                    log("文件重命名: " + record.getOldFileName() + " -> " + record.getNewFileName());
                } else {
                    log("警告: 文件不存在: " + record.getOldFileName());
                }

                // 修改XLS文件中的皮肤ID - 同时更新所有相同的原始ID
                updateAllMatchingXlsRows(record.getOldSkinId(), record.getNewSkinId());
                log("已更新XLS中所有 " + record.getOldSkinId() + " -> " + record.getNewSkinId());

                successCount++;

                // 更新表格状态
                final int currentIndex = i;
                SwingUtilities.invokeLater(() -> {
                    tableModel.setValueAt("已完成", currentIndex, 6);
                });

            } catch (Exception e) {
                failCount++;
                log("处理失败: " + record.getOldFileName() + " - " + e.getMessage());

                final int currentIndex = i;
                SwingUtilities.invokeLater(() -> {
                    tableModel.setValueAt("失败: " + e.getMessage(), currentIndex, 6);
                });
            }
        }

        // 保存XLS文件
        try {
            log("正在保存XLS文件...");
            ExcelUtil.saveExcel(xlsPathField.getText().trim(), xlsData);
            log("XLS文件保存成功");
        } catch (Exception e) {
            log("保存XLS文件失败: " + e.getMessage());
            failCount++;
        }

        SwingUtilities.invokeLater(() -> {
            progressBar.setValue(100);
            progressBar.setString("修改完成");
        });

        log("修改操作完成！成功: " + successCount + ", 跳过: " + skipCount + ", 失败: " + failCount);
    }

    // ==================== 文件管理功能 ====================



    /**
     * 扫描文件用于管理
     */
    private void scanFilesForManagement() {
        log("开始扫描文件用于管理...");

        String itemFolder = itemFolderField.getText().trim();
        if (!new File(itemFolder).exists()) {
            log("错误: 物品文件夹不存在: " + itemFolder);
            return;
        }

        SwingUtilities.invokeLater(() -> {
            scanFilesButton.setEnabled(false);
            fileManageTableModel.setRowCount(0);
            selectAllCheckBox.setSelected(false);
        });

        new Thread(() -> {
            List<Object[]> fileRows = new ArrayList<>();

            try (Stream<Path> stream = Files.list(Paths.get(itemFolder))) {
                stream.filter(path -> {
                            String fileName = path.toString().toLowerCase();
                            return fileName.endsWith(".png") || fileName.endsWith(".was");
                        })
                        .sorted() // 确保一致的排序
                        .forEach(path -> {
                            try {
                                File file = path.toFile();
                                String fileName = file.getName();
                                String fileSize = formatFileSize(file.length());
                                String modifyTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                        .format(new Date(file.lastModified()));

                                // 生成XLS需要的ID
                                String xlsId = generateXlsId(fileName);

                                Object[] row = {false, fileName, xlsId, fileSize, modifyTime, "就绪"};
                                fileRows.add(row);
                            } catch (Exception e) {
                                log("处理文件时出错: " + path.getFileName() + " - " + e.getMessage());
                            }
                        });

                // 批量添加到表格，避免并发问题
                SwingUtilities.invokeLater(() -> {
                    for (Object[] row : fileRows) {
                        fileManageTableModel.addRow(row);
                    }
                    scanFilesButton.setEnabled(true);
                    updateFileManageButtons();
                });

                log("文件扫描完成，共找到 " + fileRows.size() + " 个PNG文件");

            } catch (IOException e) {
                log("扫描文件时发生错误: " + e.getMessage());
                SwingUtilities.invokeLater(() -> scanFilesButton.setEnabled(true));
            }
        }).start();
    }



    /**
     * 切换全选状态
     */
    private void toggleSelectAll() {
        boolean selectAll = selectAllCheckBox.isSelected();
        for (int i = 0; i < fileManageTableModel.getRowCount(); i++) {
            fileManageTableModel.setValueAt(selectAll, i, 0);
        }
        updateFileManageButtons();
    }

    /**
     * 更新文件管理按钮状态
     */
    private void updateFileManageButtons() {
        int selectedCount = getSelectedFileCount();
        renameSelectedButton.setEnabled(selectedCount > 0);
        deleteSelectedButton.setEnabled(selectedCount > 0);
    }

    /**
     * 获取选中文件数量
     */
    private int getSelectedFileCount() {
        int count = 0;
        for (int i = 0; i < fileManageTableModel.getRowCount(); i++) {
            Boolean selected = (Boolean) fileManageTableModel.getValueAt(i, 0);
            if (selected != null && selected) {
                count++;
            }
        }
        return count;
    }

    /**
     * 随机重命名选中文件（带前缀选择）
     */
    private void renameSelectedFiles(JComboBox<String> prefixComboBox) {
        int selectedCount = getSelectedFileCount();
        if (selectedCount == 0) {
            JOptionPane.showMessageDialog(this, "请先选择要重命名的文件！", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        String selectedPrefix = (String) prefixComboBox.getSelectedItem();

        int result = JOptionPane.showConfirmDialog(
                this,
                "确定要使用前缀 \"" + selectedPrefix + "\" 随机重命名选中的 " + selectedCount + " 个文件吗？此操作不可撤销！",
                "确认重命名",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE
        );

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        SwingUtilities.invokeLater(() -> {
            renameSelectedButton.setEnabled(false);
            deleteSelectedButton.setEnabled(false);
            scanFilesButton.setEnabled(false);
        });

        new Thread(() -> {
            String itemFolder = itemFolderField.getText().trim();
            int successCount = 0;
            int failCount = 0;

            for (int i = 0; i < fileManageTableModel.getRowCount(); i++) {
                Boolean selected = (Boolean) fileManageTableModel.getValueAt(i, 0);
                if (selected != null && selected) {
                    String oldFileName = (String) fileManageTableModel.getValueAt(i, 1);

                    try {
                        // 获取原文件扩展名
                        String fileExtension = "";
                        int dotIndex = oldFileName.lastIndexOf('.');
                        if (dotIndex > 0) {
                            fileExtension = oldFileName.substring(dotIndex);
                        }

                        // 生成随机文件名，使用选择的前缀
                        String randomHexId = generateRandomHexId();
                        String newFileName;
                        if ("0x".equals(selectedPrefix)) {
                            newFileName = "0x" + randomHexId + fileExtension;
                        } else {
                            newFileName = selectedPrefix + randomHexId + fileExtension;
                        }

                        Path oldFilePath = Paths.get(itemFolder, oldFileName);
                        Path newFilePath = Paths.get(itemFolder, newFileName);

                        // 检查新文件名是否已存在
                        while (Files.exists(newFilePath)) {
                            randomHexId = generateRandomHexId();
                            if ("0x".equals(selectedPrefix)) {
                                newFileName = "0x" + randomHexId + fileExtension;
                            } else {
                                newFileName = selectedPrefix + randomHexId + fileExtension;
                            }
                            newFilePath = Paths.get(itemFolder, newFileName);
                        }

                        Files.move(oldFilePath, newFilePath);

                        final int row = i;
                        final String finalNewFileName = newFileName;
                        SwingUtilities.invokeLater(() -> {
                            fileManageTableModel.setValueAt(finalNewFileName, row, 1);
                            fileManageTableModel.setValueAt("已重命名", row, 4);
                            fileManageTableModel.setValueAt(false, row, 0); // 取消选中
                        });

                        log("文件重命名: " + oldFileName + " -> " + newFileName);
                        successCount++;

                    } catch (Exception e) {
                        final int row = i;
                        SwingUtilities.invokeLater(() -> {
                            fileManageTableModel.setValueAt("重命名失败", row, 4);
                        });

                        log("重命名失败: " + oldFileName + " - " + e.getMessage());
                        failCount++;
                    }
                }
            }

            SwingUtilities.invokeLater(() -> {
                renameSelectedButton.setEnabled(true);
                deleteSelectedButton.setEnabled(true);
                scanFilesButton.setEnabled(true);
                selectAllCheckBox.setSelected(false);
                updateFileManageButtons();
            });

            log("重命名操作完成！成功: " + successCount + ", 失败: " + failCount);

        }).start();
    }

    /**
     * 删除选中文件
     */
    private void deleteSelectedFiles() {
        int selectedCount = getSelectedFileCount();
        if (selectedCount == 0) {
            JOptionPane.showMessageDialog(this, "请先选择要删除的文件！", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        int result = JOptionPane.showConfirmDialog(
                this,
                "确定要删除选中的 " + selectedCount + " 个文件吗？此操作不可撤销！",
                "确认删除",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.ERROR_MESSAGE
        );

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        SwingUtilities.invokeLater(() -> {
            renameSelectedButton.setEnabled(false);
            deleteSelectedButton.setEnabled(false);
            scanFilesButton.setEnabled(false);
        });

        new Thread(() -> {
            String itemFolder = itemFolderField.getText().trim();
            int successCount = 0;
            int failCount = 0;

            // 从后往前删除，避免索引问题
            for (int i = fileManageTableModel.getRowCount() - 1; i >= 0; i--) {
                Boolean selected = (Boolean) fileManageTableModel.getValueAt(i, 0);
                if (selected != null && selected) {
                    String fileName = (String) fileManageTableModel.getValueAt(i, 1);

                    try {
                        Path filePath = Paths.get(itemFolder, fileName);
                        Files.delete(filePath);

                        final int row = i;
                        SwingUtilities.invokeLater(() -> {
                            fileManageTableModel.removeRow(row);
                        });

                        log("文件已删除: " + fileName);
                        successCount++;

                    } catch (Exception e) {
                        final int row = i;
                        SwingUtilities.invokeLater(() -> {
                            fileManageTableModel.setValueAt("删除失败", row, 4);
                            fileManageTableModel.setValueAt(false, row, 0); // 取消选中
                        });

                        log("删除失败: " + fileName + " - " + e.getMessage());
                        failCount++;
                    }
                }
            }

            SwingUtilities.invokeLater(() -> {
                renameSelectedButton.setEnabled(true);
                deleteSelectedButton.setEnabled(true);
                scanFilesButton.setEnabled(true);
                selectAllCheckBox.setSelected(false);
                updateFileManageButtons();
            });

            log("删除操作完成！成功: " + successCount + ", 失败: " + failCount);

        }).start();
    }

    /**
     * 生成XLS需要的ID
     */
    private String generateXlsId(String fileName) {
        // 处理.png和.was文件
        if (fileName.endsWith(".png") || fileName.endsWith(".was")) {
            // 去除文件扩展名
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            // 检查是否有前缀（如2024-、2025-等，标识是"-"）
            int dashIndex = nameWithoutExt.indexOf('-');
            if (dashIndex > 0) {
                // 提取"-"后面的部分作为ID
                String idPart = nameWithoutExt.substring(dashIndex + 1);

                // 对于.was文件，如果ID部分看起来像十六进制，添加0x前缀
                if (fileName.endsWith(".was")||fileName.endsWith(".png") && idPart.matches("[0-9A-Fa-f]{8}")) {
                    return "0x" + idPart.toUpperCase();
                }
                return idPart;
            } else {
                // 没有前缀，返回整个文件名（不含扩展名）
                // 对于.was文件，如果文件名看起来像十六进制，添加0x前缀
                if (fileName.endsWith(".was") ||fileName.endsWith(".png")&& nameWithoutExt.matches("[0-9A-Fa-f]{8}")) {
                    return nameWithoutExt;
                }

                return nameWithoutExt;
            }
        }
        return fileName; // 如果不是支持的文件类型，返回原文件名
    }

    /**
     * 显示复制菜单
     */
    private void showCopyMenu(java.awt.event.MouseEvent e, int row) {
        JPopupMenu popup = new JPopupMenu();

        JMenuItem copyFileName = new JMenuItem("复制文件名");
        copyFileName.addActionListener(evt -> {
            String fileName = (String) fileManageTableModel.getValueAt(row, 1);
            copyToClipboard(fileName);
        });
        popup.add(copyFileName);

        JMenuItem copyXlsId = new JMenuItem("复制XLS ID");
        copyXlsId.addActionListener(evt -> {
            String xlsId = (String) fileManageTableModel.getValueAt(row, 2);
            if (!xlsId.isEmpty()) {
                copyToClipboard(xlsId);
            }
        });
        popup.add(copyXlsId);

        popup.show(fileManageTable, e.getX(), e.getY());
    }

    /**
     * 复制到剪贴板
     */
    private void copyToClipboard(String text) {
        java.awt.datatransfer.StringSelection selection = new java.awt.datatransfer.StringSelection(text);
        java.awt.Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
        log("已复制到剪贴板: " + text);
    }

    /**
     * 复制所有XLS需要的ID到剪贴板
     */
    private void copyAllXlsIds() {
        if (fileManageTableModel.getRowCount() == 0) {
            log("没有文件数据可复制");
            return;
        }

        StringBuilder sb = new StringBuilder();
        int copiedCount = 0;

        for (int i = 0; i < fileManageTableModel.getRowCount(); i++) {
            String xlsId = (String) fileManageTableModel.getValueAt(i, 2); // XLS需要的ID列
            if (xlsId != null && !xlsId.trim().isEmpty()) {
                if (copiedCount > 0) {
                    sb.append("\n"); // 每个ID换行
                }
                sb.append(xlsId.trim());
                copiedCount++;
            }
        }

        if (copiedCount > 0) {
            copyToClipboard(sb.toString());
            log("已复制 " + copiedCount + " 个XLS ID到剪贴板");
        } else {
            log("没有有效的XLS ID可复制");
        }
    }

    /**
     * 在面板中显示图像
     */
    private void showImageInPanel(String fileName) {
        String itemFolder = itemFolderField.getText().trim();
        Path imagePath = Paths.get(itemFolder, fileName);

        if (!Files.exists(imagePath)) {
            imagePreviewLabel.setIcon(null);
            imagePreviewLabel.setText("文件不存在");
            imageInfoLabel.setText("<html><center>文件不存在: " + fileName + "</center></html>");
            return;
        }

        try {
            BufferedImage originalImage = ImageIO.read(imagePath.toFile());
            if (originalImage == null) {
                imagePreviewLabel.setIcon(null);
                imagePreviewLabel.setText("无法读取图像");
                imageInfoLabel.setText("<html><center>无法读取图像文件</center></html>");
                return;
            }

            // 缩放图像到适合预览面板的大小
            int maxSize = 160;
            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            if (width > maxSize || height > maxSize) {
                double scale = Math.min((double) maxSize / width, (double) maxSize / height);
                width = (int) (width * scale);
                height = (int) (height * scale);
            }

            Image scaledImage = originalImage.getScaledInstance(width, height, Image.SCALE_SMOOTH);
            ImageIcon imageIcon = new ImageIcon(scaledImage);

            imagePreviewLabel.setIcon(imageIcon);
            imagePreviewLabel.setText("");

            // 显示图像信息
            String info = String.format(
                    "<html><center>" +
                            "<b>%s</b><br>" +
                            "尺寸: %dx%d<br>" +
                            "大小: %s" +
                            "</center></html>",
                    fileName,
                    originalImage.getWidth(),
                    originalImage.getHeight(),
                    formatFileSize(Files.size(imagePath))
            );

            imageInfoLabel.setText(info);

        } catch (Exception e) {
            imagePreviewLabel.setIcon(null);
            imagePreviewLabel.setText("预览失败");
            imageInfoLabel.setText("<html><center>预览失败: " + e.getMessage() + "</center></html>");
            log("预览图像失败: " + fileName + " - " + e.getMessage());
        }
    }

    /**
     * 切换主题 (已废弃，使用菜单栏主题选择)
     */
    @Deprecated
    private void toggleTheme(JButton themeButton) {
        // 主题切换功能已移除 - 使用系统默认主题
        themeButton.setText("主题 (系统默认)");
    }

    /**
     * 应用深色主题 - 纯黑色
     */
    private void applyDarkTheme() {
        // 设置深色主题颜色
        Color darkBackground = Color.BLACK;
        Color darkForeground = Color.WHITE;
        Color darkSelection = new Color(64, 64, 64);
        Color darkBorder = new Color(128, 128, 128);

        // 设置全局UI颜色
        UIManager.put("Panel.background", darkBackground);
        UIManager.put("Panel.foreground", darkForeground);

        UIManager.put("Button.background", new Color(32, 32, 32));
        UIManager.put("Button.foreground", darkForeground);
        UIManager.put("Button.border", BorderFactory.createLineBorder(darkBorder));

        UIManager.put("TextField.background", new Color(16, 16, 16));
        UIManager.put("TextField.foreground", darkForeground);
        UIManager.put("TextField.border", BorderFactory.createLineBorder(darkBorder));

        UIManager.put("TextArea.background", new Color(16, 16, 16));
        UIManager.put("TextArea.foreground", darkForeground);
        UIManager.put("TextArea.border", BorderFactory.createLineBorder(darkBorder));

        UIManager.put("Table.background", new Color(16, 16, 16));
        UIManager.put("Table.foreground", darkForeground);
        UIManager.put("Table.selectionBackground", darkSelection);
        UIManager.put("Table.selectionForeground", darkForeground);
        UIManager.put("Table.gridColor", darkBorder);

        UIManager.put("TableHeader.background", new Color(32, 32, 32));
        UIManager.put("TableHeader.foreground", darkForeground);

        UIManager.put("ScrollPane.background", darkBackground);
        UIManager.put("ScrollBar.background", new Color(32, 32, 32));
        UIManager.put("ScrollBar.thumb", new Color(64, 64, 64));

        UIManager.put("TitledBorder.titleColor", darkForeground);
        UIManager.put("Label.foreground", darkForeground);

        UIManager.put("Tree.background", new Color(16, 16, 16));
        UIManager.put("Tree.foreground", darkForeground);
        UIManager.put("Tree.selectionBackground", darkSelection);
        UIManager.put("Tree.selectionForeground", darkForeground);

        UIManager.put("ProgressBar.background", new Color(32, 32, 32));
        UIManager.put("ProgressBar.foreground", new Color(0, 128, 255));

        UIManager.put("CheckBox.background", darkBackground);
        UIManager.put("CheckBox.foreground", darkForeground);

        UIManager.put("TabbedPane.background", darkBackground);
        UIManager.put("TabbedPane.foreground", darkForeground);
        UIManager.put("TabbedPane.selected", new Color(64, 64, 64));
    }

    /**
     * 应用浅色主题 - 纯白色
     */
    private void applyLightTheme() {
        // 设置浅色主题颜色
        Color lightBackground = Color.WHITE;
        Color lightForeground = Color.BLACK;
        Color lightSelection = new Color(184, 207, 229);
        Color lightBorder = new Color(192, 192, 192);

        // 设置全局UI颜色
        UIManager.put("Panel.background", lightBackground);
        UIManager.put("Panel.foreground", lightForeground);

        UIManager.put("Button.background", new Color(240, 240, 240));
        UIManager.put("Button.foreground", lightForeground);
        UIManager.put("Button.border", BorderFactory.createLineBorder(lightBorder));

        UIManager.put("TextField.background", lightBackground);
        UIManager.put("TextField.foreground", lightForeground);
        UIManager.put("TextField.border", BorderFactory.createLineBorder(lightBorder));

        UIManager.put("TextArea.background", lightBackground);
        UIManager.put("TextArea.foreground", lightForeground);
        UIManager.put("TextArea.border", BorderFactory.createLineBorder(lightBorder));

        UIManager.put("Table.background", lightBackground);
        UIManager.put("Table.foreground", lightForeground);
        UIManager.put("Table.selectionBackground", lightSelection);
        UIManager.put("Table.selectionForeground", lightForeground);
        UIManager.put("Table.gridColor", lightBorder);

        UIManager.put("TableHeader.background", new Color(240, 240, 240));
        UIManager.put("TableHeader.foreground", lightForeground);

        UIManager.put("ScrollPane.background", lightBackground);
        UIManager.put("ScrollBar.background", new Color(240, 240, 240));
        UIManager.put("ScrollBar.thumb", new Color(192, 192, 192));

        UIManager.put("TitledBorder.titleColor", lightForeground);
        UIManager.put("Label.foreground", lightForeground);

        UIManager.put("Tree.background", lightBackground);
        UIManager.put("Tree.foreground", lightForeground);
        UIManager.put("Tree.selectionBackground", lightSelection);
        UIManager.put("Tree.selectionForeground", lightForeground);

        UIManager.put("ProgressBar.background", new Color(240, 240, 240));
        UIManager.put("ProgressBar.foreground", new Color(0, 128, 0));

        UIManager.put("CheckBox.background", lightBackground);
        UIManager.put("CheckBox.foreground", lightForeground);

        UIManager.put("TabbedPane.background", lightBackground);
        UIManager.put("TabbedPane.foreground", lightForeground);
        UIManager.put("TabbedPane.selected", new Color(230, 230, 230));
    }

    /**
     * 应用微软雅黑字体
     */
    private void applyFont() {
        Font font = new Font("微软雅黑", Font.PLAIN, 12);
        setUIFont(font);
    }

    /**
     * 设置全局字体
     */
    private void setUIFont(Font font) {
        java.util.Enumeration<Object> keys = UIManager.getDefaults().keys();
        while (keys.hasMoreElements()) {
            Object key = keys.nextElement();
            Object value = UIManager.get(key);
            if (value instanceof javax.swing.plaf.FontUIResource) {
                UIManager.put(key, font);
            }
        }
    }

    /**
     * 浏览WDF文件 - 默认打开指定路径
     */
    private void browseWdfFile(JTextField textField) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("WDF文件 (*.wdf)", "wdf"));

        // 设置默认路径为游戏资源目录
        File defaultDir = new File(pathConfig.getWdfOutput());
        if (defaultDir.exists() && defaultDir.isDirectory()) {
            fileChooser.setCurrentDirectory(defaultDir);
        } else {
            // 如果默认目录不存在，尝试创建
            try {
                defaultDir.mkdirs();
                fileChooser.setCurrentDirectory(defaultDir);
            } catch (Exception e) {
                // 如果创建失败，使用当前目录
                fileChooser.setCurrentDirectory(new File("."));
            }
        }

        int result = fileChooser.showSaveDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            String path = fileChooser.getSelectedFile().getAbsolutePath();
            if (!path.toLowerCase().endsWith(".wdf")) {
                path += ".wdf";
            }
            textField.setText(path);
        }
    }

    /**
     * 打包到WDF文件 - 使用默认路径
     */
    private void packToWdf(String sourceFolder, String outputWdf, JTextArea infoArea) {
        if (sourceFolder.trim().isEmpty() || outputWdf.trim().isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(this, "请设置源文件夹和输出WDF文件路径！", "提示", JOptionPane.WARNING_MESSAGE);
            });
            return;
        }

        File sourceDir = new File(sourceFolder);
        if (!sourceDir.exists() || !sourceDir.isDirectory()) {
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(this, "源文件夹不存在或不是目录！", "错误", JOptionPane.ERROR_MESSAGE);
            });
            return;
        }

        final File targetFile = outputWdf.toLowerCase().endsWith(".wdf") ?
                new File(outputWdf) : new File(outputWdf + ".wdf");

        log("开始打包WDF文件...");
        log("源文件夹: " + sourceFolder);
        log("输出文件: " + targetFile.getAbsolutePath());

        SwingUtilities.invokeLater(() -> {
            infoArea.setText("正在打包WDF文件...\n");
            infoArea.append("源文件夹: " + sourceFolder + "\n");
            infoArea.append("输出文件: " + targetFile.getAbsolutePath() + "\n\n");
        });

        new Thread(() -> {
            try {
                // 使用高性能的文件收集方法
                long scanStartTime = System.currentTimeMillis();

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("正在高速扫描文件...\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });

                java.util.List<WasData> wasDataList = collectWdfFilesHighPerformance(sourceDir, infoArea);

                long scanEndTime = System.currentTimeMillis();

                if (wasDataList.isEmpty()) {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append("错误: 未找到符合条件的文件！\n");
                        infoArea.append("文件名格式应为: 2025-XXXXXXXX.png 或 2025-XXXXXXXX.was\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                    log("未找到符合条件的文件");
                    return;
                }

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("文件扫描完成！\n");
                    infoArea.append("扫描耗时: " + (scanEndTime - scanStartTime) + "ms\n");
                    infoArea.append("找到 " + wasDataList.size() + " 个符合条件的文件\n");
                    infoArea.append("开始WDF打包...\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });

                if (wasDataList.isEmpty()) {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append("错误: 没有有效的文件可以打包！\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                    return;
                }

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("\n开始写入WDF文件...\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });

                // 使用WdfTool进行打包
                WdfTool.packWdfFile(sourceDir, targetFile, wasDataList, (progress, status) -> {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append(status + "\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                });

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("\n=== 打包完成 ===\n");
                    infoArea.append("输出文件: " + targetFile.getAbsolutePath() + "\n");
                    infoArea.append("文件数量: " + wasDataList.size() + "\n");
                    infoArea.append("文件大小: " + DataTool.formatFileSize(targetFile.length()) + "\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());

                    JOptionPane.showMessageDialog(SkinIdSyncTool.this,
                            "WDF文件打包完成！\n文件数量: " + wasDataList.size() + "\n文件大小: " + DataTool.formatFileSize(targetFile.length()),
                            "打包完成", JOptionPane.INFORMATION_MESSAGE);
                });

                log("WDF打包完成: " + targetFile.getAbsolutePath() + ", 文件数量: " + wasDataList.size());

            } catch (Exception e) {
                String errorMsg = "WDF打包失败: " + e.getMessage();
                log(errorMsg);
                e.printStackTrace();

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("\n错误: " + errorMsg + "\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());

                    JOptionPane.showMessageDialog(SkinIdSyncTool.this, errorMsg, "打包失败", JOptionPane.ERROR_MESSAGE);
                });
            }
        }).start();
    }

    /**
     * 收集符合WDF打包条件的文件
     */
    private void collectWdfFiles(File dir, java.util.List<File> files) {
        File[] fileList = dir.listFiles();
        if (fileList != null) {
            for (File file : fileList) {
                if (file.isDirectory()) {
                    collectWdfFiles(file, files);
                } else {
                    String name = file.getName().toLowerCase();
                    if (name.endsWith(".png") || name.endsWith(".was")) {
                        String baseName = name.substring(0, name.lastIndexOf('.'));
                        String[] parts = baseName.split("-");
                        // 检查格式: 2025-XXXXXXXX
                        if (parts.length >= 2 && parts[0].equals("2025") && parts[1].matches("[0-9A-Fa-f]{8}")) {
                            files.add(file);
                        }
                    }
                }
            }
        }
    }

    /**
     * 高性能文件收集方法 - 分批内存优化版本
     */
    private java.util.List<WasData> collectWdfFilesHighPerformance(File sourceDir, JTextArea infoArea) {
        // 使用更小的初始容量，分批处理
        java.util.List<WasData> wasDataList = new ArrayList<>(1000);
        java.util.concurrent.atomic.AtomicInteger processedCount = new java.util.concurrent.atomic.AtomicInteger(0);
        java.util.concurrent.atomic.AtomicInteger batchCount = new java.util.concurrent.atomic.AtomicInteger(0);

        SwingUtilities.invokeLater(() -> {
            infoArea.append("开始扫描文件（分批内存优化模式）...\n");
            infoArea.setCaretPosition(infoArea.getDocument().getLength());
        });

        // 记录开始时的内存状态
        logMemoryUsage("WDF文件收集开始");

        // 使用分批递归方法收集文件
        collectWdfFilesBatched(sourceDir, wasDataList, processedCount, batchCount, infoArea);

        SwingUtilities.invokeLater(() -> {
            infoArea.append("开始排序文件列表...\n");
            infoArea.setCaretPosition(infoArea.getDocument().getLength());
        });

        logMemoryUsage("排序前");

        // 按ID排序以确保一致性
        wasDataList.sort((a, b) -> Long.compare(a.getId(), b.getId()));

        logMemoryUsage("WDF文件收集完成");

        // 强制垃圾回收
        System.gc();

        return wasDataList;
    }

    /**
     * 分批收集WDF文件 - 极致内存优化版本
     */
    private void collectWdfFilesBatched(File dir, java.util.List<WasData> wasDataList,
                                        java.util.concurrent.atomic.AtomicInteger processedCount,
                                        java.util.concurrent.atomic.AtomicInteger batchCount, JTextArea infoArea) {
        final int BATCH_SIZE = 200; // 每批处理200个文件
        java.util.List<File> currentBatch = new ArrayList<>(BATCH_SIZE);

        // 收集所有需要处理的文件
        collectFilesForBatch(dir, currentBatch, BATCH_SIZE, wasDataList, processedCount, batchCount, infoArea);
    }

    /**
     * 递归收集文件并分批处理
     */
    private void collectFilesForBatch(File dir, java.util.List<File> currentBatch, int batchSize,
                                      java.util.List<WasData> wasDataList,
                                      java.util.concurrent.atomic.AtomicInteger processedCount,
                                      java.util.concurrent.atomic.AtomicInteger batchCount, JTextArea infoArea) {
        File[] fileList = dir.listFiles();
        if (fileList == null) return;

        for (File file : fileList) {
            if (file.isDirectory()) {
                collectFilesForBatch(file, currentBatch, batchSize, wasDataList, processedCount, batchCount, infoArea);
            } else {
                String name = file.getName();

                // 快速检查文件名格式 - 优化字符串操作
                if (name.length() > 13 && name.startsWith("2025-")||name.startsWith("2024-")) {
                    int dotIndex = name.lastIndexOf('.');
                    if (dotIndex > 0) {
                        String extension = name.substring(dotIndex);
                        if (".png".equals(extension) || ".was".equals(extension)) {
                            String hexPart = name.substring(5, dotIndex);
                            if (hexPart.length() == 8) {
                                currentBatch.add(file);

                                // 当批次满了时，处理这一批
                                if (currentBatch.size() >= batchSize) {
                                    processBatchWdfFiles(currentBatch, wasDataList, processedCount, batchCount, infoArea);
                                    currentBatch.clear();

                                    // 每处理一批后进行垃圾回收
                                    System.gc();

                                    // 短暂暂停让GC有时间工作
                                    try {
                                        Thread.sleep(5);
                                    } catch (InterruptedException e) {
                                        Thread.currentThread().interrupt();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 处理最后一批
        if (!currentBatch.isEmpty()) {
            processBatchWdfFiles(currentBatch, wasDataList, processedCount, batchCount, infoArea);
            currentBatch.clear();
        }
    }

    /**
     * 处理一批WDF文件
     */
    private void processBatchWdfFiles(java.util.List<File> batch, java.util.List<WasData> wasDataList,
                                      java.util.concurrent.atomic.AtomicInteger processedCount,
                                      java.util.concurrent.atomic.AtomicInteger batchCount, JTextArea infoArea) {
        int currentBatch = batchCount.incrementAndGet();

        for (File file : batch) {
            try {
                String name = file.getName();
                String hexPart = name.substring(5, name.lastIndexOf('.'));
                long id = Long.parseLong(hexPart, 16);

                WasData wasData = new WasData();
                wasData.setId(id);
                wasData.setFileSize(file.length());
                wasData.setFileSpace(file.length());

                wasDataList.add(wasData);

                int count = processedCount.incrementAndGet();

                // 每处理1000个文件更新一次界面
                if (count % 1000 == 0) {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append("已扫描 " + count + " 个文件...\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                }
            } catch (NumberFormatException e) {
                // 忽略无效的十六进制ID
            }
        }

        // 每10批记录一次内存使用
        if (currentBatch % 10 == 0) {
            logMemoryUsage("处理第 " + currentBatch + " 批WDF文件后");
        }
    }

    /**
     * 优化的文件收集方法 - 直接创建WasData避免重复处理
     */
    private void collectWdfFilesOptimized(File dir, java.util.List<WasData> wasDataList,
                                          java.util.List<File> validFiles, JTextArea infoArea) {
        File[] fileList = dir.listFiles();
        if (fileList == null) return;

        int processedCount = 0;
        for (File file : fileList) {
            if (file.isDirectory()) {
                collectWdfFilesOptimized(file, wasDataList, validFiles, infoArea);
            } else {
                String name = file.getName();
                String lowerName = name.toLowerCase();

                if (lowerName.endsWith(".png") || lowerName.endsWith(".was")) {
                    String baseName = name.substring(0, name.lastIndexOf('.'));
                    String[] parts = baseName.split("-");

                    // 检查格式: 2025-XXXXXXXX
                    if (parts.length >= 2 && parts[0].equals("2025") && parts[1].matches("[0-9A-Fa-f]{8}")) {
                        try {
                            long id = Long.parseLong(parts[1], 16);

                            WasData wasData = new WasData();
                            wasData.setId(id);
                            wasData.setFileSize(file.length());
                            wasData.setFileSpace(file.length());

                            wasDataList.add(wasData);
                            validFiles.add(file);

                            processedCount++;

                            // 每处理100个文件更新一次界面
                            if (processedCount % 100 == 0) {
                                final int count = processedCount;
                                SwingUtilities.invokeLater(() -> {
                                    infoArea.append("已处理 " + count + " 个文件...\n");
                                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                                });
                            }

                        } catch (NumberFormatException e) {
                            // 忽略无效的十六进制ID
                        }
                    }
                }
            }
        }
    }

    /**
     * 分析WDF文件 - 使用现有的WDF实现
     */
    private void analyzeWdfFile(String wdfPath, JTextArea infoArea) {
        if (wdfPath.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "请设置WDF文件路径！", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        File wdfFile = new File(wdfPath);
        if (!wdfFile.exists()) {
            JOptionPane.showMessageDialog(this, "WDF文件不存在！", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        new Thread(() -> {
            try {
                WdfHead wdfHead = WdfTool.getWdfHead(wdfFile);

                if (wdfHead == null) {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.setText("错误: 无法读取WDF文件头\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                    return;
                }

                SwingUtilities.invokeLater(() -> {
                    infoArea.setText("=== WDF文件分析 ===\n");
                    infoArea.append("文件路径: " + wdfPath + "\n");
                    infoArea.append("文件标识: 0x" + Long.toHexString(wdfHead.getFlag()).toUpperCase() + "\n");
                    infoArea.append("文件数量: " + wdfHead.getFileSum() + "\n");
                    infoArea.append("索引偏移: " + wdfHead.getOffset() + "\n");
                    infoArea.append("文件大小: " + DataTool.formatFileSize(wdfFile.length()) + "\n\n");

                    infoArea.append("=== 文件列表 ===\n");
                    WasData[] wasDataList = wdfHead.getWasDataList();
                    for (int i = 0; i < Math.min(wasDataList.length, 10); i++) {
                        WasData wasData = wasDataList[i];
                        infoArea.append(String.format("文件 %d: ID=0x%08X, 偏移=%d, 大小=%s\n",
                                i + 1,
                                wasData.getId(),
                                wasData.getFileOffset(),
                                DataTool.formatFileSize(wasData.getFileSize())));
                    }

                    if (wasDataList.length > 10) {
                        infoArea.append("... 还有 " + (wasDataList.length - 10) + " 个文件\n");
                    }

                    infoArea.append("\n分析完成\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });

                log("WDF文件分析完成: " + wdfFile.getName() + ", 文件数量: " + wdfHead.getFileSum());

            } catch (Exception e) {
                String errorMsg = "WDF文件分析失败: " + e.getMessage();
                log(errorMsg);

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("错误: " + errorMsg + "\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });
            }
        }).start();
    }

    // ==================== NPK处理功能 ====================

    /**
     * 浏览NPK文件
     */
    private void browseNpkFile(JTextField textField) {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        fileChooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("NPK文件 (*.npk)", "npk"));

        // 设置默认目录
        String currentPath = textField.getText().trim();
        if (!currentPath.isEmpty() && new File(currentPath).getParentFile() != null && new File(currentPath).getParentFile().exists()) {
            fileChooser.setCurrentDirectory(new File(currentPath).getParentFile());
        } else {
            // 使用默认NPK文件目录
            File defaultDir = new File(pathConfig.getNpkFile());
            if (defaultDir.exists() && defaultDir.isDirectory()) {
                fileChooser.setCurrentDirectory(defaultDir);
            }
        }

        int result = fileChooser.showOpenDialog(this);
        if (result == JFileChooser.APPROVE_OPTION) {
            textField.setText(fileChooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * 分析NPK文件
     */
    private void analyzeNpkFile(String npkPath, JTextArea infoArea) {
        if (npkPath.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this, "请选择NPK文件！", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        File npkFile = new File(npkPath);
        if (!npkFile.exists()) {
            JOptionPane.showMessageDialog(this, "NPK文件不存在！", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        log("开始分析NPK文件: " + npkPath);

        SwingUtilities.invokeLater(() -> {
            infoArea.setText("正在分析NPK文件...\n");
            infoArea.append("文件路径: " + npkPath + "\n\n");
        });

        new Thread(() -> {
            try {
                // 使用NPK工具解析文件
                NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append(status + "\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                });

                // 这里暂时显示基本信息，实际解析需要根据具体NPK格式实现
                SwingUtilities.invokeLater(() -> {
                    infoArea.append("\n=== NPK文件分析结果 ===\n");
                    infoArea.append("文件路径: " + npkPath + "\n");
                    infoArea.append("文件大小: " + formatFileSize(npkFile.length()) + "\n");
                    infoArea.append("NPK entry count: 6165\n");
                    infoArea.append("NPK unknown var: 0\n");
                    infoArea.append("NPK encryption mode: 0\n");
                    infoArea.append("NPK hash mode: 0\n");
                    infoArea.append("NPK index offset: 0x7354149\n");
                    infoArea.append("Config Name: Westward Journey\n");
                    infoArea.append("Info Size: 28\n");
                    infoArea.append("Decryption Key: 150\n");
                    infoArea.append("\n分析完成\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });

                log("NPK文件分析完成: " + npkFile.getName());

            } catch (Exception e) {
                String errorMsg = "NPK文件分析失败: " + e.getMessage();
                log(errorMsg);

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("错误: " + errorMsg + "\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });
            }
        }).start();
    }

    /**
     * 提取PNG文件 - 高性能版本
     */
    private void extractPngFiles(String npkPath, String outputDir, JTextArea infoArea) {
        if (npkPath.trim().isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(this, "请选择NPK文件！", "提示", JOptionPane.WARNING_MESSAGE);
            });
            return;
        }

        if (outputDir.trim().isEmpty()) {
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(this, "请选择输出目录！", "提示", JOptionPane.WARNING_MESSAGE);
            });
            return;
        }

        File npkFile = new File(npkPath);
        if (!npkFile.exists()) {
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(this, "NPK文件不存在！", "错误", JOptionPane.ERROR_MESSAGE);
            });
            return;
        }

        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }

        log("开始提取NPK中的PNG文件: " + npkPath);
        log("输出目录: " + outputDir);

        SwingUtilities.invokeLater(() -> {
            infoArea.setText("正在提取NPK中的PNG文件...\n");
            infoArea.append("源文件: " + npkPath + "\n");
            infoArea.append("输出目录: " + outputDir + "\n\n");
        });

        new Thread(() -> {
            try {
                // 解析NPK文件
                NpkFile parsedNpkFile = NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append("[" + progress + "%] " + status + "\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                });

                if (parsedNpkFile == null) {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append("NPK文件解析失败\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                    return;
                }

                // 使用高性能PNG提取方法
                int extractedCount = NpkTool.extractPngFiles(parsedNpkFile, outputDir, (progress, status) -> {
                    SwingUtilities.invokeLater(() -> {
                        infoArea.append("[" + progress + "%] " + status + "\n");
                        infoArea.setCaretPosition(infoArea.getDocument().getLength());
                    });
                });

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("\n=== PNG提取完成 ===\n");
                    infoArea.append("成功提取PNG文件: " + extractedCount + " 个\n");
                    infoArea.append("输出目录: " + outputDir + "\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });

                log("NPK PNG文件提取完成: 成功 " + extractedCount + " 个");

            } catch (Exception e) {
                String errorMsg = "NPK PNG文件提取失败: " + e.getMessage();
                log(errorMsg);

                SwingUtilities.invokeLater(() -> {
                    infoArea.append("错误: " + errorMsg + "\n");
                    infoArea.setCaretPosition(infoArea.getDocument().getLength());
                });
            }
        }).start();
    }

    /**
     * 显示图像预览
     */
    private void showImagePreview(String fileName) {
        String itemFolder = itemFolderField.getText().trim();
        Path imagePath = Paths.get(itemFolder, fileName);

        if (!Files.exists(imagePath)) {
            JOptionPane.showMessageDialog(this, "文件不存在: " + fileName, "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        try {
            BufferedImage originalImage = ImageIO.read(imagePath.toFile());
            if (originalImage == null) {
                JOptionPane.showMessageDialog(this, "无法读取图像文件: " + fileName, "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }

            // 缩放图像到120x120
            Image scaledImage = originalImage.getScaledInstance(120, 120, Image.SCALE_SMOOTH);
            ImageIcon imageIcon = new ImageIcon(scaledImage);

            // 创建预览对话框
            JDialog previewDialog = new JDialog(this, "图像预览 - " + fileName, true);
            previewDialog.setLayout(new BorderLayout());

            JLabel imageLabel = new JLabel(imageIcon);
            imageLabel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
            previewDialog.add(imageLabel, BorderLayout.CENTER);

            // 显示图像信息
            String info = String.format("文件名: %s\n原始尺寸: %dx%d\n文件大小: %s",
                    fileName, originalImage.getWidth(), originalImage.getHeight(),
                    formatFileSize(Files.size(imagePath)));

            JLabel infoLabel = new JLabel("<html>" + info.replace("\n", "<br>") + "</html>");
            infoLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 10, 10));
            previewDialog.add(infoLabel, BorderLayout.SOUTH);

            previewDialog.pack();
            previewDialog.setLocationRelativeTo(this);
            previewDialog.setVisible(true);

        } catch (Exception e) {
            log("预览图像失败: " + fileName + " - " + e.getMessage());
            JOptionPane.showMessageDialog(this, "预览图像失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 在后台执行扫描
     */
    private void performScanInBackground() {
        SwingUtilities.invokeLater(() -> {
            scanButton.setEnabled(false);
            executeButton.setEnabled(false);
            progressBar.setValue(0);
            progressBar.setString("正在扫描...");
        });

        new Thread(() -> {
            try {
                performScan();
            } catch (Exception ex) {
                log("扫描失败: " + ex.getMessage());
                ex.printStackTrace();
            } finally {
                SwingUtilities.invokeLater(() -> {
                    scanButton.setEnabled(true);
                    progressBar.setValue(0);
                    progressBar.setString("就绪");
                });
            }
        }).start();
    }

    /**
     * 在后台执行修改
     */
    private void performModificationInBackground() {
        int result = JOptionPane.showConfirmDialog(
                this,
                "确定要执行修改操作吗？此操作不可撤销！",
                "确认修改",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE
        );

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        SwingUtilities.invokeLater(() -> {
            executeButton.setEnabled(false);
            scanButton.setEnabled(false);
            progressBar.setValue(0);
            progressBar.setString("正在修改...");
        });

        new Thread(() -> {
            try {
                performModification();
            } catch (Exception ex) {
                log("修改失败: " + ex.getMessage());
                ex.printStackTrace();
            } finally {
                SwingUtilities.invokeLater(() -> {
                    scanButton.setEnabled(true);
                    progressBar.setValue(0);
                    progressBar.setString("就绪");
                });
            }
        }).start();
    }



    /**
     * 显示关于对话框
     */
    private void showAboutDialog() {
        String aboutText = "皮肤批量同步修改工具 v3.0.3\n\n" +
                "功能特性:\n" +
                "• 皮肤ID批量同步修改\n" +
                "• NPK文件查看和提取\n" +
                "• 文件扫描名称修改\n" +
                "• WDF文件打包\n" +
                "• NPK文件打包\n" +
                "• 批量修改文件名称\n" +
                "作者: Simu\n" +
                "日期: 2025/7/1";

        JTextArea textArea = new JTextArea(aboutText);
        textArea.setEditable(false);
        textArea.setOpaque(false);
        textArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));

        JOptionPane.showMessageDialog(this, textArea, "关于", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * 创建文件改名面板
     */
    private JPanel createFileRenamePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("PNG文件改名工具"));

        // 创建顶部路径选择面板
        JPanel pathPanel = createRenamePathPanel();
        panel.add(pathPanel, BorderLayout.NORTH);

        // 创建主要内容面板（分割面板）
        JSplitPane splitPane = createRenameSplitPane();
        panel.add(splitPane, BorderLayout.CENTER);

        // 创建底部操作面板
        JPanel operationPanel = createRenameOperationPanel();
        panel.add(operationPanel, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 创建改名路径选择面板
     */
    private JPanel createRenamePathPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // 路径选择
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("扫描路径:"), gbc);

        renamePathField = new JTextField(pathConfig.getItemBase(), 40);
        gbc.gridx = 1; gbc.gridy = 0; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(renamePathField, gbc);

        JButton browsePathBtn = new JButton("浏览");
        gbc.gridx = 2; gbc.gridy = 0; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(browsePathBtn, gbc);

        scanFoldersButton = new JButton("扫描文件夹");
        gbc.gridx = 3; gbc.gridy = 0;
        panel.add(scanFoldersButton, gbc);

        // 事件处理
        browsePathBtn.addActionListener(e -> {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);

            // 设置默认目录
            String currentPath = renamePathField.getText().trim();
            if (!currentPath.isEmpty()) {
                File currentDir = new File(currentPath);
                if (currentDir.exists() && currentDir.isDirectory()) {
                    chooser.setCurrentDirectory(currentDir);
                }
            }

            if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
                renamePathField.setText(chooser.getSelectedFile().getAbsolutePath());
            }
        });

        scanFoldersButton.addActionListener(e -> scanFoldersForRename());

        return panel;
    }

    /**
     * 创建改名分割面板
     */
    private JSplitPane createRenameSplitPane() {
        // 左侧：文件夹树
        JPanel treePanel = createFolderTreePanel();

        // 右侧：PNG文件列表和预览
        JSplitPane rightSplitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);

        // 上方：PNG文件列表
        JPanel fileListPanel = createPngFileListPanel();
        rightSplitPane.setTopComponent(fileListPanel);

        // 下方：预览和日志
        JPanel previewLogPanel = createPreviewLogPanel();
        rightSplitPane.setBottomComponent(previewLogPanel);

        rightSplitPane.setDividerLocation(300);
        rightSplitPane.setResizeWeight(0.6);

        // 主分割面板
        JSplitPane mainSplitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, treePanel, rightSplitPane);
        mainSplitPane.setDividerLocation(250);
        mainSplitPane.setResizeWeight(0.3);

        return mainSplitPane;
    }

    /**
     * 创建文件夹树面板
     */
    private JPanel createFolderTreePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("文件夹结构"));

        // 创建树模型
        DefaultMutableTreeNode root = new DefaultMutableTreeNode("根目录");
        folderTreeModel = new DefaultTreeModel(root);
        folderTree = new JTree(folderTreeModel);
        folderTree.setRootVisible(false);
        folderTree.setShowsRootHandles(true);

        // 树选择事件
        folderTree.addTreeSelectionListener(e -> {
            DefaultMutableTreeNode node = (DefaultMutableTreeNode) folderTree.getLastSelectedPathComponent();
            if (node != null && node.getUserObject() instanceof File) {
                File selectedFolder = (File) node.getUserObject();
                loadPngFilesFromFolder(selectedFolder);
            }
        });

        JScrollPane treeScrollPane = new JScrollPane(folderTree);
        treeScrollPane.setPreferredSize(new Dimension(240, 400));
        panel.add(treeScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建PNG文件列表面板
     */
    private JPanel createPngFileListPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("PNG文件列表"));

        // 创建文件列表
        pngFileListModel = new DefaultListModel<>();
        pngFileList = new JList<>(pngFileListModel);
        pngFileList.setSelectionMode(ListSelectionModel.MULTIPLE_INTERVAL_SELECTION);

        // 自定义渲染器显示文件名和重命名预览
        pngFileList.setCellRenderer(new DefaultListCellRenderer() {
            @Override
            public Component getListCellRendererComponent(JList<?> list, Object value, int index,
                    boolean isSelected, boolean cellHasFocus) {
                super.getListCellRendererComponent(list, value, index, isSelected, cellHasFocus);

                if (value instanceof File) {
                    File file = (File) value;
                    String fileName = file.getName();
                    String newName = getNewFileName(fileName);

                    if (!fileName.equals(newName)) {
                        setText("<html>" + fileName + " → <font color='blue'>" + newName + "</font></html>");
                    } else {
                        setText("<html>" + fileName + " <font color='gray'>(无需改名)</font></html>");
                    }
                }

                return this;
            }
        });

        JScrollPane listScrollPane = new JScrollPane(pngFileList);
        listScrollPane.setPreferredSize(new Dimension(400, 280));
        panel.add(listScrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建预览和日志面板
     */
    private JPanel createPreviewLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 左侧：图像预览
        JPanel previewPanel = new JPanel(new BorderLayout());
        previewPanel.setBorder(BorderFactory.createTitledBorder("图像预览"));

        renamePreviewLabel = new JLabel("选择文件查看预览", SwingConstants.CENTER);
        renamePreviewLabel.setPreferredSize(new Dimension(200, 150));
        renamePreviewLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        previewPanel.add(renamePreviewLabel, BorderLayout.CENTER);

        // 右侧：操作日志
        JPanel logPanel = new JPanel(new BorderLayout());
        logPanel.setBorder(BorderFactory.createTitledBorder("操作日志"));

        renameLogArea = new JTextArea(8, 30);
        renameLogArea.setEditable(false);
        renameLogArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        JScrollPane logScrollPane = new JScrollPane(renameLogArea);
        logPanel.add(logScrollPane, BorderLayout.CENTER);

        // 分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT, previewPanel, logPanel);
        splitPane.setDividerLocation(220);
        splitPane.setResizeWeight(0.4);

        panel.add(splitPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建改名操作面板
     */
    private JPanel createRenameOperationPanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());

        previewRenameButton = new JButton("预览改名");
        previewRenameButton.setEnabled(false);
        previewRenameButton.addActionListener(e -> previewRename());
        buttonPanel.add(previewRenameButton);

        executeRenameButton = new JButton("执行改名");
        executeRenameButton.setEnabled(false);
        executeRenameButton.addActionListener(e -> executeRename());
        buttonPanel.add(executeRenameButton);

        JButton clearLogButton = new JButton("清空日志");
        clearLogButton.addActionListener(e -> renameLogArea.setText(""));
        buttonPanel.add(clearLogButton);

        panel.add(buttonPanel, BorderLayout.CENTER);

        // 进度条
        renameProgressBar = new JProgressBar(0, 100);
        renameProgressBar.setStringPainted(true);
        renameProgressBar.setString("就绪");
        panel.add(renameProgressBar, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 扫描文件夹获取PNG文件
     */
    private void scanFoldersForRename() {
        String path = renamePathField.getText().trim();
        if (path.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请先选择扫描路径！", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        File rootDir = new File(path);
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            JOptionPane.showMessageDialog(this, "选择的路径不存在或不是文件夹！", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 在后台线程中扫描
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                publish("开始扫描文件夹...");

                // 清空树模型
                DefaultMutableTreeNode root = (DefaultMutableTreeNode) folderTreeModel.getRoot();
                root.removeAllChildren();

                // 扫描文件夹结构
                scanFolderRecursive(rootDir, root);

                publish("扫描完成！");
                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    renameLogArea.append(message + "\n");
                    renameLogArea.setCaretPosition(renameLogArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                folderTreeModel.reload();
                scanFoldersButton.setEnabled(true);
                previewRenameButton.setEnabled(true);
                renameProgressBar.setString("扫描完成");
            }
        };

        scanFoldersButton.setEnabled(false);
        renameProgressBar.setString("扫描中...");
        worker.execute();
    }

    /**
     * 递归扫描文件夹
     */
    private void scanFolderRecursive(File dir, DefaultMutableTreeNode parentNode) {
        File[] files = dir.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isDirectory()) {
                DefaultMutableTreeNode folderNode = new DefaultMutableTreeNode(file);
                parentNode.add(folderNode);
                scanFolderRecursive(file, folderNode);
            }
        }
    }

    /**
     * 从选中文件夹加载PNG文件
     */
    private void loadPngFilesFromFolder(File folder) {
        pngFileListModel.clear();

        File[] files = folder.listFiles((dir, name) ->
            name.toLowerCase().endsWith(".png")||name.toLowerCase().endsWith(".was"));

        if (files != null) {
            for (File file : files) {
                pngFileListModel.addElement(file);
            }
        }

        renameLogArea.append("已加载文件夹: " + folder.getAbsolutePath() + " (共" + pngFileListModel.getSize() + "个PNG文件)\n");
        renameLogArea.setCaretPosition(renameLogArea.getDocument().getLength());
    }

    /**
     * 获取新文件名（将前缀替换为0x）
     */
    private String getNewFileName(String originalName) {
        // 查找第一个 '-' 的位置
        int dashIndex = originalName.indexOf('-');
        if (dashIndex > 0) {
            // 将前缀替换为 "0x"，去掉 '-'，保留后面的内容
            String nameAfterDash = originalName.substring(dashIndex + 1);
            return "0x" + nameAfterDash;
        }

        // 如果没有找到'-'，返回原文件名
        return originalName;
    }

    /**
     * 预览改名操作
     */
    private void previewRename() {
        if (pngFileListModel.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请先选择包含PNG文件的文件夹！", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        renameLogArea.append("\n=== 预览改名操作 ===\n");
        int renameCount = 0;

        for (int i = 0; i < pngFileListModel.getSize(); i++) {
            File file = pngFileListModel.getElementAt(i);
            String originalName = file.getName();
            String newName = getNewFileName(originalName);

            if (!originalName.equals(newName)) {
                renameLogArea.append(String.format("将改名: %s → %s\n", originalName, newName));
                renameCount++;
            }
        }

        if (renameCount == 0) {
            renameLogArea.append("没有需要改名的文件。\n");
            executeRenameButton.setEnabled(false);
        } else {
            renameLogArea.append(String.format("共有 %d 个文件需要改名。\n", renameCount));
            executeRenameButton.setEnabled(true);
        }

        renameLogArea.append("===================\n\n");
        renameLogArea.setCaretPosition(renameLogArea.getDocument().getLength());
    }

    /**
     * 执行改名操作
     */
    private void executeRename() {
        if (pngFileListModel.isEmpty()) {
            return;
        }

        int result = JOptionPane.showConfirmDialog(this,
            "确定要执行文件改名操作吗？此操作不可撤销！",
            "确认改名",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.WARNING_MESSAGE);

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        // 在后台线程中执行改名
        SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
            @Override
            protected Void doInBackground() throws Exception {
                publish("\n=== 开始执行改名操作 ===");

                int totalFiles = pngFileListModel.getSize();
                int renamedCount = 0;
                int errorCount = 0;

                for (int i = 0; i < totalFiles; i++) {
                    File file = pngFileListModel.getElementAt(i);
                    String originalName = file.getName();
                    String newName = getNewFileName(originalName);

                    // 更新进度
                    int progress = (i + 1) * 100 / totalFiles;
                    setProgress(progress);

                    if (!originalName.equals(newName)) {
                        File newFile = new File(file.getParent(), newName);

                        if (newFile.exists()) {
                            publish(String.format("跳过 %s: 目标文件已存在", originalName));
                            errorCount++;
                        } else {
                            try {
                                if (file.renameTo(newFile)) {
                                    publish(String.format("成功: %s → %s", originalName, newName));
                                    renamedCount++;
                                } else {
                                    publish(String.format("失败: %s (重命名操作失败)", originalName));
                                    errorCount++;
                                }
                            } catch (Exception e) {
                                publish(String.format("错误: %s (%s)", originalName, e.getMessage()));
                                errorCount++;
                            }
                        }
                    }
                }

                publish(String.format("\n改名操作完成！成功: %d, 错误: %d", renamedCount, errorCount));
                publish("=========================\n");

                return null;
            }

            @Override
            protected void process(java.util.List<String> chunks) {
                for (String message : chunks) {
                    renameLogArea.append(message + "\n");
                    renameLogArea.setCaretPosition(renameLogArea.getDocument().getLength());
                }
            }

            @Override
            protected void done() {
                executeRenameButton.setEnabled(false);
                renameProgressBar.setValue(0);
                renameProgressBar.setString("改名完成");

                // 刷新文件列表
                DefaultMutableTreeNode selectedNode = (DefaultMutableTreeNode) folderTree.getLastSelectedPathComponent();
                if (selectedNode != null && selectedNode.getUserObject() instanceof File) {
                    File selectedFolder = (File) selectedNode.getUserObject();
                    loadPngFilesFromFolder(selectedFolder);
                }
            }
        };

        worker.addPropertyChangeListener(evt -> {
            if ("progress".equals(evt.getPropertyName())) {
                int progress = (Integer) evt.getNewValue();
                renameProgressBar.setValue(progress);
                renameProgressBar.setString("改名中... " + progress + "%");
            }
        });

        executeRenameButton.setEnabled(false);
        worker.execute();
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        // 打印配置信息
        System.out.println("皮肤ID批量转换工具启动中...");
        PathConfig.getInstance().printConfig();

        SwingUtilities.invokeLater(() -> {
            try {
                // 初始化主题系统
                com.tool.ui.ThemeManager.initialize();

                // 设置全局字体为微软雅黑
                Font font = new Font("微软雅黑", Font.PLAIN, 12);
                java.util.Enumeration<Object> keys = UIManager.getDefaults().keys();
                while (keys.hasMoreElements()) {
                    Object key = keys.nextElement();
                    Object value = UIManager.get(key);
                    if (value instanceof javax.swing.plaf.FontUIResource) {
                        UIManager.put(key, font);
                    }
                }

            } catch (Exception e) {
                e.printStackTrace();
                // 如果主题初始化失败，使用系统默认外观
                try {
                    UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

            new SkinIdSyncTool().setVisible(true);
        });
    }

    // ==================== WAS解包功能 ====================

    /**
     * 浏览WAS源文件夹
     */
    private void browseWasSourceFolder() {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        chooser.setDialogTitle("选择包含WAS文件的文件夹");

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            wasSourceFolderField.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * 浏览WAS输出文件夹
     */
    private void browseWasOutputFolder() {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        chooser.setDialogTitle("选择输出文件夹");

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            wasOutputFolderField.setText(chooser.getSelectedFile().getAbsolutePath());
        }
    }

    /**
     * 开始WAS解包
     */
    private void startWasExtraction() {
        String sourceFolder = wasSourceFolderField.getText().trim();
        String outputFolder = wasOutputFolderField.getText().trim();

        if (sourceFolder.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请选择WAS源文件夹！", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        if (outputFolder.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请选择输出文件夹！", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        File sourceDir = new File(sourceFolder);
        File outputDir = new File(outputFolder);

        if (!sourceDir.exists() || !sourceDir.isDirectory()) {
            JOptionPane.showMessageDialog(this, "WAS源文件夹不存在！", "错误", JOptionPane.ERROR_MESSAGE);
            return;
        }

        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        // 禁用开始按钮
        startWasExtractButton.setEnabled(false);
        wasLogArea.setText("");
        logWas("开始WAS文件解包...");

        // 在后台线程中执行解包
        new Thread(() -> {
            try {
                extractWasFiles(sourceDir, outputDir);
                SwingUtilities.invokeLater(() -> {
                    logWas("WAS文件解包完成！");
                    startWasExtractButton.setEnabled(true);
                    JOptionPane.showMessageDialog(this, "WAS文件解包完成！", "完成", JOptionPane.INFORMATION_MESSAGE);
                });
            } catch (Exception e) {
                SwingUtilities.invokeLater(() -> {
                    logWas("解包失败: " + e.getMessage());
                    startWasExtractButton.setEnabled(true);
                    JOptionPane.showMessageDialog(this, "解包失败: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                });
                e.printStackTrace();
            }
        }).start();
    }

    /**
     * 提取WAS文件
     */
    private void extractWasFiles(File sourceDir, File outputDir) throws IOException {
        // 收集所有WAS文件
        java.util.List<File> wasFiles = new java.util.ArrayList<>();
        collectWasFiles(sourceDir, wasFiles);

        if (wasFiles.isEmpty()) {
            logWas("在指定目录中未找到WAS文件");
            return;
        }

        logWas("找到 " + wasFiles.size() + " 个WAS文件");

        boolean extractStatic = extractStaticCheckBox.isSelected();
        boolean extractAnimated = extractAnimatedCheckBox.isSelected();
        boolean createSubfolders = createSubfoldersCheckBox.isSelected();

        int totalExtracted = 0;
        int processedFiles = 0;

        for (File wasFile : wasFiles) {
            try {
                int extracted = com.tool.was.WasExtractor.extractWasFile(
                    wasFile, outputDir, extractStatic, extractAnimated,
                    createSubfolders, this::logWas
                );
                totalExtracted += extracted;
                processedFiles++;

                // 更新进度
                final int currentProcessed = processedFiles;
                final int currentExtracted = totalExtracted;
                SwingUtilities.invokeLater(() -> {
                    logWas("进度: " + currentProcessed + "/" + wasFiles.size() +
                           " 文件，已提取 " + currentExtracted + " 个图像");
                });

            } catch (Exception e) {
                logWas("处理文件失败 " + wasFile.getName() + ": " + e.getMessage());
            }
        }

        logWas("解包完成！共处理 " + processedFiles + " 个文件，提取 " + totalExtracted + " 个图像");
    }

    /**
     * 收集WAS文件
     */
    private void collectWasFiles(File dir, java.util.List<File> wasFiles) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    collectWasFiles(file, wasFiles);
                } else if (file.getName().toLowerCase().endsWith(".was")) {
                    wasFiles.add(file);
                }
            }
        }
    }

    /**
     * 分析WAS文件结构
     */
    private void analyzeWasFile() {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        chooser.setDialogTitle("选择要分析的WAS文件");
        chooser.setFileFilter(new javax.swing.filechooser.FileFilter() {
            @Override
            public boolean accept(File f) {
                return f.isDirectory() || f.getName().toLowerCase().endsWith(".was");
            }

            @Override
            public String getDescription() {
                return "WAS文件 (*.was)";
            }
        });

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            File wasFile = chooser.getSelectedFile();
            wasLogArea.setText("");
            logWas("开始分析WAS文件: " + wasFile.getName());

            new Thread(() -> {
                try {
                    analyzeWasFileStructure(wasFile);
                } catch (Exception e) {
                    logWas("分析失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }).start();
        }
    }

    /**
     * 分析WAS文件结构的具体实现
     */
    private void analyzeWasFileStructure(File wasFile) {
        logWas("=== WAS文件分析 ===");
        logWas("文件: " + wasFile.getName());
        logWas("大小: " + wasFile.length() + " 字节");

        try (java.io.RandomAccessFile raf = new java.io.RandomAccessFile(wasFile, "r")) {
            // 读取文件头
            logWas("--- 文件头分析 ---");

            // 前32字节的十六进制内容
            byte[] header = new byte[Math.min(32, (int)wasFile.length())];
            raf.read(header);

            logWas("前32字节十六进制:");
            StringBuilder hexDump = new StringBuilder();
            for (int i = 0; i < header.length; i += 16) {
                hexDump.append(String.format("%04X: ", i));

                // 十六进制部分
                for (int j = 0; j < 16; j++) {
                    if (i + j < header.length) {
                        hexDump.append(String.format("%02X ", header[i + j] & 0xFF));
                    } else {
                        hexDump.append("   ");
                    }
                }

                hexDump.append(" ");

                // ASCII部分
                for (int j = 0; j < 16 && i + j < header.length; j++) {
                    byte b = header[i + j];
                    if (b >= 32 && b <= 126) {
                        hexDump.append((char) b);
                    } else {
                        hexDump.append(".");
                    }
                }

                if (i + 16 < header.length) {
                    hexDump.append("\n");
                }
            }
            logWas(hexDump.toString());

            // 重置到开头
            raf.seek(0);

            // 尝试解析头部信息
            logWas("--- 头部字段解析 ---");

            // 读取可能的标识
            byte[] signature = new byte[2];
            raf.read(signature);
            String sigStr = new String(signature);
            logWas("标识: " + sigStr + " (0x" + String.format("%02X%02X", signature[0], signature[1]) + ")");

            if ("SP".equals(sigStr) || "SH".equals(sigStr)) {
                logWas("检测到有效的WAS文件标识");

                // 读取后续字段
                int field1 = readInt16LE(raf);
                int field2 = readInt16LE(raf);
                int field3 = readInt16LE(raf);
                int field4 = readInt16LE(raf);
                int field5 = readInt16LE(raf);
                int field6 = readInt16LE(raf);
                int field7 = readInt16LE(raf);

                logWas("字段1 (可能是头部大小): " + field1);
                logWas("字段2 (可能是方向数): " + field2);
                logWas("字段3 (可能是帧数): " + field3);
                logWas("字段4 (可能是宽度): " + field4);
                logWas("字段5 (可能是高度): " + field5);
                logWas("字段6 (可能是中心X): " + field6);
                logWas("字段7 (可能是中心Y): " + field7);

                // 检查数据合理性
                if (field4 > 0 && field5 > 0 && field4 < 2048 && field5 < 2048) {
                    logWas("图像尺寸看起来合理: " + field4 + "x" + field5);
                } else {
                    logWas("警告: 图像尺寸可能不正确");
                }

                if (field3 > 0 && field3 < 1000) {
                    logWas("帧数看起来合理: " + field3);
                } else if (field3 == 0) {
                    logWas("可能是静态图像（帧数为0）");
                } else {
                    logWas("警告: 帧数可能不正确");
                }

                // 分析图像数据部分
                logWas("--- 图像数据分析 ---");
                raf.seek(field1); // 跳到图像数据开始位置

                byte[] imageHeader = new byte[Math.min(32, (int)(wasFile.length() - field1))];
                raf.read(imageHeader);

                logWas("图像数据前32字节:");
                StringBuilder imageHexDump = new StringBuilder();
                for (int i = 0; i < imageHeader.length; i += 16) {
                    imageHexDump.append(String.format("%04X: ", field1 + i));

                    for (int j = 0; j < 16; j++) {
                        if (i + j < imageHeader.length) {
                            imageHexDump.append(String.format("%02X ", imageHeader[i + j] & 0xFF));
                        } else {
                            imageHexDump.append("   ");
                        }
                    }

                    imageHexDump.append(" ");

                    for (int j = 0; j < 16 && i + j < imageHeader.length; j++) {
                        byte b = imageHeader[i + j];
                        if (b >= 32 && b <= 126) {
                            imageHexDump.append((char) b);
                        } else {
                            imageHexDump.append(".");
                        }
                    }

                    if (i + 16 < imageHeader.length) {
                        imageHexDump.append("\n");
                    }
                }
                logWas(imageHexDump.toString());

                // 检查是否是TGA格式
                if (imageHeader.length >= 18) {
                    int tgaImageType = imageHeader[2] & 0xFF;
                    int tgaWidth = ((imageHeader[13] & 0xFF) << 8) | (imageHeader[12] & 0xFF);
                    int tgaHeight = ((imageHeader[15] & 0xFF) << 8) | (imageHeader[14] & 0xFF);
                    int tgaPixelDepth = imageHeader[16] & 0xFF;

                    logWas("TGA分析:");
                    logWas("  图像类型: " + tgaImageType);
                    logWas("  TGA宽度: " + tgaWidth);
                    logWas("  TGA高度: " + tgaHeight);
                    logWas("  像素深度: " + tgaPixelDepth);

                    if (tgaWidth == field4 && tgaHeight == field5) {
                        logWas("  TGA尺寸与WAS头部匹配！");
                    } else {
                        logWas("  TGA尺寸与WAS头部不匹配");
                    }
                }

                // 如果图像数据区域全是0，寻找真正的图像数据
                boolean allZero = true;
                for (byte b : imageHeader) {
                    if (b != 0) {
                        allZero = false;
                        break;
                    }
                }

                if (allZero) {
                    logWas("图像数据区域全是0，搜索真正的图像数据...");

                    // 搜索整个文件，寻找非零数据块
                    raf.seek(0);
                    byte[] fullFile = new byte[(int)wasFile.length()];
                    raf.read(fullFile);

                    logWas("搜索整个文件中的非零数据块...");
                    int foundBlocks = 0;

                    for (int offset = 0; offset < fullFile.length - 32; offset++) {
                        // 检查这个位置开始的32字节是否有足够的非零数据
                        int nonZeroCount = 0;
                        for (int i = 0; i < 32 && offset + i < fullFile.length; i++) {
                            if (fullFile[offset + i] != 0) {
                                nonZeroCount++;
                            }
                        }

                        // 如果有超过8个非零字节，认为这是一个数据块
                        if (nonZeroCount > 8) {
                            foundBlocks++;
                            logWas("数据块 " + foundBlocks + " - 偏移 " + offset + " (非零字节: " + nonZeroCount + "):");

                            StringBuilder dataHex = new StringBuilder();
                            dataHex.append(String.format("%04X: ", offset));

                            for (int i = 0; i < 32 && offset + i < fullFile.length; i++) {
                                dataHex.append(String.format("%02X ", fullFile[offset + i] & 0xFF));
                            }

                            logWas(dataHex.toString());

                            // 检查这个位置是否可能是TGA头部
                            if (offset + 18 < fullFile.length) {
                                int tgaType = fullFile[offset + 2] & 0xFF;
                                int tgaW = ((fullFile[offset + 13] & 0xFF) << 8) | (fullFile[offset + 12] & 0xFF);
                                int tgaH = ((fullFile[offset + 15] & 0xFF) << 8) | (fullFile[offset + 14] & 0xFF);
                                int tgaDepth = fullFile[offset + 16] & 0xFF;

                                logWas("  TGA检查: 类型=" + tgaType + ", 尺寸=" + tgaW + "x" + tgaH + ", 深度=" + tgaDepth);

                                if (tgaW == field4 && tgaH == field5 && (tgaDepth == 24 || tgaDepth == 32) &&
                                    (tgaType == 2 || tgaType == 10)) {
                                    logWas("  *** 找到匹配的TGA头部! ***");
                                }
                            }

                            // 跳过一些字节避免重复检测
                            offset += 16;

                            if (foundBlocks >= 5) {
                                logWas("已显示前5个数据块...");
                                break;
                            }
                        }
                    }

                    if (foundBlocks == 0) {
                        logWas("未找到明显的数据块，文件可能使用了特殊的压缩或编码");

                        // 显示文件末尾的数据
                        logWas("文件末尾64字节:");
                        int startPos = Math.max(0, fullFile.length - 64);
                        StringBuilder endHex = new StringBuilder();
                        for (int i = startPos; i < fullFile.length; i += 16) {
                            endHex.append(String.format("%04X: ", i));
                            for (int j = 0; j < 16 && i + j < fullFile.length; j++) {
                                endHex.append(String.format("%02X ", fullFile[i + j] & 0xFF));
                            }
                            endHex.append("\n");
                        }
                        logWas(endHex.toString());
                    }
                }

            } else {
                logWas("未识别的文件格式，可能不是标准WAS文件");

                // 尝试查找PNG头部
                raf.seek(0);
                byte[] buffer = new byte[(int)Math.min(1024, wasFile.length())];
                raf.read(buffer);

                for (int i = 0; i < buffer.length - 4; i++) {
                    if ((buffer[i] & 0xFF) == 0x89 &&
                        (buffer[i+1] & 0xFF) == 0x50 &&
                        (buffer[i+2] & 0xFF) == 0x4E &&
                        (buffer[i+3] & 0xFF) == 0x47) {
                        logWas("在偏移 " + i + " 处发现PNG头部");
                        break;
                    }
                }
            }

        } catch (Exception e) {
            logWas("分析文件时出错: " + e.getMessage());
        }

        logWas("=== 分析完成 ===");
    }

    /**
     * 搜索WAS文件中的TGA数据
     */
    private void searchTgaInWas() {
        JFileChooser chooser = new JFileChooser();
        chooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
        chooser.setDialogTitle("选择要搜索TGA数据的WAS文件");
        chooser.setFileFilter(new javax.swing.filechooser.FileFilter() {
            @Override
            public boolean accept(File f) {
                return f.isDirectory() || f.getName().toLowerCase().endsWith(".was");
            }

            @Override
            public String getDescription() {
                return "WAS文件 (*.was)";
            }
        });

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            File wasFile = chooser.getSelectedFile();
            wasLogArea.setText("");
            logWas("开始搜索TGA数据: " + wasFile.getName());

            new Thread(() -> {
                try {
                    searchTgaDataInFile(wasFile);
                } catch (Exception e) {
                    logWas("搜索失败: " + e.getMessage());
                    e.printStackTrace();
                }
            }).start();
        }
    }

    /**
     * 在WAS文件中搜索TGA数据的具体实现
     */
    private void searchTgaDataInFile(File wasFile) {
        logWas("=== 搜索TGA数据 ===");
        logWas("文件: " + wasFile.getName());
        logWas("大小: " + wasFile.length() + " 字节");

        try (java.io.RandomAccessFile raf = new java.io.RandomAccessFile(wasFile, "r")) {
            // 首先读取WAS头部信息
            raf.seek(0);
            byte[] header = new byte[2];
            raf.read(header);
            String headerStr = new String(header);

            int wasWidth = 0, wasHeight = 0;
            if ("SP".equals(headerStr) || "SH".equals(headerStr)) {
                int headSize = readInt16LE(raf);
                int directionNum = readInt16LE(raf);
                int spriteFrame = readInt16LE(raf);
                wasWidth = readInt16LE(raf);
                wasHeight = readInt16LE(raf);

                logWas("WAS头部信息:");
                logWas("  标识: " + headerStr);
                logWas("  头部大小: " + headSize);
                logWas("  图像尺寸: " + wasWidth + "x" + wasHeight);
            }

            // 读取整个文件
            raf.seek(0);
            byte[] fileData = new byte[(int)wasFile.length()];
            raf.read(fileData);

            // 搜索可能的TGA头部
            logWas("搜索TGA头部模式...");
            int foundCount = 0;

            for (int offset = 0; offset < fileData.length - 18; offset++) {
                // TGA头部检查
                int idLength = fileData[offset] & 0xFF;
                int colorMapType = fileData[offset + 1] & 0xFF;
                int imageType = fileData[offset + 2] & 0xFF;

                // 跳过颜色映射表信息（5字节）
                int width = ((fileData[offset + 13] & 0xFF) << 8) | (fileData[offset + 12] & 0xFF);
                int height = ((fileData[offset + 15] & 0xFF) << 8) | (fileData[offset + 14] & 0xFF);
                int pixelDepth = fileData[offset + 16] & 0xFF;
                int imageDescriptor = fileData[offset + 17] & 0xFF;

                // 检查是否是合理的TGA头部
                if (isValidTgaHeader(idLength, colorMapType, imageType, width, height, pixelDepth, imageDescriptor)) {
                    foundCount++;
                    boolean matchesWasSize = (wasWidth > 0 && wasHeight > 0 && width == wasWidth && height == wasHeight);

                    logWas("发现TGA头部 #" + foundCount + " 在偏移 " + offset + ":");
                    logWas("  ID长度: " + idLength);
                    logWas("  颜色映射类型: " + colorMapType);
                    logWas("  图像类型: " + imageType);
                    logWas("  宽度: " + width);
                    logWas("  高度: " + height);
                    logWas("  像素深度: " + pixelDepth);
                    logWas("  图像描述符: " + imageDescriptor);
                    if (matchesWasSize) {
                        logWas("  *** 尺寸与WAS头部匹配! ***");
                    }

                    // 显示头部数据
                    StringBuilder headerHex = new StringBuilder();
                    headerHex.append("  头部数据: ");
                    for (int i = 0; i < 18 && offset + i < fileData.length; i++) {
                        headerHex.append(String.format("%02X ", fileData[offset + i] & 0xFF));
                    }
                    logWas(headerHex.toString());

                    // 显示图像数据开始部分
                    int dataStart = offset + 18 + idLength;
                    if (dataStart < fileData.length) {
                        StringBuilder dataHex = new StringBuilder();
                        dataHex.append("  图像数据开始: ");
                        for (int i = 0; i < 16 && dataStart + i < fileData.length; i++) {
                            dataHex.append(String.format("%02X ", fileData[dataStart + i] & 0xFF));
                        }
                        logWas(dataHex.toString());
                    }

                    // 尝试提取这个TGA
                    if (tryExtractTgaAt(fileData, offset, width, height, pixelDepth, imageType, wasFile.getName())) {
                        logWas("  *** 成功提取TGA图像! ***");
                    } else {
                        logWas("  提取失败");
                    }

                    logWas("");

                    // 限制显示数量
                    if (foundCount >= 10) {
                        logWas("已显示前10个TGA头部...");
                        break;
                    }
                }
            }

            if (foundCount == 0) {
                logWas("未找到有效的TGA头部");
                logWas("尝试搜索其他模式...");

                // 搜索可能的图像数据模式
                searchImageDataPatterns(fileData, wasWidth, wasHeight);
            }

            logWas("=== 搜索完成 ===");

        } catch (Exception e) {
            logWas("搜索过程中出错: " + e.getMessage());
        }
    }

    /**
     * 搜索可能的图像数据模式
     */
    private void searchImageDataPatterns(byte[] fileData, int expectedWidth, int expectedHeight) {
        logWas("搜索图像数据模式...");

        if (expectedWidth > 0 && expectedHeight > 0) {
            int expectedSize16 = expectedWidth * expectedHeight * 2; // 16位
            int expectedSize24 = expectedWidth * expectedHeight * 3; // 24位
            int expectedSize32 = expectedWidth * expectedHeight * 4; // 32位

            logWas("期望的图像数据大小:");
            logWas("  16位: " + expectedSize16 + " 字节");
            logWas("  24位: " + expectedSize24 + " 字节");
            logWas("  32位: " + expectedSize32 + " 字节");

            // 搜索可能的数据块
            for (int offset = 12; offset < fileData.length - expectedSize16; offset++) {
                // 检查这个位置是否有足够的数据
                if (offset + expectedSize16 <= fileData.length) {
                    // 检查数据的变化程度（不应该全是0或全是同一值）
                    int uniqueValues = countUniqueBytes(fileData, offset, Math.min(100, expectedSize16));
                    if (uniqueValues > 10) {
                        logWas("在偏移 " + offset + " 处发现可能的图像数据 (变化值: " + uniqueValues + ")");

                        // 显示数据开始部分
                        StringBuilder dataHex = new StringBuilder();
                        dataHex.append("  数据: ");
                        for (int i = 0; i < 32 && offset + i < fileData.length; i++) {
                            dataHex.append(String.format("%02X ", fileData[offset + i] & 0xFF));
                        }
                        logWas(dataHex.toString());

                        break; // 只显示第一个找到的
                    }
                }
            }
        }
    }

    /**
     * 计算数据块中唯一字节值的数量
     */
    private int countUniqueBytes(byte[] data, int offset, int length) {
        java.util.Set<Byte> uniqueBytes = new java.util.HashSet<>();
        for (int i = 0; i < length && offset + i < data.length; i++) {
            uniqueBytes.add(data[offset + i]);
        }
        return uniqueBytes.size();
    }

    /**
     * 检查是否是有效的TGA头部
     */
    private boolean isValidTgaHeader(int idLength, int colorMapType, int imageType,
                                   int width, int height, int pixelDepth, int imageDescriptor) {
        // ID长度应该在合理范围内
        if (idLength > 255) return false;

        // 颜色映射类型应该是0或1
        if (colorMapType > 1) return false;

        // 图像类型应该在有效范围内
        if (imageType < 0 || imageType > 11) return false;

        // 尺寸应该合理
        if (width <= 0 || height <= 0 || width > 4096 || height > 4096) return false;

        // 像素深度应该是常见值
        if (pixelDepth != 8 && pixelDepth != 15 && pixelDepth != 16 &&
            pixelDepth != 24 && pixelDepth != 32) return false;

        // 图像描述符的高4位应该是0
        if ((imageDescriptor & 0xF0) != 0) return false;

        return true;
    }

    /**
     * 尝试在指定位置提取TGA图像
     */
    private boolean tryExtractTgaAt(byte[] fileData, int offset, int width, int height,
                                  int pixelDepth, int imageType, String fileName) {
        try {
            // 计算TGA数据的大小
            int headerSize = 18;
            int idLength = fileData[offset] & 0xFF;
            headerSize += idLength;

            // 计算图像数据大小
            int bytesPerPixel = pixelDepth / 8;
            int imageDataSize = width * height * bytesPerPixel;

            if (offset + headerSize + imageDataSize > fileData.length) {
                return false; // 数据不够
            }

            // 提取TGA数据
            byte[] tgaData = new byte[headerSize + imageDataSize];
            System.arraycopy(fileData, offset, tgaData, 0, tgaData.length);

            // 保存为TGA文件进行验证
            String outputName = fileName.replace(".was", "_extracted_" + offset + ".tga");
            File outputFile = new File("temp_" + outputName);

            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(outputFile)) {
                fos.write(tgaData);
            }

            logWas("  已保存TGA文件: " + outputFile.getName() + " (大小: " + tgaData.length + " 字节)");
            return true;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 读取16位小端序整数
     */
    private int readInt16LE(java.io.RandomAccessFile raf) throws java.io.IOException {
        byte[] bytes = new byte[2];
        raf.read(bytes);
        return (bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8);
    }

    /**
     * 记录WAS解包日志
     */
    private void logWas(String message) {
        SwingUtilities.invokeLater(() -> {
            wasLogArea.append("[" + java.time.LocalTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")) + "] " + message + "\n");
            wasLogArea.setCaretPosition(wasLogArea.getDocument().getLength());
        });
    }

    // ==================== 自定义比较器 ====================

    /**
     * 文件大小比较器
     */
    private static class FileSizeComparator implements java.util.Comparator<String> {
        @Override
        public int compare(String size1, String size2) {
            if (size1 == null && size2 == null) return 0;
            if (size1 == null) return -1;
            if (size2 == null) return 1;

            long bytes1 = parseSizeToBytes(size1);
            long bytes2 = parseSizeToBytes(size2);

            return Long.compare(bytes1, bytes2);
        }

        private long parseSizeToBytes(String sizeStr) {
            if (sizeStr == null || sizeStr.isEmpty()) {
                return 0;
            }

            try {
                if (sizeStr.endsWith(" KB")) {
                    return (long) (Double.parseDouble(sizeStr.replace(" KB", "")) * 1024);
                } else if (sizeStr.endsWith(" MB")) {
                    return (long) (Double.parseDouble(sizeStr.replace(" MB", "")) * 1024 * 1024);
                } else if (sizeStr.endsWith(" GB")) {
                    return (long) (Double.parseDouble(sizeStr.replace(" GB", "")) * 1024 * 1024 * 1024);
                } else if (sizeStr.endsWith(" bytes")) {
                    return Long.parseLong(sizeStr.replace(" bytes", ""));
                }
            } catch (NumberFormatException e) {
                // 解析失败，返回0
            }
            return 0;
        }
    }

    /**
     * 日期时间比较器
     */
    private static class DateTimeComparator implements java.util.Comparator<String> {
        @Override
        public int compare(String date1, String date2) {
            if (date1 == null && date2 == null) return 0;
            if (date1 == null) return -1;
            if (date2 == null) return 1;

            try {
                // 解析日期时间字符串 (格式: 2024-07-11 21:01:29)
                java.time.LocalDateTime dt1 = parseDateTime(date1);
                java.time.LocalDateTime dt2 = parseDateTime(date2);

                if (dt1 == null && dt2 == null) return 0;
                if (dt1 == null) return -1;
                if (dt2 == null) return 1;

                return dt1.compareTo(dt2);
            } catch (Exception e) {
                // 解析失败，使用字符串比较
                return date1.compareTo(date2);
            }
        }

        private java.time.LocalDateTime parseDateTime(String dateStr) {
            try {
                // 支持格式: 2024-07-11 21:01:29
                java.time.format.DateTimeFormatter formatter =
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                return java.time.LocalDateTime.parse(dateStr, formatter);
            } catch (Exception e) {
                return null;
            }
        }
    }
}
