package com.tool.npk;

import javax.swing.ImageIcon;

/**
 * NPK图像工具类
 * 提供便捷的NPK图像读取方法，类似于WDF的接口
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkImageUtils {
    
    /**
     * 从items.npk中读取指定皮肤的ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移，如"1a5de2bd"或"0x1a5de2bd"）
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getItemNpkPng(String skin, int w, int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "items.npk");
    }
    
    /**
     * 从items.npk中读取指定皮肤的原始ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移，如"1a5de2bd"或"0x1a5de2bd"）
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getItemNpkPng(String skin) {
        return NpkImageReader.getNpkPng(skin, "items.npk");
    }
    
    /**
     * 从item.npk中读取指定皮肤的ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移，如"1a5de2bd"或"0x1a5de2bd"）
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getItemFile(String skin, int w, int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "item.npk");
    }
    
    /**
     * 从item.npk中读取指定皮肤的原始ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移，如"1a5de2bd"或"0x1a5de2bd"）
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getItemFile(String skin) {
        return NpkImageReader.getNpkPng(skin, "item.npk");
    }
    
    /**
     * 从指定NPK文件中读取皮肤ImageIcon（类似WDF接口）
     * 
     * @param skin 皮肤名称（十六进制偏移，如"1a5de2bd"或"0x1a5de2bd"）
     * @param w 目标宽度
     * @param h 目标高度
     * @param npkFileName NPK文件名
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getNpkPng(String skin, int w, int h, String npkFileName) {
        return NpkImageReader.getNpkPng(skin, w, h, npkFileName);
    }
    
    /**
     * 从指定NPK文件中读取皮肤原始ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移，如"1a5de2bd"或"0x1a5de2bd"）
     * @param npkFileName NPK文件名
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getNpkPng(String skin, String npkFileName) {
        return NpkImageReader.getNpkPng(skin, npkFileName);
    }
    
    /**
     * 从武器NPK文件中读取ImageIcon
     * 
     * @param skin 皮肤名称
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getWeaponNpkPng(String skin, int w, int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "weapon.npk");
    }
    
    /**
     * 从装备NPK文件中读取ImageIcon
     * 
     * @param skin 皮肤名称
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getEquipNpkPng(String skin, int w, int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "equip.npk");
    }
    
    /**
     * 从角色NPK文件中读取ImageIcon
     * 
     * @param skin 皮肤名称
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getCharacterNpkPng(String skin, int w, int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "character.npk");
    }
    
    /**
     * 从技能NPK文件中读取ImageIcon
     * 
     * @param skin 皮肤名称
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getSkillNpkPng(String skin, int w, int h) {
        return NpkImageReader.getNpkPng(skin, w, h, "skill.npk");
    }
    
    /**
     * 清空所有NPK图像缓存
     */
    public static void clearCache() {
        NpkImageReader.clearAllCache();
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计字符串
     */
    public static String getCacheInfo() {
        return NpkImageReader.getCacheStats();
    }
    
    // ========== 兼容性方法 ==========
    
    /**
     * 兼容WDF接口的方法名
     * 从items.npk中读取指定皮肤的ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移）
     * @param w 目标宽度
     * @param h 目标高度
     * @param npkFileName NPK文件名（为了兼容，实际使用items.npk）
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getWdfPng(String skin, int w, int h, String npkFileName) {
        // 如果指定了特定的NPK文件名，使用指定的文件
        if (npkFileName != null && !npkFileName.isEmpty() && !npkFileName.equals("items.wdf")) {
            return NpkImageReader.getNpkPng(skin, w, h, npkFileName);
        }
        // 默认使用items.npk
        return getItemNpkPng(skin, w, h);
    }
    
    /**
     * 示例方法：从items.npk读取宝物图像
     * 
     * @param skin 皮肤名称（十六进制偏移）
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon xbaoNpkFile(String skin, int w, int h) {
        return getItemNpkPng(skin, w, h);
    }
    
    /**
     * 示例方法：从item.npk读取物品图像
     * 
     * @param skin 皮肤名称（十六进制偏移）
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon itemNpkFile(String skin, int w, int h) {
        return getItemFile(skin, w, h);
    }
    
    /**
     * 示例方法：从武器NPK读取武器图像
     * 
     * @param skin 皮肤名称（十六进制偏移）
     * @param w 目标宽度
     * @param h 目标高度
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon weaponNpkFile(String skin, int w, int h) {
        return getWeaponNpkPng(skin, w, h);
    }
}
