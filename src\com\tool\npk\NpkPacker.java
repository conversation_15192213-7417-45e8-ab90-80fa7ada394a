package com.tool.npk;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.zip.Deflater;

/**
 * NPK文件打包工具
 * 将文件夹中的PNG文件打包成NPK格式
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkPacker {
    
    /**
     * 打包文件夹到NPK文件
     * 
     * @param sourceFolder 源文件夹路径
     * @param outputNpkPath 输出NPK文件路径
     * @param compressionType 压缩类型 (0=无压缩, 1=Deflate, 2=LZ4, 3=ZSTD)
     * @param progressCallback 进度回调 (进度百分比, 状态信息)
     * @return 是否成功
     */
    public static boolean packFolder(String sourceFolder, String outputNpkPath, 
                                   int compressionType, BiConsumer<Integer, String> progressCallback) {
        try {
            File folder = new File(sourceFolder);
            if (!folder.exists() || !folder.isDirectory()) {
                if (progressCallback != null) {
                    progressCallback.accept(-1, "源文件夹不存在或不是目录: " + sourceFolder);
                }
                return false;
            }
            
            // 收集所有PNG和WAS文件
            List<File> supportedFiles = collectSupportedFiles(folder);
            if (supportedFiles.isEmpty()) {
                if (progressCallback != null) {
                    progressCallback.accept(-1, "源文件夹中没有找到PNG或WAS文件");
                }
                return false;
            }

            if (progressCallback != null) {
                progressCallback.accept(10, "找到 " + supportedFiles.size() + " 个文件 (PNG/WAS)");
            }
            
            // 创建NPK文件
            return createNpkFile(supportedFiles, outputNpkPath, compressionType, progressCallback);
            
        } catch (Exception e) {
            if (progressCallback != null) {
                progressCallback.accept(-1, "打包失败: " + e.getMessage());
            }
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 收集文件夹中的所有支持的文件（PNG和WAS）
     */
    private static List<File> collectSupportedFiles(File folder) {
        List<File> supportedFiles = new ArrayList<>();

        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    String fileName = file.getName().toLowerCase();
                    if (fileName.endsWith(".png") || fileName.endsWith(".was")) {
                        supportedFiles.add(file);
                    }
                }
            }
        }

        // 按文件名排序，确保一致性
        supportedFiles.sort(Comparator.comparing(File::getName));

        return supportedFiles;
    }
    
    /**
     * 创建NPK文件
     */
    private static boolean createNpkFile(List<File> pngFiles, String outputNpkPath, 
                                       int compressionType, BiConsumer<Integer, String> progressCallback) {
        try (RandomAccessFile raf = new RandomAccessFile(outputNpkPath, "rw")) {
            // 写入NPK文件头
            writeNpkHeader(raf, pngFiles.size());
            if (progressCallback != null) {
                progressCallback.accept(20, "NPK文件头写入完成");
            }
            // 准备文件数据和索引
            List<NpkPackEntry> entries = new ArrayList<>();
            long currentOffset = calculateDataStartOffset(pngFiles.size());
            
            // 写入文件数据
            for (int i = 0; i < pngFiles.size(); i++) {
                File file = pngFiles.get(i);
                
                if (progressCallback != null && (i + 1) % 100 == 0) {
                    int progress = 20 + (i * 50 / pngFiles.size());
                    progressCallback.accept(progress, "处理文件: " + (i + 1) + "/" + pngFiles.size());
                }
                
                // 读取文件数据
                byte[] originalData = Files.readAllBytes(file.toPath());
                byte[] compressedData = compressData(originalData, compressionType);
                
                // 创建条目
                NpkPackEntry entry = new NpkPackEntry();
                entry.fileName = file.getName(); // 直接使用原始文件名，不进行任何处理
                entry.offset = currentOffset;
                entry.originalSize = originalData.length;
                entry.compressedSize = compressedData.length;
                entry.compressionType = (compressedData.length < originalData.length) ? compressionType : 0;
                entry.data = (entry.compressionType == 0) ? originalData : compressedData;

                // 调试信息：显示正在打包的文件名
                System.out.println("打包文件: " + file.getName() + " -> NPK条目文件名: " + entry.fileName);

                // 解析文件名中的偏移（如果是十六进制格式）
                entry.fileOffset = parseFileOffset(entry.fileName);
                
                entries.add(entry);
                
                // 写入文件数据
                raf.seek(currentOffset);
                raf.write(entry.data);
                
                currentOffset += entry.data.length;
                
                // 4字节对齐
                long padding = (4 - (currentOffset % 4)) % 4;
                if (padding > 0) {
                    raf.write(new byte[(int)padding]);
                    currentOffset += padding;
                }
            }
            
            if (progressCallback != null) {
                progressCallback.accept(70, "文件数据写入完成");
            }
            
            // 记录索引表位置
            long indexTableOffset = currentOffset;

            // 写入索引表
            writeIndexTable(raf, entries);

            if (progressCallback != null) {
                progressCallback.accept(80, "索引表写入完成");
            }
            // 写入文件名表
            writeFileNameTable(raf, entries);
            if (progressCallback != null) {
                progressCallback.accept(90, "文件名表写入完成");
            }
            // 更新文件头中的索引偏移
            updateIndexOffsetFixed(raf, indexTableOffset);
            if (progressCallback != null) {
                progressCallback.accept(100, "NPK文件创建完成: " + outputNpkPath);
            }
            return true;
        } catch (Exception e) {
            if (progressCallback != null) {
                progressCallback.accept(-1, "创建NPK文件失败: " + e.getMessage());
            }
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 写入NPK文件头
     */
    private static void writeNpkHeader(RandomAccessFile raf, int fileCount) throws IOException {
        raf.seek(0);
        
        // 魔数: NXPK
        raf.write("NXPK".getBytes());
        
        // 文件数量 (4字节)
        writeInt32LE(raf, fileCount);
        
        // 保留字段 (8字节)
        raf.write(new byte[8]);
        
        // 版本号 (4字节)
        writeInt32LE(raf, 1);
        
        // 索引偏移 (4字节) - 稍后更新
        writeInt32LE(raf, 0);
        
        // 填充到32字节
        raf.write(new byte[8]);
    }
    
    /**
     * 计算数据开始偏移
     */
    private static long calculateDataStartOffset(int fileCount) {
        // NPK头部: 32字节
        return 32;
    }
    
    /**
     * 压缩数据
     */
    private static byte[] compressData(byte[] data, int compressionType) {
        try {
            switch (compressionType) {
                case 1: // Deflate压缩
                    return compressDeflate(data);
                case 2: // LZ4压缩 (暂时使用Deflate代替)
                case 3: // ZSTD压缩 (暂时使用Deflate代替)
                    return compressDeflate(data);
                default: // 无压缩
                    return data;
            }
        } catch (Exception e) {
            // 压缩失败，返回原始数据
            return data;
        }
    }
    
    /**
     * Deflate压缩
     */
    private static byte[] compressDeflate(byte[] data) throws IOException {
        Deflater deflater = new Deflater(Deflater.BEST_COMPRESSION, true);
        deflater.setInput(data);
        deflater.finish();
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        
        while (!deflater.finished()) {
            int count = deflater.deflate(buffer);
            baos.write(buffer, 0, count);
        }
        
        deflater.end();
        return baos.toByteArray();
    }
    
    /**
     * 解析文件名中的偏移
     * 支持格式: 0x04b4a97c.png, 04b4a97c.png
     */
    private static long parseFileOffset(String fileName) {
        try {
            String name = fileName.toLowerCase();
            String hexPart = null;

            if (name.startsWith("0x") && name.endsWith(".png")) {
                // 格式: 0x04b4a97c.png
                hexPart = name.substring(2, name.length() - 4);
            } else if (name.endsWith(".png")) {
                // 格式: 04b4a97c.png
                String nameWithoutExt = name.substring(0, name.length() - 4);
                if (nameWithoutExt.matches("[0-9a-f]{8}")) {
                    hexPart = nameWithoutExt;
                }
            }

            if (hexPart != null && hexPart.length() == 8) {
                return Long.parseLong(hexPart, 16);
            }
        } catch (Exception e) {
            // 解析失败，使用默认值
        }
        return 0;
    }

    /**
     * 保持原始文件名不变
     * 不再进行文件名标准化，保持用户原始文件名
     */
    public static String normalizeFileName(String fileName) {
        // 直接返回原文件名，不进行任何修改
        // 这样可以保持WAS文件的原始文件名不变
        return fileName;
    }
    
    /**
     * 写入索引表
     */
    private static void writeIndexTable(RandomAccessFile raf, List<NpkPackEntry> entries) throws IOException {
        long indexOffset = raf.getFilePointer();

        for (NpkPackEntry entry : entries) {
            // 字段0: 使用原始文件名中的偏移，而不是在NPK中的偏移 (4字节)
            writeInt32LE(raf, (int)(entry.fileOffset & 0xFFFFFFFF));

            // 字段1: 数据偏移 (4字节)
            writeInt32LE(raf, (int)(entry.offset & 0xFFFFFFFF));

            // 字段2: 压缩大小 (4字节)
            writeInt32LE(raf, (int)entry.compressedSize);

            // 字段3: 原始大小 (4字节)
            writeInt32LE(raf, (int)entry.originalSize);

            // 字段4-5: 保留字段 (8字节)
            writeInt32LE(raf, 0);
            writeInt32LE(raf, 0);

            // 字段6: 压缩类型 (4字节)
            writeInt32LE(raf, entry.compressionType);
        }
    }
    
    /**
     * 写入文件名表
     */
    private static void writeFileNameTable(RandomAccessFile raf, List<NpkPackEntry> entries) throws IOException {
        // NXFN标识
        raf.write("NXFN".getBytes());
        
        // 保留字段 (12字节)
        raf.write(new byte[12]);
        
        // 写入文件名
        for (int i = 0; i < entries.size(); i++) {
            NpkPackEntry entry = entries.get(i);
            raf.write(entry.fileName.getBytes("UTF-8"));
            raf.write(0); // NULL终止符
            System.out.println("写入文件名[" + i + "]: " + entry.fileName); // 调试信息
        }
        
        // 4字节对齐
        long currentPos = raf.getFilePointer();
        long padding = (4 - (currentPos % 4)) % 4;
        if (padding > 0) {
            raf.write(new byte[(int)padding]);
        }
    }
    
    /**
     * 更新索引偏移
     */
    private static void updateIndexOffset(RandomAccessFile raf, int fileCount) throws IOException {
        // 计算索引表的实际位置
        // 索引表在所有文件数据之后
        raf.seek(32); // 跳过文件头

        long indexOffset = 32; // 从数据开始位置计算

        // 遍历所有文件数据，计算索引表的正确位置
        for (int i = 0; i < fileCount; i++) {
            try {
                // 跳过当前文件的数据
                raf.seek(indexOffset);

                // 读取文件大小信息来跳过文件数据
                // 这里需要根据实际的文件大小来跳过
                // 暂时使用简单的方法：直接定位到文件末尾前的索引表位置
                break;
            } catch (Exception e) {
                break;
            }
        }

        // 简化方法：索引表在文件名表之前
        // 先找到NXFN标识的位置
        raf.seek(32);
        long currentPos = 32;

        while (currentPos < raf.length() - 4) {
            raf.seek(currentPos);
            byte[] buffer = new byte[4];
            int read = raf.read(buffer);

            if (read == 4 && buffer[0] == 'N' && buffer[1] == 'X' &&
                buffer[2] == 'F' && buffer[3] == 'N') {
                // 找到NXFN，索引表在它之前
                indexOffset = currentPos - (fileCount * 28);
                break;
            }
            currentPos++;
        }

        // 更新文件头中的索引偏移
        raf.seek(20); // 索引偏移字段位置
        writeInt32LE(raf, (int)(indexOffset & 0xFFFFFFFF));
    }

    /**
     * 更新索引偏移（修复版本）
     */
    private static void updateIndexOffsetFixed(RandomAccessFile raf, long indexOffset) throws IOException {
        // 直接更新文件头中的索引偏移
        raf.seek(20); // 索引偏移字段位置
        writeInt32LE(raf, (int)(indexOffset & 0xFFFFFFFF));
    }
    
    /**
     * 写入32位小端序整数
     */
    private static void writeInt32LE(RandomAccessFile raf, int value) throws IOException {
        raf.write(value & 0xFF);
        raf.write((value >> 8) & 0xFF);
        raf.write((value >> 16) & 0xFF);
        raf.write((value >> 24) & 0xFF);
    }
    
    /**
     * NPK打包条目
     */
    private static class NpkPackEntry {
        String fileName;
        long offset;
        long originalSize;
        long compressedSize;
        int compressionType;
        long fileOffset; // 从文件名解析的偏移
        byte[] data;
    }
}
