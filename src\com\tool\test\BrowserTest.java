package com.tool.test;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;

/**
 * 文件浏览器测试工具 - 验证默认路径设置
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class BrowserTest extends JFrame {
    
    // 默认路径配置
    private static final String DEFAULT_NPK_SOURCE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_OUTPUT = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_FILE = "G:\\JXy2o\\GameClient3.0\\res";
    
    private JTextField sourceFolderField;
    private JTextField outputNpkField;
    private JTextField npkFileField;
    private JTextField outputDirField;
    
    public BrowserTest() {
        setTitle("文件浏览器测试");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        createUI();
        
        setSize(800, 400);
        setLocationRelativeTo(null);
    }
    
    private void createUI() {
        JTabbedPane tabbedPane = new JTabbedPane();
        
        // NPK打包测试标签页
        tabbedPane.addTab("NPK打包测试", createNpkPackTestPanel());
        
        // NPK查看测试标签页
        tabbedPane.addTab("NPK查看测试", createNpkViewTestPanel());
        
        add(tabbedPane, BorderLayout.CENTER);
        
        // 状态栏
        JLabel statusLabel = new JLabel("文件浏览器默认路径测试 - 验证浏览器是否打开到正确目录");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        add(statusLabel, BorderLayout.SOUTH);
    }
    
    private JPanel createNpkPackTestPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 源文件夹
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("源文件夹:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        sourceFolderField = new JTextField(DEFAULT_NPK_SOURCE, 30);
        panel.add(sourceFolderField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseSourceBtn = new JButton("浏览源文件夹");
        browseSourceBtn.addActionListener(new SourceFolderBrowserAction());
        panel.add(browseSourceBtn, gbc);
        
        // 输出NPK
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("输出NPK:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        outputNpkField = new JTextField(DEFAULT_NPK_OUTPUT, 30);
        panel.add(outputNpkField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputBtn = new JButton("浏览输出文件");
        browseOutputBtn.addActionListener(new OutputNpkBrowserAction());
        panel.add(browseOutputBtn, gbc);
        
        // 测试信息
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        JTextArea infoArea = new JTextArea();
        infoArea.setText("NPK打包浏览器测试\n\n");
        infoArea.append("测试说明:\n");
        infoArea.append("1. 点击'浏览源文件夹'应该打开到: " + DEFAULT_NPK_SOURCE + "\n");
        infoArea.append("2. 点击'浏览输出文件'应该打开到: " + DEFAULT_NPK_OUTPUT + "\n\n");
        infoArea.append("当前默认路径状态:\n");
        infoArea.append("源文件夹: " + (new File(DEFAULT_NPK_SOURCE).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.append("输出目录: " + (new File(DEFAULT_NPK_OUTPUT).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.setEditable(false);
        panel.add(new JScrollPane(infoArea), gbc);
        
        return panel;
    }
    
    private JPanel createNpkViewTestPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // NPK文件
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("NPK文件:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        npkFileField = new JTextField("", 30); // 留空，让用户选择
        panel.add(npkFileField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseNpkBtn = new JButton("浏览NPK文件");
        browseNpkBtn.addActionListener(new NpkFileBrowserAction());
        panel.add(browseNpkBtn, gbc);
        
        // 输出目录
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("输出目录:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        outputDirField = new JTextField(DEFAULT_NPK_OUTPUT, 30);
        panel.add(outputDirField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputDirBtn = new JButton("浏览输出目录");
        browseOutputDirBtn.addActionListener(new OutputDirBrowserAction());
        panel.add(browseOutputDirBtn, gbc);
        
        // 测试信息
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        JTextArea infoArea = new JTextArea();
        infoArea.setText("NPK查看浏览器测试\n\n");
        infoArea.append("测试说明:\n");
        infoArea.append("1. 点击'浏览NPK文件'应该打开到: " + DEFAULT_NPK_FILE + "\n");
        infoArea.append("2. 点击'浏览输出目录'应该打开到: " + DEFAULT_NPK_OUTPUT + "\n\n");
        infoArea.append("当前默认路径状态:\n");
        infoArea.append("NPK文件目录: " + (new File(DEFAULT_NPK_FILE).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.append("输出目录: " + (new File(DEFAULT_NPK_OUTPUT).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.setEditable(false);
        panel.add(new JScrollPane(infoArea), gbc);
        
        return panel;
    }
    
    // 源文件夹浏览器动作
    private class SourceFolderBrowserAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
            
            // 设置默认目录
            String currentPath = sourceFolderField.getText().trim();
            if (!currentPath.isEmpty() && new File(currentPath).exists()) {
                chooser.setCurrentDirectory(new File(currentPath));
            } else {
                File defaultDir = new File(DEFAULT_NPK_SOURCE);
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }
            
            if (chooser.showOpenDialog(BrowserTest.this) == JFileChooser.APPROVE_OPTION) {
                sourceFolderField.setText(chooser.getSelectedFile().getAbsolutePath());
            }
        }
    }
    
    // 输出NPK文件浏览器动作
    private class OutputNpkBrowserAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("NPK文件", "npk"));
            
            // 设置默认目录
            String currentPath = outputNpkField.getText().trim();
            if (!currentPath.isEmpty() && new File(currentPath).getParentFile() != null && new File(currentPath).getParentFile().exists()) {
                chooser.setCurrentDirectory(new File(currentPath).getParentFile());
            } else {
                File defaultDir = new File(DEFAULT_NPK_OUTPUT);
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }
            
            if (chooser.showSaveDialog(BrowserTest.this) == JFileChooser.APPROVE_OPTION) {
                String path = chooser.getSelectedFile().getAbsolutePath();
                if (!path.toLowerCase().endsWith(".npk")) {
                    path += ".npk";
                }
                outputNpkField.setText(path);
            }
        }
    }
    
    // NPK文件浏览器动作
    private class NpkFileBrowserAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileSelectionMode(JFileChooser.FILES_ONLY);
            chooser.setFileFilter(new javax.swing.filechooser.FileNameExtensionFilter("NPK文件 (*.npk)", "npk"));
            
            // 设置默认目录
            String currentPath = npkFileField.getText().trim();
            if (!currentPath.isEmpty() && new File(currentPath).getParentFile() != null && new File(currentPath).getParentFile().exists()) {
                chooser.setCurrentDirectory(new File(currentPath).getParentFile());
            } else {
                File defaultDir = new File(DEFAULT_NPK_FILE);
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }
            
            if (chooser.showOpenDialog(BrowserTest.this) == JFileChooser.APPROVE_OPTION) {
                npkFileField.setText(chooser.getSelectedFile().getAbsolutePath());
            }
        }
    }
    
    // 输出目录浏览器动作
    private class OutputDirBrowserAction implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser chooser = new JFileChooser();
            chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
            
            // 设置默认目录
            String currentPath = outputDirField.getText().trim();
            if (!currentPath.isEmpty() && new File(currentPath).exists()) {
                chooser.setCurrentDirectory(new File(currentPath));
            } else {
                File defaultDir = new File(DEFAULT_NPK_OUTPUT);
                if (defaultDir.exists() && defaultDir.isDirectory()) {
                    chooser.setCurrentDirectory(defaultDir);
                }
            }
            
            if (chooser.showOpenDialog(BrowserTest.this) == JFileChooser.APPROVE_OPTION) {
                outputDirField.setText(chooser.getSelectedFile().getAbsolutePath());
            }
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new BrowserTest().setVisible(true);
        });
    }
}
