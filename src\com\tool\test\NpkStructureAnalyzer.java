package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;

/**
 * NPK文件结构深度分析器
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkStructureAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 深度分析NPK文件结构
     */
    public void analyzeNpkStructure() {
        System.out.println("=== NPK文件结构深度分析 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            System.out.println("文件大小: " + formatFileSize(raf.length()));
            
            // 分析文件头
            analyzeHeader(raf);
            
            // 分析索引区域
            analyzeIndexArea(raf);
            
            // 尝试不同的索引结构
            tryDifferentIndexStructures(raf);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析文件头
     */
    private void analyzeHeader(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 文件头分析 ---");
        
        raf.seek(0);
        
        // 读取签名
        byte[] signature = new byte[4];
        raf.read(signature);
        System.out.println("签名: " + new String(signature));
        
        // 读取条目数量
        int entryCount = readInt32LE(raf);
        System.out.println("条目数量: " + entryCount);
        
        // 读取其他字段
        int field1 = readInt32LE(raf);
        int field2 = readInt32LE(raf);
        int field3 = readInt32LE(raf);
        
        System.out.println("字段1: " + field1);
        System.out.println("字段2: " + field2);
        System.out.println("字段3: " + field3);
        
        // 读取可能的索引偏移
        long pos = raf.getFilePointer();
        int indexOffset32 = readInt32LE(raf);
        raf.seek(pos);
        long indexOffset64 = readInt64LE(raf);
        
        System.out.println("可能的32位索引偏移: 0x" + Integer.toHexString(indexOffset32) + " (" + indexOffset32 + ")");
        System.out.println("可能的64位索引偏移: 0x" + Long.toHexString(indexOffset64) + " (" + indexOffset64 + ")");
        
        // 验证哪个偏移更合理
        long fileSize = raf.length();
        if (indexOffset32 > 0 && indexOffset32 < fileSize) {
            System.out.println("32位偏移看起来合理");
        }
        if (indexOffset64 > 0 && indexOffset64 < fileSize) {
            System.out.println("64位偏移看起来合理");
        }
    }
    
    /**
     * 分析索引区域
     */
    private void analyzeIndexArea(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 索引区域分析 ---");

        // 正确读取索引偏移 - 应该在偏移20处
        raf.seek(20);
        long indexOffset = readInt32LE(raf) & 0xFFFFFFFFL;

        System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
        
        if (indexOffset > 0 && indexOffset < raf.length()) {
            raf.seek(indexOffset);
            
            System.out.println("索引区域前64字节:");
            byte[] indexSample = new byte[64];
            raf.read(indexSample);
            
            for (int i = 0; i < indexSample.length; i += 16) {
                System.out.printf("%08X: ", (int)(indexOffset + i));
                for (int j = 0; j < 16 && i + j < indexSample.length; j++) {
                    System.out.printf("%02X ", indexSample[i + j] & 0xFF);
                }
                System.out.print(" | ");
                for (int j = 0; j < 16 && i + j < indexSample.length; j++) {
                    char c = (char)(indexSample[i + j] & 0xFF);
                    System.out.print(c >= 32 && c < 127 ? c : '.');
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 尝试不同的索引结构
     */
    private void tryDifferentIndexStructures(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 尝试不同的索引结构 ---");

        raf.seek(20);
        long indexOffset = readInt32LE(raf) & 0xFFFFFFFFL;
        
        if (indexOffset <= 0 || indexOffset >= raf.length()) {
            System.out.println("索引偏移无效，无法分析");
            return;
        }
        
        raf.seek(indexOffset);
        
        // 尝试结构1: 文件名长度 + 文件名 + 偏移 + 大小
        System.out.println("\n尝试结构1: 变长文件名结构");
        tryVariableLengthStructure(raf, indexOffset);
        
        // 尝试结构2: 固定长度结构
        System.out.println("\n尝试结构2: 固定长度结构");
        tryFixedLengthStructure(raf, indexOffset);
        
        // 尝试结构3: 文件名表 + 索引表分离
        System.out.println("\n尝试结构3: 分离的文件名表和索引表");
        trySeparatedStructure(raf, indexOffset);
    }
    
    /**
     * 尝试变长文件名结构
     */
    private void tryVariableLengthStructure(RandomAccessFile raf, long indexOffset) throws IOException {
        raf.seek(indexOffset);
        
        for (int i = 0; i < 5; i++) { // 只分析前5个条目
            try {
                long pos = raf.getFilePointer();
                
                // 读取文件名长度
                int nameLength = readInt32LE(raf);
                System.out.println("条目 " + i + " (位置: 0x" + Long.toHexString(pos) + "):");
                System.out.println("  文件名长度: " + nameLength);
                
                if (nameLength > 0 && nameLength < 256) {
                    // 读取文件名
                    byte[] nameBytes = new byte[nameLength];
                    raf.read(nameBytes);
                    String fileName = new String(nameBytes, "UTF-8").trim();
                    System.out.println("  文件名: " + fileName);
                    
                    // 读取文件信息
                    long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                    long size = readInt32LE(raf) & 0xFFFFFFFFL;
                    
                    System.out.println("  偏移: 0x" + Long.toHexString(offset));
                    System.out.println("  大小: " + formatFileSize(size));
                } else {
                    System.out.println("  文件名长度异常，跳过");
                    break;
                }
            } catch (Exception e) {
                System.out.println("  解析失败: " + e.getMessage());
                break;
            }
        }
    }
    
    /**
     * 尝试固定长度结构
     */
    private void tryFixedLengthStructure(RandomAccessFile raf, long indexOffset) throws IOException {
        raf.seek(indexOffset);
        
        for (int i = 0; i < 5; i++) { // 只分析前5个条目
            try {
                long pos = raf.getFilePointer();
                
                System.out.println("条目 " + i + " (位置: 0x" + Long.toHexString(pos) + "):");
                
                // 尝试32字节固定结构
                long field1 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field2 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field3 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field4 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field5 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field6 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field7 = readInt32LE(raf) & 0xFFFFFFFFL;
                long field8 = readInt32LE(raf) & 0xFFFFFFFFL;
                
                System.out.println("  字段1: 0x" + Long.toHexString(field1) + " (" + field1 + ")");
                System.out.println("  字段2: 0x" + Long.toHexString(field2) + " (" + field2 + ")");
                System.out.println("  字段3: 0x" + Long.toHexString(field3) + " (" + field3 + ")");
                System.out.println("  字段4: 0x" + Long.toHexString(field4) + " (" + field4 + ")");
                
                // 判断哪些字段可能是偏移和大小
                if (field1 < raf.length() && field2 < 100 * 1024 * 1024) {
                    System.out.println("  可能: 偏移=" + field1 + ", 大小=" + field2);
                }
                if (field2 < raf.length() && field3 < 100 * 1024 * 1024) {
                    System.out.println("  可能: 偏移=" + field2 + ", 大小=" + field3);
                }
                
            } catch (Exception e) {
                System.out.println("  解析失败: " + e.getMessage());
                break;
            }
        }
    }
    
    /**
     * 尝试分离的结构
     */
    private void trySeparatedStructure(RandomAccessFile raf, long indexOffset) throws IOException {
        System.out.println("分析是否有分离的文件名表和索引表...");
        
        // 这种结构通常在索引区域前有文件名表
        // 暂时跳过，需要更多信息
        System.out.println("需要更多信息来分析分离结构");
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 读取64位小端序整数
     */
    private long readInt64LE(RandomAccessFile raf) throws IOException {
        long low = readInt32LE(raf) & 0xFFFFFFFFL;
        long high = readInt32LE(raf) & 0xFFFFFFFFL;
        return low | (high << 32);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        NpkStructureAnalyzer analyzer = new NpkStructureAnalyzer();
        analyzer.analyzeNpkStructure();
    }
}
