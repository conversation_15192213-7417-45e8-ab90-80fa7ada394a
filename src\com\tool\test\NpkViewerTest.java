package com.tool.test;

import com.tool.npk.*;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.File;
import java.util.List;

/**
 * NPK查看器测试工具
 * 专门测试搜索、刷新、复制等功能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkViewerTest extends JFrame {
    
    private JList<NpkEntry> testList;
    private DefaultListModel<NpkEntry> testModel;
    private JTextField searchField;
    private JLabel statusLabel;
    private NpkFile currentNpkFile;
    
    public NpkViewerTest() {
        initializeUI();
        loadTestData();
    }
    
    private void initializeUI() {
        setTitle("NPK查看器功能测试");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // 创建控制面板
        JPanel controlPanel = new JPanel(new FlowLayout());
        
        searchField = new JTextField(20);
        JButton searchBtn = new JButton("搜索");
        JButton clearBtn = new JButton("清空");
        JButton refreshBtn = new JButton("刷新");
        JButton copyBtn = new JButton("复制文件名");
        
        controlPanel.add(new JLabel("搜索:"));
        controlPanel.add(searchField);
        controlPanel.add(searchBtn);
        controlPanel.add(clearBtn);
        controlPanel.add(refreshBtn);
        controlPanel.add(copyBtn);
        
        add(controlPanel, BorderLayout.NORTH);
        
        // 创建列表
        testModel = new DefaultListModel<>();
        testList = new JList<>(testModel);
        testList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane scrollPane = new JScrollPane(testList);
        scrollPane.setPreferredSize(new Dimension(600, 400));
        add(scrollPane, BorderLayout.CENTER);
        
        // 状态栏
        statusLabel = new JLabel("就绪");
        add(statusLabel, BorderLayout.SOUTH);
        
        // 事件处理
        searchBtn.addActionListener(e -> testSearch());
        clearBtn.addActionListener(e -> testClear());
        refreshBtn.addActionListener(e -> testRefresh());
        copyBtn.addActionListener(e -> testCopyFileName());
        
        // Enter键搜索
        searchField.addActionListener(e -> testSearch());
        
        pack();
        setLocationRelativeTo(null);
    }
    
    private void loadTestData() {
        statusLabel.setText("正在加载测试数据...");
        
        SwingWorker<NpkFile, Void> worker = new SwingWorker<NpkFile, Void>() {
            @Override
            protected NpkFile doInBackground() throws Exception {
                File npkFile = new File("item.npk");
                if (!npkFile.exists()) {
                    throw new RuntimeException("item.npk文件不存在");
                }
                
                return NpkTool.parseNpkFile(npkFile, null);
            }
            
            @Override
            protected void done() {
                try {
                    currentNpkFile = get();
                    
                    // 加载前100个条目用于测试
                    List<NpkEntry> entries = currentNpkFile.getEntries();
                    int loadCount = Math.min(100, entries.size());
                    
                    for (int i = 0; i < loadCount; i++) {
                        testModel.addElement(entries.get(i));
                    }
                    
                    statusLabel.setText("测试数据加载完成: " + loadCount + " 个条目");
                    
                } catch (Exception e) {
                    statusLabel.setText("加载失败: " + e.getMessage());
                    JOptionPane.showMessageDialog(NpkViewerTest.this, 
                        "加载测试数据失败:\n" + e.getMessage(), 
                        "错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        };
        
        worker.execute();
    }
    
    private void testSearch() {
        String searchText = searchField.getText().trim();
        statusLabel.setText("正在搜索: " + searchText);
        
        SwingWorker<List<NpkEntry>, Void> worker = new SwingWorker<List<NpkEntry>, Void>() {
            @Override
            protected List<NpkEntry> doInBackground() throws Exception {
                if (currentNpkFile == null) {
                    throw new RuntimeException("没有加载NPK文件");
                }
                
                // 模拟搜索延迟
                Thread.sleep(100);
                
                return NpkViewerHelper.searchEntries(currentNpkFile.getEntries(), searchText);
            }
            
            @Override
            protected void done() {
                try {
                    List<NpkEntry> results = get();
                    
                    // 更新列表
                    testModel.clear();
                    int displayCount = Math.min(100, results.size());
                    for (int i = 0; i < displayCount; i++) {
                        testModel.addElement(results.get(i));
                    }
                    
                    if (searchText.isEmpty()) {
                        statusLabel.setText("显示所有文件: " + displayCount + " 个");
                    } else {
                        statusLabel.setText("搜索结果: " + displayCount + " 个 (关键词: " + searchText + ")");
                    }
                    
                } catch (Exception e) {
                    statusLabel.setText("搜索失败: " + e.getMessage());
                }
            }
        };
        
        worker.execute();
    }
    
    private void testClear() {
        searchField.setText("");
        statusLabel.setText("已清空搜索");
        testSearch(); // 重新显示所有文件
    }
    
    private void testRefresh() {
        statusLabel.setText("正在刷新...");
        
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            @Override
            protected Void doInBackground() throws Exception {
                // 模拟刷新延迟
                Thread.sleep(200);
                return null;
            }
            
            @Override
            protected void done() {
                try {
                    if (currentNpkFile == null) {
                        statusLabel.setText("没有数据可刷新");
                        return;
                    }
                    
                    // 重新加载数据
                    testModel.clear();
                    List<NpkEntry> entries = currentNpkFile.getEntries();
                    int loadCount = Math.min(100, entries.size());
                    
                    for (int i = 0; i < loadCount; i++) {
                        testModel.addElement(entries.get(i));
                    }
                    
                    statusLabel.setText("刷新完成: " + loadCount + " 个条目");
                    
                } catch (Exception e) {
                    statusLabel.setText("刷新失败: " + e.getMessage());
                }
            }
        };
        
        worker.execute();
    }
    
    private void testCopyFileName() {
        NpkEntry selectedEntry = testList.getSelectedValue();
        if (selectedEntry == null) {
            statusLabel.setText("请先选择一个文件");
            return;
        }
        
        try {
            String fileName = selectedEntry.getFileName();
            
            // 复制到剪贴板
            java.awt.datatransfer.StringSelection stringSelection = 
                new java.awt.datatransfer.StringSelection(fileName);
            java.awt.datatransfer.Clipboard clipboard = 
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard();
            clipboard.setContents(stringSelection, null);
            
            statusLabel.setText("已复制文件名: " + fileName);
            
        } catch (Exception e) {
            statusLabel.setText("复制失败: " + e.getMessage());
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new NpkViewerTest().setVisible(true);
        });
    }
}
