package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件名表分析器
 * 目标：精确解析文件名表，找到剩余3082个条目的真实偏移和大小
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class FileNameTableAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 分析文件名表结构
     */
    public void analyzeFileNameTable() {
        System.out.println("=== 文件名表结构分析 ===");
        System.out.println("目标: 找到剩余3082个条目的真实偏移和大小");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            long indexOffset = 0x7354149L;
            long nameTableStart = indexOffset + (3083 * 56);
            
            System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
            System.out.println("文件名表开始: 0x" + Long.toHexString(nameTableStart));
            
            // 分析文件名表的详细结构
            analyzeNameTableStructure(raf, nameTableStart);
            
            // 尝试找到文件名与偏移的对应关系
            findNameOffsetMapping(raf, nameTableStart);
            
            // 搜索可能的额外索引信息
            searchAdditionalIndexInfo(raf, nameTableStart);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析文件名表结构
     */
    private void analyzeNameTableStructure(RandomAccessFile raf, long nameTableStart) throws IOException {
        System.out.println("\n--- 分析文件名表结构 ---");
        
        raf.seek(nameTableStart);
        long remainingSize = raf.length() - nameTableStart;
        
        System.out.println("文件名表大小: " + remainingSize + " 字节");
        
        // 读取前1KB进行分析
        byte[] sample = new byte[1024];
        int bytesRead = raf.read(sample);
        
        System.out.println("文件名表前1KB内容:");
        
        // 显示十六进制和文本
        for (int i = 0; i < bytesRead; i += 32) {
            System.out.printf("0x%08X: ", (int)(nameTableStart + i));
            
            // 十六进制
            for (int j = 0; j < 32 && i + j < bytesRead; j++) {
                System.out.printf("%02X ", sample[i + j] & 0xFF);
            }
            
            System.out.print(" | ");
            
            // 文本
            for (int j = 0; j < 32 && i + j < bytesRead; j++) {
                char c = (char)(sample[i + j] & 0xFF);
                System.out.print(c >= 32 && c < 127 ? c : '.');
            }
            
            System.out.println();
            
            if (i >= 256) break; // 只显示前8行
        }
        
        // 分析文件名模式
        analyzeFileNamePatterns(sample, bytesRead);
    }
    
    /**
     * 分析文件名模式
     */
    private void analyzeFileNamePatterns(byte[] data, int length) {
        System.out.println("\n分析文件名模式:");
        
        String text = new String(data, 0, length);
        
        // 查找文件名
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("[^\\x00-\\x1F]*\\.png");
        java.util.regex.Matcher matcher = pattern.matcher(text);
        
        int count = 0;
        while (matcher.find() && count < 10) {
            String fileName = matcher.group();
            int pos = matcher.start();
            
            System.out.println("文件名 " + (++count) + " (位置 " + pos + "): " + fileName);
        }
        
        // 分析分隔符
        System.out.println("\n分析分隔符:");
        int nullCount = 0;
        int backslashCount = 0;
        
        for (int i = 0; i < length; i++) {
            if (data[i] == 0) nullCount++;
            if (data[i] == '\\') backslashCount++;
        }
        
        System.out.println("NULL字符数量: " + nullCount);
        System.out.println("反斜杠数量: " + backslashCount);
    }
    
    /**
     * 寻找文件名与偏移的对应关系
     */
    private void findNameOffsetMapping(RandomAccessFile raf, long nameTableStart) throws IOException {
        System.out.println("\n--- 寻找文件名与偏移的对应关系 ---");
        
        // 策略1: 检查文件名表是否包含偏移信息
        raf.seek(nameTableStart);
        byte[] nameTableData = new byte[10000]; // 读取10KB
        int bytesRead = raf.read(nameTableData);
        
        // 查找可能的偏移模式
        System.out.println("搜索可能的偏移模式...");
        
        for (int i = 0; i < bytesRead - 8; i += 4) {
            // 尝试读取32位值
            int value = (nameTableData[i] & 0xFF) |
                       ((nameTableData[i+1] & 0xFF) << 8) |
                       ((nameTableData[i+2] & 0xFF) << 16) |
                       ((nameTableData[i+3] & 0xFF) << 24);
            
            long unsignedValue = value & 0xFFFFFFFFL;
            
            // 检查是否是合理的偏移
            if (unsignedValue > 0 && unsignedValue < raf.length() && unsignedValue % 4 == 0) {
                // 检查该偏移处是否有PNG文件头
                raf.seek(unsignedValue);
                byte[] header = new byte[8];
                raf.read(header);
                
                if (isPngHeader(header)) {
                    System.out.println("找到可能的PNG偏移: 0x" + Long.toHexString(unsignedValue) + 
                                     " (在文件名表位置: 0x" + Long.toHexString(nameTableStart + i) + ")");
                }
            }
        }
        
        // 策略2: 检查是否有交替的文件名和偏移结构
        analyzeAlternatingStructure(nameTableData, bytesRead, nameTableStart);
    }
    
    /**
     * 分析交替结构
     */
    private void analyzeAlternatingStructure(byte[] data, int length, long baseOffset) {
        System.out.println("\n分析可能的交替结构 (文件名 + 偏移/大小):");
        
        int pos = 0;
        int entryCount = 0;
        
        while (pos < length - 20 && entryCount < 10) {
            // 查找下一个文件名
            int nameStart = pos;
            while (pos < length && data[pos] != 0) {
                pos++;
            }
            
            if (pos > nameStart) {
                String fileName = new String(data, nameStart, pos - nameStart);
                
                if (fileName.endsWith(".png")) {
                    entryCount++;
                    System.out.println("条目 " + entryCount + ":");
                    System.out.println("  文件名: " + fileName);
                    
                    // 跳过NULL分隔符
                    while (pos < length && data[pos] == 0) {
                        pos++;
                    }
                    
                    // 检查后续是否有偏移/大小信息
                    if (pos + 8 <= length) {
                        long offset = readInt32LE(data, pos) & 0xFFFFFFFFL;
                        long size = readInt32LE(data, pos + 4) & 0xFFFFFFFFL;
                        
                        System.out.println("  可能的偏移: 0x" + Long.toHexString(offset));
                        System.out.println("  可能的大小: " + size);
                        
                        pos += 8;
                    }
                }
            }
            
            pos++;
        }
    }
    
    /**
     * 搜索额外的索引信息
     */
    private void searchAdditionalIndexInfo(RandomAccessFile raf, long nameTableStart) throws IOException {
        System.out.println("\n--- 搜索额外的索引信息 ---");
        
        // 检查文件末尾是否有额外的索引
        long fileSize = raf.length();
        long lastKB = Math.max(fileSize - 1024, nameTableStart);
        
        System.out.println("检查文件末尾1KB...");
        raf.seek(lastKB);
        
        byte[] endData = new byte[(int)(fileSize - lastKB)];
        raf.read(endData);
        
        // 查找可能的索引标识
        String endText = new String(endData);
        if (endText.contains("index") || endText.contains("INDEX") || endText.contains("idx")) {
            System.out.println("文件末尾可能包含索引信息");
        }
        
        // 检查是否有重复的偏移模式
        System.out.println("检查重复的偏移模式...");
        
        // 简单的模式检测
        for (int i = 0; i < endData.length - 8; i += 4) {
            long value = readInt32LE(endData, i) & 0xFFFFFFFFL;
            
            if (value > 0 && value < fileSize) {
                System.out.println("文件末尾发现可能的偏移: 0x" + Long.toHexString(value));
                break;
            }
        }
    }
    
    /**
     * 检查PNG文件头
     */
    private boolean isPngHeader(byte[] data) {
        if (data.length < 8) return false;
        return data[0] == (byte)0x89 && data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && data[7] == (byte)0x0A;
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(byte[] data, int offset) {
        return (data[offset] & 0xFF) |
               ((data[offset+1] & 0xFF) << 8) |
               ((data[offset+2] & 0xFF) << 16) |
               ((data[offset+3] & 0xFF) << 24);
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        FileNameTableAnalyzer analyzer = new FileNameTableAnalyzer();
        analyzer.analyzeFileNameTable();
    }
}
