# 皮肤ID同步工具修改说明

## 修改概述

根据用户需求，对皮肤ID批量同步修改工具进行了重要修改，主要变更如下：

### 最新修改（ID映射一致性）

**新增功能：**
- **ID映射一致性**：相同的原始ID使用相同的转换后ID
- **批量更新**：同时更新XLS中所有匹配的行
- **共享皮肤文件**：确保相同ID的物品共享同一个皮肤文件
- 自动跳过在XLS中未找到对应ID的文件
- 在预览表格中明确显示将被跳过的文件
- 提供详细的跳过统计信息

### 1. 文件名处理逻辑变更

**修改前：**
- 只处理纯数字文件名（如：`9134.png`）
- 基于原始数字ID转换为十六进制
- 从00000001开始的顺序命名

**修改后：**
- 支持任意格式的文件名（如：`weapon_sword.png`, `armor_001.png`, `item_potion_health_large.png`）
- 使用随机生成的8位十六进制ID
- 不再依赖原始文件名的数字格式

### 2. 随机ID生成机制

新增了随机十六进制ID生成功能：

```java
private String generateRandomHexId() {
    // 生成一个随机的32位整数，确保结果是8位十六进制
    long randomValue = random.nextLong() & 0xFFFFFFFFL;
    // 确保生成的十六进制至少以5-9或A-F开头（避免与小数值冲突）
    if (randomValue < 0x50000000L) {
        randomValue += 0x50000000L;
    }
    return String.format("%08X", randomValue);
}
```

**特点：**
- 生成8位大写十六进制ID
- 确保ID以5-9或A-F开头，避免与小数值冲突
- 随机性强，重复概率极低

### 3. XLS查找逻辑增强

新增了 `findXlsRowByFileName` 方法，支持多种匹配方式：

1. **直接匹配文件名**：原文件名与XLS中的值直接匹配
2. **数字匹配**：如果XLS中是数字，尝试与原文件名匹配
3. **十六进制转换匹配**：如果XLS中是十六进制格式，转换后匹配

### 4. 转换示例

**示例1：武器文件**
```
原文件名: weapon_sword.png
新文件名: 2025-A1B2C3D4.png
原皮肤ID: weapon_sword
新皮肤ID: 0xA1B2C3D4
```

**示例2：装备文件**
```
原文件名: armor_001.png
新文件名: 2025-F5E6D7C8.png
原皮肤ID: armor_001
新皮肤ID: 0xF5E6D7C8
```

**示例3：数字文件**
```
原文件名: 9134.png
新文件名: 2025-B9A8C7D6.png
原皮肤ID: 9134
新皮肤ID: 0xB9A8C7D6
```

### 示例5: ID映射一致性示例

**XLS中的相同ID：**
```
多情环    6120  -> 0xDDFAFD8E
多情环    6120  -> 0xDDFAFD8E  (相同)
多情环    6120  -> 0xDDFAFD8E  (相同)
多情环    6120  -> 0xDDFAFD8E  (相同)
```

**对应的文件转换：**
```
6120.png -> 2025-DDFAFD8E.png
```

**优势：**
- 所有相同ID的物品共享同一个皮肤文件
- 减少资源冗余，节省存储空间
- 保持游戏内物品外观的一致性

### 示例6: 跳过功能示例

**处理结果统计：**
```
扫描完成，总计 10 个文件
  - 准备转换: 6 个
  - 将跳过: 3 个 (XLS中未找到)
  - 已转换: 1 个

修改操作完成！成功: 6, 跳过: 3, 失败: 0
```

**预览表格状态：**
- `weapon_sword.png` → 状态：准备就绪
- `unknown_item.png` → 状态：将跳过(XLS中未找到)
- `2025-12345678.png` → 状态：已转换

## 修改的文件

### 1. SkinIdSyncTool.java
- 添加了 `Random` 对象用于生成随机ID
- **新增了 `idMappingCache` 映射表**，确保相同ID的一致性
- **新增了 `buildIdMapping` 方法**，预处理XLS文件建立ID映射
- **新增了 `updateAllMatchingXlsRows` 方法**，批量更新所有匹配行
- 修改了 `scanPngFiles` 方法，使用映射表确保ID一致性
- 新增了 `generateRandomHexId` 方法
- 新增了 `findXlsRowByFileName` 方法
- 更新了类注释，反映新的转换规则

### 2. 皮肤ID批量同步修改工具使用说明.md
- 更新了转换规则说明
- 修改了转换示例
- 更新了技术细节中的转换算法

### 3. 新增测试文件
- `RandomHexIdTest.java`：用于测试随机ID生成功能
- `SkipLogicTest.java`：用于测试跳过逻辑功能
- `IdMappingTest.java`：用于测试ID映射一致性功能
- `XlsDebugTool.java`：用于分析XLS文件和PNG文件
- `SkippedFilesReport.java`：用于生成跳过文件的详细报告
- `修改说明.md`：本文档

## 优势

1. **灵活性**：支持任意格式的文件名，不再限制为纯数字
2. **一致性**：相同的原始ID使用相同的转换后ID，确保共享皮肤文件
3. **批量处理**：同时更新XLS中所有匹配的行，提高效率
4. **资源优化**：减少重复皮肤文件，节省存储空间
5. **兼容性**：保持对已转换文件的识别和处理
6. **可扩展性**：XLS查找逻辑支持多种匹配方式
7. **智能跳过**：自动跳过XLS中未找到的文件，避免错误操作
8. **清晰反馈**：提供详细的处理状态和统计信息

## 测试验证

通过 `RandomHexIdTest` 测试验证了：
- 随机ID生成的唯一性（测试20个ID，重复率0%）
- ID格式的正确性（8位大写十六进制）
- 文件名转换的正确性
- 各种文件名格式的支持

通过 `SkipLogicTest` 测试验证了：
- 跳过逻辑的正确性（XLS中未找到的文件被正确跳过）
- 状态显示的准确性（预览表格正确显示文件状态）
- 统计信息的完整性（成功、跳过、失败的数量统计）

## 使用注意事项

1. **备份重要性**：修改前务必备份原始文件和XLS文件
2. **XLS匹配**：确保XLS文件中有对应的条目，否则会显示"XLS中未找到"
3. **文件权限**：确保对文件夹有读写权限
4. **唯一性**：虽然随机ID重复概率极低，但建议在大批量操作前进行测试

## 向后兼容性

- 工具仍然能够识别和处理已转换的文件（2025-xxxxxxxx.png格式）
- 保持原有的UI界面和操作流程
- 保持原有的XLS文件格式和结构
