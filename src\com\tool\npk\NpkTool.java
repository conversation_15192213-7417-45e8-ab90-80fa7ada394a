package com.tool.npk;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * NPK文件处理工具
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkTool {
    
    /**
     * 解析NPK文件
     */
    public static NpkFile parseNpkFile(File npkFile, BiConsumer<Integer, String> progressCallback) throws IOException {
        if (!npkFile.exists() || !npkFile.isFile()) {
            throw new IOException("NPK文件不存在: " + npkFile.getAbsolutePath());
        }
        
        NpkFile npkFileObj = new NpkFile();
        npkFileObj.setFile(npkFile);
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            // 读取NPK文件头
            NpkHeader header = readNpkHeader(raf, progressCallback);
            npkFileObj.setHeader(header);
            
            if (progressCallback != null) {
                progressCallback.accept(20, "正在读取文件索引...");
            }
            
            // 读取文件索引
            List<NpkEntry> entries = readNpkEntries(raf, header, progressCallback);
            npkFileObj.setEntries(entries);
            
            if (progressCallback != null) {
                progressCallback.accept(100, "NPK文件解析完成");
            }
        }
        
        return npkFileObj;
    }
    
    /**
     * 读取NPK文件头 - 基于真实文件分析的修复版本
     */
    private static NpkHeader readNpkHeader(RandomAccessFile raf, BiConsumer<Integer, String> progressCallback) throws IOException {
        NpkHeader header = new NpkHeader();

        if (progressCallback != null) {
            progressCallback.accept(5, "正在读取NPK文件头...");
        }

        // 读取文件签名 (NXPK)
        byte[] signatureBytes = new byte[4];
        raf.read(signatureBytes);
        String signature = new String(signatureBytes, StandardCharsets.UTF_8);
        header.setSignature(signature);

        // 验证NPK文件签名
        if (!"NXPK".equals(signature)) {
            throw new IOException("不是有效的NPK文件，期望签名: NXPK，实际签名: " + signature);
        }

        // 读取条目数量
        int entryCount = readInt32LE(raf);
        header.setEntryCount(entryCount);

        // 读取其他头部信息
        header.setUnknownVar(readInt32LE(raf));
        header.setEncryptionMode(readInt32LE(raf));
        header.setHashMode(readInt32LE(raf));

        // 读取索引偏移 - 根据真实文件分析，偏移在第20字节处
        raf.seek(20); // 跳转到正确的索引偏移位置
        long indexOffset = readInt32LE(raf) & 0xFFFFFFFFL; // 读取为无符号32位
        header.setIndexOffset(indexOffset);

        // 验证索引偏移的合理性
        long fileSize = raf.length();
        if (indexOffset <= 0 || indexOffset >= fileSize) {
            throw new IOException("无效的索引偏移: 0x" + Long.toHexString(indexOffset) +
                                ", 文件大小: " + fileSize);
        }

        // 设置默认配置信息
        header.setConfigName("Westward Journey");
        header.setInfoSize(28);
        header.setDecryptionKey(150);

        if (progressCallback != null) {
            progressCallback.accept(10, "NPK文件头读取完成，条目数: " + entryCount +
                                  ", 索引偏移: 0x" + Long.toHexString(indexOffset));
        }

        return header;
    }
    
    /**
     * 读取NPK文件条目 - 智能解析
     */
    private static List<NpkEntry> readNpkEntries(RandomAccessFile raf, NpkHeader header,
                                                BiConsumer<Integer, String> progressCallback) throws IOException {
        // 预分配容量以减少扩容次数
        List<NpkEntry> entries = new ArrayList<>(header.getEntryCount());

        long indexOffset = header.getIndexOffset();
        long fileLength = raf.length();

        // 验证索引偏移的有效性
        if (indexOffset < 0 || indexOffset >= fileLength) {
            throw new IOException("无效的索引偏移: 0x" + Long.toHexString(indexOffset) +
                                ", 文件大小: " + fileLength);
        }

        // 跳转到索引位置
        raf.seek(indexOffset);

        int entryCount = header.getEntryCount();
        if (progressCallback != null) {
            progressCallback.accept(15, "开始读取 " + entryCount + " 个文件条目...");
        }

        // 计算每个条目的大小
        long indexSize = fileLength - indexOffset;
        int estimatedEntrySize = (int)(indexSize / entryCount);

        if (progressCallback != null) {
            progressCallback.accept(18, "估算条目大小: " + estimatedEntrySize + " 字节");
        }

        // 基于真实NPK格式分析，使用正确的28字节条目结构
        if (progressCallback != null) {
            progressCallback.accept(20, "使用真实NPK格式解析 (28字节条目结构)");
        }
        entries = readRealNpkFormat(raf, header, progressCallback);

        return entries;
    }

    /**
     * 读取固定长度的NPK条目 - 修复版本
     */
    private static List<NpkEntry> readFixedLengthEntries(RandomAccessFile raf, NpkHeader header,
                                                        BiConsumer<Integer, String> progressCallback) throws IOException {
        List<NpkEntry> entries = new ArrayList<>();
        int entryCount = header.getEntryCount();

        if (progressCallback != null) {
            progressCallback.accept(20, "开始读取 " + entryCount + " 个固定长度条目...");
        }

        for (int i = 0; i < entryCount; i++) {
            try {
                long currentPos = raf.getFilePointer();
                if (currentPos + 32 > raf.length()) {
                    if (progressCallback != null) {
                        progressCallback.accept(-1, "到达文件末尾，已读取 " + i + " 个条目");
                    }
                    break;
                }

                NpkEntry entry = readRealNpkEntry(raf, i, new ArrayList<>());
                if (entry != null) {
                    entries.add(entry);
                }

                // 更新进度
                if (progressCallback != null && (i + 1) % 1000 == 0) {
                    int progress = 20 + (i * 70 / entryCount);
                    progressCallback.accept(progress, "正在读取条目: " + (i + 1) + "/" + entryCount);
                }
            } catch (Exception e) {
                if (progressCallback != null) {
                    progressCallback.accept(-1, "读取条目 " + (i + 1) + " 时出错: " + e.getMessage());
                }
                // 尝试跳过损坏的条目
                try {
                    long currentPos = raf.getFilePointer();
                    long nextPos = header.getIndexOffset() + (i + 1) * 32;
                    if (nextPos < raf.length()) {
                        raf.seek(nextPos);
                    } else {
                        break;
                    }
                } catch (IOException skipError) {
                    break;
                }
            }
        }

        if (progressCallback != null) {
            progressCallback.accept(90, "成功读取 " + entries.size() + " 个条目");
        }

        return entries;
    }

    /**
     * 读取变长的NPK条目
     */
    private static List<NpkEntry> readVariableLengthEntries(RandomAccessFile raf, NpkHeader header,
                                                           BiConsumer<Integer, String> progressCallback) throws IOException {
        List<NpkEntry> entries = new ArrayList<>();
        int entryCount = header.getEntryCount();

        if (progressCallback != null) {
            progressCallback.accept(20, "使用变长条目解析模式");
        }

        // 变长解析暂时返回空列表，避免解析错误
        // 可以在后续版本中实现更复杂的变长解析逻辑

        return entries;
    }

    /**
     * 读取变长条目但使用固定步长 - 基于真实分析的方法
     */
    private static List<NpkEntry> readVariableLengthEntriesWithFixedStep(RandomAccessFile raf, NpkHeader header,
                                                                        BiConsumer<Integer, String> progressCallback) throws IOException {
        List<NpkEntry> entries = new ArrayList<>();
        int entryCount = header.getEntryCount();
        long indexOffset = header.getIndexOffset();

        // 基于深度分析，使用56字节作为固定步长，但读取更多条目
        int stepSize = 56;

        // 计算实际可读取的条目数
        long indexSize = raf.length() - indexOffset;
        int maxPossibleEntries = (int)(indexSize / stepSize);
        int actualEntryCount = Math.min(entryCount, maxPossibleEntries);

        if (progressCallback != null) {
            progressCallback.accept(20, "开始读取 " + actualEntryCount + " 个变长条目 (步长: " + stepSize + " 字节)...");
        }

        for (int i = 0; i < actualEntryCount; i++) {
            try {
                // 计算当前条目的位置
                long entryPos = indexOffset + (i * stepSize);

                if (entryPos + stepSize > raf.length()) {
                    if (progressCallback != null) {
                        progressCallback.accept(-1, "到达文件末尾，已读取 " + i + " 个条目");
                    }
                    break;
                }

                raf.seek(entryPos);
                NpkEntry entry = readRealNpkEntry(raf, i, new ArrayList<>());
                if (entry != null) {
                    entries.add(entry);
                } else {
                    // 如果连续遇到无效条目，可能已经到达索引末尾
                    if (progressCallback != null) {
                        progressCallback.accept(-1, "条目 " + (i + 1) + " 无效，可能已到达有效索引末尾");
                    }
                    // 继续尝试几个条目，如果都无效则停止
                    int consecutiveFailures = 1;
                    for (int j = i + 1; j < Math.min(i + 10, actualEntryCount); j++) {
                        long nextEntryPos = indexOffset + (j * stepSize);
                        if (nextEntryPos + stepSize > raf.length()) break;

                        raf.seek(nextEntryPos);
                        NpkEntry nextEntry = readRealNpkEntry(raf, j, new ArrayList<>());
                        if (nextEntry == null) {
                            consecutiveFailures++;
                        } else {
                            break;
                        }
                    }

                    if (consecutiveFailures >= 5) {
                        if (progressCallback != null) {
                            progressCallback.accept(-1, "连续5个条目无效，停止解析。已成功解析 " + entries.size() + " 个条目");
                        }
                        break;
                    }
                }

                // 更新进度
                if (progressCallback != null && (i + 1) % 1000 == 0) {
                    int progress = 20 + (i * 70 / actualEntryCount);
                    progressCallback.accept(progress, "正在读取条目: " + (i + 1) + "/" + actualEntryCount);
                }

            } catch (Exception e) {
                if (progressCallback != null) {
                    progressCallback.accept(-1, "读取条目 " + (i + 1) + " 时出错: " + e.getMessage());
                }
                // 继续处理下一个条目
            }
        }

        if (progressCallback != null) {
            progressCallback.accept(90, "成功读取 " + entries.size() + " 个条目");
        }

        return entries;
    }

    /**
     * 真实NPK格式解析 - 基于文档分析的正确实现
     */
    private static List<NpkEntry> readRealNpkFormat(RandomAccessFile raf, NpkHeader header,
                                                   BiConsumer<Integer, String> progressCallback) throws IOException {
        // 预分配容量以减少扩容次数
        List<NpkEntry> entries = new ArrayList<>(header.getEntryCount());
        long indexOffset = header.getIndexOffset();
        int fileCount = header.getEntryCount();

        if (progressCallback != null) {
            progressCallback.accept(20, "开始真实NPK格式解析: " + fileCount + " 个条目 (28字节结构)");
        }

        // 第一步：读取文件名表
        List<String> fileNames = readFileNameTable(raf, indexOffset, fileCount);

        if (progressCallback != null) {
            progressCallback.accept(40, "文件名表解析完成: " + fileNames.size() + " 个文件名");
        }

        // 第二步：解析索引表 (每个条目28字节)
        for (int i = 0; i < fileCount; i++) {
            try {
                long entryPos = indexOffset + (i * 28); // 真实的28字节条目大小

                if (entryPos + 28 > raf.length()) {
                    break;
                }

                raf.seek(entryPos);
                NpkEntry entry = readRealNpkEntry(raf, i, fileNames);
                if (entry != null) {
                    entries.add(entry);
                }

                // 更新进度
                if (progressCallback != null && (i + 1) % 1000 == 0) {
                    int progress = 40 + (i * 50 / fileCount);
                    progressCallback.accept(progress, "索引解析: " + (i + 1) + "/" + fileCount);
                }

            } catch (Exception e) {
                if (progressCallback != null) {
                    progressCallback.accept(-1, "条目 " + (i + 1) + " 解析失败: " + e.getMessage());
                }
            }
        }

        if (progressCallback != null) {
            progressCallback.accept(90, "真实NPK格式解析完成: " + entries.size() + " 个条目");
        }

        return entries;
    }

    /**
     * 读取文件名表 - 基于NXFN结构
     */
    private static List<String> readFileNameTable(RandomAccessFile raf, long indexOffset, int fileCount) throws IOException {
        List<String> fileNames = new ArrayList<>();

        System.out.println("文件名表读取调试信息:");
        System.out.println("  索引偏移: 0x" + Long.toHexString(indexOffset));
        System.out.println("  文件数量: " + fileCount);

        try {
            // 尝试多个可能的文件名表位置
            long[] possibleOffsets = {
                indexOffset + (fileCount * 28) + 16,  // 原始计算
                indexOffset + (fileCount * 28),       // 无头部
                indexOffset + (fileCount * 32),       // 32字节条目
                indexOffset + (fileCount * 32) + 16   // 32字节条目+头部
            };

            boolean foundNXFN = false;

            for (long nameTableOffset : possibleOffsets) {
                System.out.println("  尝试文件名表偏移: 0x" + Long.toHexString(nameTableOffset));

                if (nameTableOffset >= raf.length()) {
                    System.out.println("    偏移超出文件长度，跳过");
                    continue;
                }

                raf.seek(nameTableOffset);

                // 查找NXFN标识，搜索更大范围
                byte[] buffer = new byte[2048];
                int bytesRead = raf.read(buffer);
                System.out.println("    读取到 " + bytesRead + " 字节数据");

                for (int i = 0; i <= bytesRead - 4; i++) {
                    if (buffer[i] == 'N' && buffer[i+1] == 'X' &&
                        buffer[i+2] == 'F' && buffer[i+3] == 'N') {

                        foundNXFN = true;
                        System.out.println("    找到NXFN标识，位置: " + i);

                        // 找到NXFN，跳过头部信息
                        raf.seek(nameTableOffset + i + 16);

                        // 读取文件名
                        for (int j = 0; j < fileCount; j++) {
                            String fileName = readNullTerminatedString(raf);
                            if (fileName != null && !fileName.isEmpty()) {
                                // 提取文件名的最后部分
                                String[] parts = fileName.split("[\\\\/]");
                                String actualFileName = parts[parts.length - 1];
                                fileNames.add(actualFileName);
                                System.out.println("    读取到文件名[" + j + "]: " + actualFileName);
                            } else {
                                fileNames.add(null);
                                System.out.println("    文件名[" + j + "]为空或null");
                            }
                        }
                        break;
                    }
                }

                if (foundNXFN) {
                    break; // 找到了，退出外层循环
                } else {
                    System.out.println("    在此位置未找到NXFN标识");
                    // 显示前32字节的十六进制内容用于调试
                    System.out.print("    前32字节内容: ");
                    for (int i = 0; i < Math.min(32, bytesRead); i++) {
                        System.out.printf("%02X ", buffer[i] & 0xFF);
                    }
                    System.out.println();
                }
            }

            if (!foundNXFN) {
                System.out.println("  所有位置都未找到NXFN标识！");
            }

        } catch (Exception e) {
            // 文件名表读取失败，返回空列表，让调用方处理
            System.err.println("文件名表读取失败: " + e.getMessage());
        }

        // 确保文件名数量匹配，但不生成默认文件名
        while (fileNames.size() < fileCount) {
            fileNames.add(null); // 添加null，让readRealNpkEntry方法处理
        }

        return fileNames;
    }

    /**
     * 读取以NULL结尾的字符串
     */
    private static String readNullTerminatedString(RandomAccessFile raf) throws IOException {
        StringBuilder sb = new StringBuilder();
        int b;

        while ((b = raf.read()) != -1 && b != 0) {
            if (b >= 32 && b < 127) { // 可打印ASCII字符
                sb.append((char)b);
            }
        }

        return sb.toString();
    }





    /**
     * 读取真实NPK条目 - 基于文档分析的28字节结构
     */
    private static NpkEntry readRealNpkEntry(RandomAccessFile raf, int entryIndex, List<String> fileNames) throws IOException {
        NpkEntry entry = new NpkEntry();

        try {
            // 基于文档分析的真实NPK条目结构 (28字节)
            // 字段0: 原始文件偏移或标识 (4字节)
            long originalOffset = readInt32LE(raf) & 0xFFFFFFFFL;

            // 字段1: 文件数据偏移 (4字节)
            long dataOffset = readInt32LE(raf) & 0xFFFFFFFFL;

            // 字段2: 压缩大小 (4字节)
            long compressedSize = readInt32LE(raf) & 0xFFFFFFFFL;

            // 字段3: 原始大小 (4字节)
            long originalSize = readInt32LE(raf) & 0xFFFFFFFFL;

            // 字段4-5: 未知字段 (8字节)
            long field4 = readInt32LE(raf) & 0xFFFFFFFFL;
            long field5 = readInt32LE(raf) & 0xFFFFFFFFL;

            // 字段6: 压缩类型 (4字节) - 2=LZ4, 3=ZSTD
            long compressionType = readInt32LE(raf) & 0xFFFFFFFFL;

            // 验证数据的合理性
            if (dataOffset >= raf.length() || compressedSize <= 0 || compressedSize > 100 * 1024 * 1024) {
                return null;
            }

            // 获取文件名 - 优先使用文件名表中的原始文件名
            String fileName;
            if (fileNames != null && entryIndex < fileNames.size() &&
                fileNames.get(entryIndex) != null && !fileNames.get(entryIndex).isEmpty() &&
                !fileNames.get(entryIndex).startsWith("file_")) {
                // 使用文件名表中存储的原始文件名
                fileName = fileNames.get(entryIndex);
            } else {
                // 如果文件名表中没有有效文件名，则根据偏移量和文件类型生成
                long fileNameOffset = (originalOffset != 0) ? originalOffset : dataOffset;
                fileName = generateFileNameWithType(raf, dataOffset, fileNameOffset, compressedSize, (int)compressionType);
            }

            // 设置条目信息
            entry.setFileName(fileName);
            entry.setOffset(dataOffset); // 使用数据偏移作为实际偏移
            entry.setCompressedSize(compressedSize);
            entry.setOriginalSize(originalSize);
            entry.setCrc32(field4); // 使用field4作为CRC
            entry.setCompressionType((int)compressionType);

            return entry;

        } catch (Exception e) {
            throw new IOException("解析真实NPK条目失败 entry: " + entryIndex + " - " + e.getMessage());
        }
    }

    /**
     * 根据文件内容生成带类型的文件名
     */
    private static String generateFileNameWithType(RandomAccessFile raf, long dataOffset, long fileNameOffset, long compressedSize, int compressionType) {
        try {
            // 保存当前位置
            long currentPos = raf.getFilePointer();

            // 读取文件头部数据来判断类型
            raf.seek(dataOffset);
            byte[] headerData = new byte[Math.min(16, (int)compressedSize)];
            int bytesRead = raf.read(headerData);

            // 恢复位置
            raf.seek(currentPos);

            if (bytesRead > 0) {
                // 检测文件类型
                String extension = detectFileType(headerData, compressionType);
                return String.format("0x%08x.%s", fileNameOffset, extension);
            }
        } catch (Exception e) {
            // 如果检测失败，使用默认扩展名
        }

        // 默认使用png扩展名
        return String.format("0x%08x.png", fileNameOffset);
    }

    /**
     * 根据文件头部数据检测文件类型
     */
    private static String detectFileType(byte[] headerData, int compressionType) {
        if (headerData.length < 2) {
            return "was"; // 默认改为was，因为用户主要打包was文件
        }
        // 如果是压缩数据，先尝试解压一小部分来检测
        byte[] dataToCheck = headerData;
        if (compressionType > 0) {
            try {
                byte[] decompressed = decompressHeaderData(headerData, compressionType);
                if (decompressed != null && decompressed.length >= 2) {
                    dataToCheck = decompressed;
                }
            } catch (Exception e) {
                // 解压失败，使用原始数据
            }
        }
        // WAS文件头: "SP" 或 "SH"
        if (dataToCheck.length >= 2) {
            char firstChar = (char)(dataToCheck[0] & 0xFF);
            char secondChar = (char)(dataToCheck[1] & 0xFF);
            String header = "" + firstChar + secondChar;

            if ("SP".equals(header) || "SH".equals(header)) {
                return "was";
            }
        }
        // PNG文件头: 89 50 4E 47 (‰PNG)
        if (dataToCheck.length >= 4 &&
            (dataToCheck[0] & 0xFF) == 0x89 &&
            (dataToCheck[1] & 0xFF) == 0x50 &&
            (dataToCheck[2] & 0xFF) == 0x4E &&
            (dataToCheck[3] & 0xFF) == 0x47) {
            return "png";
        }
        // 检查是否是PNG数据（使用现有的isPngData方法）
        if (isPngData(dataToCheck)) {
            return "png";
        }
        // 如果都不匹配，默认认为是WAS文件
        return "was";
    }

    /**
     * 解压头部数据用于类型检测
     */
    private static byte[] decompressHeaderData(byte[] compressedData, int compressionType) {
        try {
            if (compressionType == 1) {
                // Deflate压缩
                return decompressType1(compressedData, Math.min(64, compressedData.length * 2));
            }
            // 其他压缩类型暂时不处理
        } catch (Exception e) {
            // 解压失败
        }
        return null;
    }


    
    /**
     * 提取NPK文件中的指定文件 - 高性能版本
     */
    public static byte[] extractFile(NpkFile npkFile, NpkEntry entry, BiConsumer<Integer, String> progressCallback) throws IOException {
        File file = npkFile.getFile();

        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            raf.seek(entry.getOffset());

            // 使用压缩大小读取数据
            long compressedSize = entry.getCompressedSize();
            if (compressedSize <= 0 || compressedSize > 50 * 1024 * 1024) { // 50MB限制
                return null;
            }

            // 读取压缩数据
            byte[] compressedData = new byte[(int)compressedSize];
            int totalBytesRead = 0;
            byte[] buffer = new byte[Math.min(1048576, (int)compressedSize)]; // 1MB缓冲区

            while (totalBytesRead < compressedSize) {
                int toRead = (int)Math.min(buffer.length, compressedSize - totalBytesRead);
                int bytesRead = raf.read(buffer, 0, toRead);

                if (bytesRead == -1) {
                    break;
                }

                System.arraycopy(buffer, 0, compressedData, totalBytesRead, bytesRead);
                totalBytesRead += bytesRead;
            }

            if (totalBytesRead != compressedSize) {
                return null; // 读取失败
            }

            // 检查是否需要解压缩
            int compressionType = entry.getCompressionType();
            if (compressionType == 1) {
                // 压缩类型1 - 尝试多种解压缩方法
                return decompressType1(compressedData, entry.getOriginalSize());
            } else if (compressionType == 2) {
                // LZ4压缩
                return decompressLZ4(compressedData, entry.getOriginalSize());
            } else if (compressionType == 3) {
                // ZSTD压缩
                return decompressZSTD(compressedData, entry.getOriginalSize());
            } else if (compressedData.length != entry.getOriginalSize()) {
                // 可能是其他压缩格式，尝试zlib
                return decompressZlib(compressedData);
            } else {
                // 未压缩
                return compressedData;
            }
        }
    }
    
    /**
     * 检查是否是zlib压缩数据
     */
    private static boolean isZlibCompressed(byte[] data) {
        if (data.length < 2) {
            return false;
        }

        // zlib压缩数据通常以78 9C开头
        return (data[0] & 0xFF) == 0x78 && (data[1] & 0xFF) == 0x9C;
    }

    /**
     * 使用zlib解压缩数据
     */
    private static byte[] decompressZlib(byte[] compressedData) {
        try {
            java.util.zip.Inflater inflater = new java.util.zip.Inflater();
            inflater.setInput(compressedData);

            java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream();
            byte[] buffer = new byte[8192];

            while (!inflater.finished()) {
                int count = inflater.inflate(buffer);
                outputStream.write(buffer, 0, count);
            }

            inflater.end();
            return outputStream.toByteArray();

        } catch (Exception e) {
            // 解压失败，返回原始数据
            return compressedData;
        }
    }

    /**
     * 压缩类型1解压缩 - 针对NPK中最常见的压缩格式
     */
    private static byte[] decompressType1(byte[] compressedData, long originalSize) {
        try {
            // 方法1: 尝试标准zlib解压缩
            try {
                byte[] result = decompressZlib(compressedData);
                if (result.length == originalSize) {
                    return result; // 不再只检查PNG，支持所有文件类型
                }
            } catch (Exception e) {
                // zlib失败，尝试其他方法
            }

            // 方法2: 尝试原始deflate解压缩
            try {
                java.util.zip.Inflater inflater = new java.util.zip.Inflater(true); // 原始deflate
                inflater.setInput(compressedData);

                byte[] result = new byte[(int)originalSize];
                int resultLength = inflater.inflate(result);
                inflater.end();

                if (resultLength == originalSize) {
                    return result; // 不再只检查PNG，支持所有文件类型
                }
            } catch (Exception e) {
                // deflate失败
            }

            // 方法3: 跳过可能的头部后尝试解压缩
            for (int skip = 0; skip <= 16; skip += 2) {
                if (skip >= compressedData.length) break;

                try {
                    byte[] dataToDecompress = new byte[compressedData.length - skip];
                    System.arraycopy(compressedData, skip, dataToDecompress, 0, dataToDecompress.length);

                    java.util.zip.Inflater inflater = new java.util.zip.Inflater(true);
                    inflater.setInput(dataToDecompress);

                    byte[] result = new byte[(int)originalSize];
                    int resultLength = inflater.inflate(result);
                    inflater.end();

                    if (resultLength == originalSize) {
                        return result; // 不再只检查PNG，支持所有文件类型
                    }
                } catch (Exception e) {
                    // 继续尝试下一个偏移
                }
            }

            // 如果所有解压缩方法都失败，返回原始数据
            return compressedData;

        } catch (Exception e) {
            return compressedData;
        }
    }

    /**
     * 检查是否是PNG数据
     */
    private static boolean isPngData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }

        return data[0] == (byte)0x89 &&
               data[1] == (byte)0x50 &&
               data[2] == (byte)0x4E &&
               data[3] == (byte)0x47 &&
               data[4] == (byte)0x0D &&
               data[5] == (byte)0x0A &&
               data[6] == (byte)0x1A &&
               data[7] == (byte)0x0A;
    }

    /**
     * LZ4解压缩 - 改进版本
     */
    private static byte[] decompressLZ4(byte[] compressedData, long originalSize) {
        try {
            // 方法1: 尝试简单的LZ4块解压缩
            byte[] result = decompressLZ4Block(compressedData, (int)originalSize);
            if (result != null && result.length == originalSize) {
                return result; // 不再只检查PNG，支持所有文件类型
            }

            // 方法2: 尝试跳过可能的头部
            for (int skip = 0; skip <= 16; skip += 4) {
                if (skip >= compressedData.length) break;

                try {
                    byte[] dataToDecompress = new byte[compressedData.length - skip];
                    System.arraycopy(compressedData, skip, dataToDecompress, 0, dataToDecompress.length);

                    byte[] blockResult = decompressLZ4Block(dataToDecompress, (int)originalSize);
                    if (blockResult != null && blockResult.length == originalSize) {
                        return blockResult; // 不再只检查PNG，支持所有文件类型
                    }
                } catch (Exception e) {
                    // 继续尝试下一个偏移
                }
            }

            // 方法3: Fallback到其他解压缩方法
            return decompressType1(compressedData, originalSize);

        } catch (Exception e) {
            return compressedData;
        }
    }

    /**
     * 简单的LZ4块解压缩实现
     */
    private static byte[] decompressLZ4Block(byte[] compressed, int originalSize) {
        try {
            byte[] output = new byte[originalSize];
            int srcPos = 0;
            int dstPos = 0;

            while (srcPos < compressed.length && dstPos < originalSize) {
                // 读取token
                int token = compressed[srcPos++] & 0xFF;

                // 字面量长度
                int literalLength = token >>> 4;
                if (literalLength == 15) {
                    // 扩展长度
                    while (srcPos < compressed.length) {
                        int b = compressed[srcPos++] & 0xFF;
                        literalLength += b;
                        if (b != 255) break;
                    }
                }

                // 复制字面量
                if (literalLength > 0) {
                    if (srcPos + literalLength > compressed.length ||
                        dstPos + literalLength > originalSize) {
                        break;
                    }
                    System.arraycopy(compressed, srcPos, output, dstPos, literalLength);
                    srcPos += literalLength;
                    dstPos += literalLength;
                }

                // 检查是否到达末尾
                if (srcPos >= compressed.length) break;

                // 读取偏移
                if (srcPos + 1 >= compressed.length) break;
                int offset = (compressed[srcPos] & 0xFF) | ((compressed[srcPos + 1] & 0xFF) << 8);
                srcPos += 2;

                if (offset == 0) break;

                // 匹配长度
                int matchLength = (token & 0x0F) + 4;
                if (matchLength == 19) {
                    // 扩展长度
                    while (srcPos < compressed.length) {
                        int b = compressed[srcPos++] & 0xFF;
                        matchLength += b;
                        if (b != 255) break;
                    }
                }

                // 复制匹配
                int matchStart = dstPos - offset;
                if (matchStart < 0 || dstPos + matchLength > originalSize) {
                    break;
                }

                for (int i = 0; i < matchLength; i++) {
                    if (dstPos >= originalSize) break;
                    output[dstPos++] = output[matchStart + i];
                }
            }

            // 验证结果
            if (dstPos == originalSize) {
                return output; // 不再只检查PNG，支持所有文件类型
            }

        } catch (Exception e) {
            // LZ4解压缩失败
        }

        return null;
    }

    /**
     * ZSTD解压缩
     */
    private static byte[] decompressZSTD(byte[] compressedData, long originalSize) {
        try {
            // 简单的ZSTD解压缩实现
            // 注意：这是一个简化版本，真实的ZSTD可能需要专门的库

            if (compressedData.length >= 4) {
                // ZSTD魔数检查
                int magic = (compressedData[0] & 0xFF) |
                           ((compressedData[1] & 0xFF) << 8) |
                           ((compressedData[2] & 0xFF) << 16) |
                           ((compressedData[3] & 0xFF) << 24);

                if (magic == 0xFD2FB528) { // ZSTD magic number
                    // 跳过ZSTD头部，尝试解压缩
                    // 这里需要专门的ZSTD库，暂时返回原数据
                    return compressedData;
                }
            }

            // Fallback到zlib
            return decompressZlib(compressedData);

        } catch (Exception e) {
            return compressedData;
        }
    }

    /**
     * 解压数据 (兼容旧方法)
     */
    private static byte[] decompressData(byte[] compressedData, NpkEntry entry, BiConsumer<Integer, String> progressCallback) {
        int compressionType = entry.getCompressionType();
        if (compressionType == 1) {
            return decompressType1(compressedData, entry.getOriginalSize());
        } else if (compressionType == 2) {
            return decompressLZ4(compressedData, entry.getOriginalSize());
        } else if (compressionType == 3) {
            return decompressZSTD(compressedData, entry.getOriginalSize());
        } else {
            return decompressZlib(compressedData);
        }
    }
    
    /**
     * 获取NPK文件统计信息
     */
    public static NpkStatistics getStatistics(NpkFile npkFile) {
        NpkStatistics stats = new NpkStatistics();
        
        List<NpkEntry> entries = npkFile.getEntries();
        stats.setTotalFiles(entries.size());
        
        long totalOriginalSize = 0;
        long totalCompressedSize = 0;
        int compressedFiles = 0;
        
        for (NpkEntry entry : entries) {
            totalOriginalSize += entry.getOriginalSize();
            totalCompressedSize += entry.getCompressedSize();
            
            if (entry.isCompressed()) {
                compressedFiles++;
            }
        }
        
        stats.setTotalOriginalSize(totalOriginalSize);
        stats.setTotalCompressedSize(totalCompressedSize);
        stats.setCompressedFiles(compressedFiles);
        stats.setCompressionRatio((double) totalCompressedSize / totalOriginalSize);
        
        return stats;
    }

    /**
     * 过滤支持的文件（PNG和WAS）
     */
    public static List<NpkEntry> filterSupportedFiles(List<NpkEntry> entries) {
        List<NpkEntry> supportedFiles = new ArrayList<>();

        for (NpkEntry entry : entries) {
            if (isSupportedFile(entry)) {
                supportedFiles.add(entry);
            }
        }

        return supportedFiles;
    }

    /**
     * 过滤PNG文件 - 为了向后兼容，现在支持PNG和WAS文件
     * @deprecated 使用 filterSupportedFiles 替代
     */
    @Deprecated
    public static List<NpkEntry> filterPngFiles(List<NpkEntry> entries) {
        return filterSupportedFiles(entries);
    }

    /**
     * 判断是否为支持的文件 - 支持PNG和WAS文件
     */
    private static boolean isSupportedFile(NpkEntry entry) {
        String fileName = entry.getFileName();
        if (fileName == null) {
            return true; // 假设无文件名的也是支持的文件
        }

        // 检查文件扩展名
        String lowerName = fileName.toLowerCase();
        if (lowerName.endsWith(".png") || lowerName.endsWith(".was")) {
            return true;
        }

        // 检查文件大小是否合理
        long size = entry.getOriginalSize();
        if (size > 0 && size < 50 * 1024 * 1024) { // 0到50MB之间的文件
            return true; // 假设都是支持的文件
        }

        return false;
    }

    /**
     * 批量提取支持的文件 - 高性能版本（支持PNG和WAS）
     */
    public static int extractPngFiles(NpkFile npkFile, String outputDir, BiConsumer<Integer, String> progressCallback) {
        if (npkFile == null || npkFile.getEntries().isEmpty()) {
            return 0;
        }

        // 过滤支持的文件
        List<NpkEntry> allEntries = npkFile.getEntries();
        List<NpkEntry> supportedEntries = filterSupportedFiles(allEntries);

        if (progressCallback != null) {
            progressCallback.accept(5, "找到 " + supportedEntries.size() + " 个支持的文件 (总共 " + allEntries.size() + " 个文件)");
        }

        if (supportedEntries.isEmpty()) {
            if (progressCallback != null) {
                progressCallback.accept(100, "没有找到支持的文件");
            }
            return 0;
        }

        // 创建输出目录
        File outputDirFile = new File(outputDir);
        if (!outputDirFile.exists()) {
            outputDirFile.mkdirs();
        }

        int extractedCount = 0;
        int errorCount = 0;

        for (int i = 0; i < supportedEntries.size(); i++) {
            NpkEntry entry = supportedEntries.get(i);

            try {
                // 提取文件数据
                byte[] fileData = extractFile(npkFile, entry, null);

                if (fileData != null && fileData.length > 0) {
                    // 保存文件（不再只验证PNG，支持所有文件类型）
                    String fileName = entry.getFileName();
                    if (fileName == null || fileName.isEmpty()) {
                        // 根据数据类型生成文件名
                        String extension = isPngData(fileData) ? "png" : "was";
                        fileName = String.format("extracted_%08X.%s", (int)(entry.getOffset() & 0xFFFFFFFF), extension);
                    }

                    File outputFile = new File(outputDirFile, fileName);
                    File parentDir = outputFile.getParentFile();
                    if (parentDir != null && !parentDir.exists()) {
                        parentDir.mkdirs();
                    }

                    try (java.io.FileOutputStream fos = new java.io.FileOutputStream(outputFile)) {
                        fos.write(fileData);
                    }

                    extractedCount++;
                } else {
                    errorCount++;
                }

                // 更新进度
                if (progressCallback != null && (i + 1) % 50 == 0) {
                    int progress = 10 + (i * 80 / supportedEntries.size());
                    progressCallback.accept(progress, "已处理 " + (i + 1) + "/" + supportedEntries.size() +
                                          " (成功: " + extractedCount + ", 失败: " + errorCount + ")");
                }

            } catch (Exception e) {
                errorCount++;
            }
        }

        if (progressCallback != null) {
            progressCallback.accept(100, "PNG提取完成: 成功 " + extractedCount + " 个，失败 " + errorCount + " 个");
        }

        return extractedCount;
    }



    // 辅助方法：读取小端序32位整数
    private static int readInt32LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[4];
        raf.read(bytes);
        return (bytes[0] & 0xFF) | 
               ((bytes[1] & 0xFF) << 8) | 
               ((bytes[2] & 0xFF) << 16) | 
               ((bytes[3] & 0xFF) << 24);
    }
    
    // 辅助方法：读取小端序64位整数
    private static long readInt64LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[8];
        raf.read(bytes);
        return (bytes[0] & 0xFFL) | 
               ((bytes[1] & 0xFFL) << 8) | 
               ((bytes[2] & 0xFFL) << 16) | 
               ((bytes[3] & 0xFFL) << 24) |
               ((bytes[4] & 0xFFL) << 32) | 
               ((bytes[5] & 0xFFL) << 40) | 
               ((bytes[6] & 0xFFL) << 48) | 
               ((bytes[7] & 0xFFL) << 56);
    }
}



/**
 * NPK统计信息
 */
class NpkStatistics {
    private int totalFiles;
    private long totalOriginalSize;
    private long totalCompressedSize;
    private int compressedFiles;
    private double compressionRatio;

    public int getTotalFiles() {
        return totalFiles;
    }

    public void setTotalFiles(int totalFiles) {
        this.totalFiles = totalFiles;
    }

    public long getTotalOriginalSize() {
        return totalOriginalSize;
    }

    public void setTotalOriginalSize(long totalOriginalSize) {
        this.totalOriginalSize = totalOriginalSize;
    }

    public long getTotalCompressedSize() {
        return totalCompressedSize;
    }

    public void setTotalCompressedSize(long totalCompressedSize) {
        this.totalCompressedSize = totalCompressedSize;
    }

    public int getCompressedFiles() {
        return compressedFiles;
    }

    public void setCompressedFiles(int compressedFiles) {
        this.compressedFiles = compressedFiles;
    }

    public double getCompressionRatio() {
        return compressionRatio;
    }

    public void setCompressionRatio(double compressionRatio) {
        this.compressionRatio = compressionRatio;
    }

    @Override
    public String toString() {
        return String.format(
            "NPK统计信息:\n" +
            "  总文件数: %d\n" +
            "  压缩文件数: %d\n" +
            "  原始总大小: %s\n" +
            "  压缩后大小: %s\n" +
            "  压缩率: %.1f%%",
            totalFiles, compressedFiles,
            formatFileSize(totalOriginalSize),
            formatFileSize(totalCompressedSize),
            compressionRatio * 100
        );
    }

    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }
}
