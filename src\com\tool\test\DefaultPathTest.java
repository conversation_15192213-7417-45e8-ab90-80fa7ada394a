package com.tool.test;

import javax.swing.*;
import java.awt.*;
import java.io.File;

/**
 * 默认路径测试工具 - 验证路径设置
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class DefaultPathTest {
    
    // 默认路径配置（与主程序保持一致）
    private static final String DEFAULT_ITEM_FOLDER = "G:\\JXy2o\\GameClient3.0\\res\\item";
    private static final String DEFAULT_ITEM= "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_XLS_PATH = "G:\\JXy2o\\GameClient3.0\\res\\config\\item.xls";
    private static final String DEFAULT_WDF_SOURCE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_WDF_OUTPUT = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_SOURCE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_OUTPUT = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_FILE = "G:\\JXy2o\\GameClient3.0\\res";
    
    public static void main(String[] args) {
        System.out.println("=== 默认路径测试 ===");
        
        // 测试路径存在性
        testPathExistence();
        
        // 创建GUI测试界面
        SwingUtilities.invokeLater(() -> createTestGUI());
    }
    
    /**
     * 测试路径存在性
     */
    private static void testPathExistence() {
        System.out.println("\n--- 路径存在性检查 ---");
        
        String[] paths = {
            DEFAULT_ITEM_FOLDER,
            DEFAULT_ITEM,
            DEFAULT_XLS_PATH,
            DEFAULT_WDF_SOURCE,
            DEFAULT_WDF_OUTPUT,
            DEFAULT_NPK_SOURCE,
            DEFAULT_NPK_OUTPUT,
            DEFAULT_NPK_FILE
        };
        
        String[] pathNames = {
            "皮肤文件夹",
            "游戏资源目录",
            "XLS配置文件",
            "WDF源文件夹",
            "WDF输出目录",
            "NPK源文件夹",
            "NPK输出目录",
            "NPK文件目录"
        };
        
        for (int i = 0; i < paths.length; i++) {
            File file = new File(paths[i]);
            boolean exists = file.exists();
            String type = file.isDirectory() ? "目录" : (file.isFile() ? "文件" : "未知");
            
            System.out.printf("%-12s: %-40s [%s] %s\n", 
                pathNames[i], 
                paths[i], 
                type,
                exists ? "✅ 存在" : "❌ 不存在");
        }
    }
    
    /**
     * 创建测试GUI界面
     */
    private static void createTestGUI() {
        JFrame frame = new JFrame("默认路径测试界面");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setLayout(new BorderLayout());
        
        // 创建标签页
        JTabbedPane tabbedPane = new JTabbedPane();
        
        // WDF处理标签页
        tabbedPane.addTab("WDF处理", createWdfPanel());
        
        // NPK查看标签页
        tabbedPane.addTab("NPK查看", createNpkViewPanel());
        
        // NPK打包标签页
        tabbedPane.addTab("NPK打包", createNpkPackPanel());
        
        frame.add(tabbedPane, BorderLayout.CENTER);
        
        // 状态栏
        JLabel statusLabel = new JLabel("默认路径测试界面 - 验证路径设置是否正确");
        statusLabel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));
        frame.add(statusLabel, BorderLayout.SOUTH);
        
        frame.setSize(800, 600);
        frame.setLocationRelativeTo(null);
        frame.setVisible(true);
    }
    
    /**
     * 创建WDF处理面板
     */
    private static JPanel createWdfPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 源文件夹
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("源文件夹:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField sourceFolderField = new JTextField(DEFAULT_WDF_SOURCE);
        panel.add(sourceFolderField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseSourceButton = new JButton("浏览");
        panel.add(browseSourceButton, gbc);
        
        // 输出WDF文件
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("输出WDF:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField outputWdfField = new JTextField(DEFAULT_WDF_OUTPUT);
        panel.add(outputWdfField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputButton = new JButton("浏览");
        panel.add(browseOutputButton, gbc);
        
        // 信息显示
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        JTextArea infoArea = new JTextArea("WDF处理默认路径测试\n\n");
        infoArea.append("源文件夹默认路径: " + DEFAULT_WDF_SOURCE + "\n");
        infoArea.append("输出WDF默认路径: " + DEFAULT_WDF_OUTPUT + "\n\n");
        infoArea.append("路径存在性检查:\n");
        infoArea.append("源文件夹: " + (new File(DEFAULT_WDF_SOURCE).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.append("输出目录: " + (new File(DEFAULT_WDF_OUTPUT).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.setEditable(false);
        panel.add(new JScrollPane(infoArea), gbc);
        
        return panel;
    }
    
    /**
     * 创建NPK查看面板
     */
    private static JPanel createNpkViewPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // NPK文件
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("NPK文件:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField npkFileField = new JTextField(DEFAULT_NPK_FILE);
        panel.add(npkFileField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseNpkButton = new JButton("浏览");
        panel.add(browseNpkButton, gbc);
        
        // 输出目录
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("输出目录:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField outputDirField = new JTextField(DEFAULT_NPK_OUTPUT);
        panel.add(outputDirField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputButton = new JButton("浏览");
        panel.add(browseOutputButton, gbc);
        
        // 信息显示
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        JTextArea infoArea = new JTextArea("NPK查看默认路径测试\n\n");
        infoArea.append("NPK文件默认路径: " + DEFAULT_NPK_FILE + "\n");
        infoArea.append("输出目录默认路径: " + DEFAULT_NPK_OUTPUT + "\n\n");
        infoArea.append("路径存在性检查:\n");
        infoArea.append("NPK文件目录: " + (new File(DEFAULT_NPK_FILE).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.append("输出目录: " + (new File(DEFAULT_NPK_OUTPUT).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.setEditable(false);
        panel.add(new JScrollPane(infoArea), gbc);
        
        return panel;
    }
    
    /**
     * 创建NPK打包面板
     */
    private static JPanel createNpkPackPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 源文件夹
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("源文件夹:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField sourceFolderField = new JTextField(DEFAULT_NPK_SOURCE, 30);
        panel.add(sourceFolderField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseSourceBtn = new JButton("浏览");
        panel.add(browseSourceBtn, gbc);
        
        // 输出NPK
        gbc.gridx = 0; gbc.gridy = 1; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("输出NPK:"), gbc);
        
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        JTextField outputNpkField = new JTextField(DEFAULT_NPK_OUTPUT, 30);
        panel.add(outputNpkField, gbc);
        
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        JButton browseOutputBtn = new JButton("浏览");
        panel.add(browseOutputBtn, gbc);
        
        // 信息显示
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 3; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        JTextArea infoArea = new JTextArea("NPK打包默认路径测试\n\n");
        infoArea.append("源文件夹默认路径: " + DEFAULT_NPK_SOURCE + "\n");
        infoArea.append("输出NPK默认路径: " + DEFAULT_NPK_OUTPUT + "\n\n");
        infoArea.append("路径存在性检查:\n");
        infoArea.append("源文件夹: " + (new File(DEFAULT_NPK_SOURCE).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.append("输出目录: " + (new File(DEFAULT_NPK_OUTPUT).exists() ? "✅ 存在" : "❌ 不存在") + "\n");
        infoArea.setEditable(false);
        panel.add(new JScrollPane(infoArea), gbc);
        
        return panel;
    }
}
