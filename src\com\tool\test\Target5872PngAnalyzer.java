package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 针对5872个PNG文件的分析器
 * 目标：找到所有5872个PNG文件的提取方法
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class Target5872PngAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    private static final int TARGET_PNG_COUNT = 5872;
    
    /**
     * 分析如何提取5872个PNG文件
     */
    public void analyzeFor5872Pngs() {
        System.out.println("=== 针对5872个PNG文件的分析 ===");
        System.out.println("目标: 找到提取所有5872个PNG文件的方法");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            System.out.println("文件大小: " + formatFileSize(raf.length()));
            
            // 策略1: 搜索所有PNG文件头并分析分布
            List<PngInfo> allPngs = searchAllPngFiles(raf);
            
            // 策略2: 分析索引覆盖情况
            analyzeIndexCoverage(raf, allPngs);
            
            // 策略3: 寻找缺失PNG文件的规律
            findMissingPngPattern(raf, allPngs);
            
            // 策略4: 尝试重构完整的文件列表
            reconstructCompleteFileList(raf, allPngs);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 搜索所有PNG文件
     */
    private List<PngInfo> searchAllPngFiles(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 搜索所有PNG文件 ---");
        
        List<PngInfo> pngFiles = new ArrayList<>();
        byte[] buffer = new byte[8192];
        long position = 0;
        
        while (position < raf.length()) {
            raf.seek(position);
            int bytesRead = raf.read(buffer);
            
            for (int i = 0; i <= bytesRead - 8; i++) {
                if (isPngHeader(buffer, i)) {
                    long pngOffset = position + i;
                    
                    // 估计PNG文件大小
                    long size = estimatePngSize(raf, pngOffset);
                    
                    pngFiles.add(new PngInfo(pngOffset, size));
                    
                    if (pngFiles.size() % 500 == 0) {
                        System.out.println("找到 " + pngFiles.size() + " 个PNG文件...");
                    }
                    
                    if (pngFiles.size() >= TARGET_PNG_COUNT) {
                        break;
                    }
                }
            }
            
            if (pngFiles.size() >= TARGET_PNG_COUNT) {
                break;
            }
            
            position += bytesRead - 7; // 重叠7字节
        }
        
        System.out.println("总共找到 " + pngFiles.size() + " 个PNG文件");
        
        if (pngFiles.size() >= TARGET_PNG_COUNT) {
            System.out.println("✓ 达到目标数量 " + TARGET_PNG_COUNT + "!");
        } else {
            System.out.println("⚠ 未达到目标数量，还差 " + (TARGET_PNG_COUNT - pngFiles.size()) + " 个");
        }
        
        return pngFiles;
    }
    
    /**
     * 分析索引覆盖情况
     */
    private void analyzeIndexCoverage(RandomAccessFile raf, List<PngInfo> allPngs) throws IOException {
        System.out.println("\n--- 分析索引覆盖情况 ---");
        
        // 获取索引中的偏移
        Set<Long> indexOffsets = getIndexOffsets(raf);
        System.out.println("索引中的偏移数量: " + indexOffsets.size());
        
        // 分析覆盖情况
        int coveredCount = 0;
        int uncoveredCount = 0;
        
        System.out.println("分析PNG文件的索引覆盖情况:");
        
        for (PngInfo png : allPngs) {
            if (indexOffsets.contains(png.offset)) {
                coveredCount++;
            } else {
                uncoveredCount++;
                
                // 显示前几个未覆盖的PNG
                if (uncoveredCount <= 10) {
                    System.out.println("  未覆盖PNG #" + uncoveredCount + ": 偏移=0x" + Long.toHexString(png.offset) + ", 大小=" + png.size);
                }
            }
        }
        
        System.out.println("索引覆盖的PNG: " + coveredCount + " 个");
        System.out.println("索引未覆盖的PNG: " + uncoveredCount + " 个");
        System.out.println("覆盖率: " + (coveredCount * 100.0 / allPngs.size()) + "%");
        
        if (uncoveredCount > 0) {
            System.out.println("需要找到访问未覆盖PNG文件的方法");
        }
    }
    
    /**
     * 获取索引中的所有偏移
     */
    private Set<Long> getIndexOffsets(RandomAccessFile raf) throws IOException {
        Set<Long> offsets = new HashSet<>();
        long indexOffset = 0x7354149L;
        int stepSize = 56;
        
        for (int i = 0; i < 3083; i++) {
            long entryPos = indexOffset + (i * stepSize);
            
            try {
                raf.seek(entryPos + 4); // 跳到第二个字段（偏移字段）
                long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                
                if (offset < raf.length()) {
                    offsets.add(offset);
                }
            } catch (Exception e) {
                // 忽略错误
            }
        }
        
        return offsets;
    }
    
    /**
     * 寻找缺失PNG文件的规律
     */
    private void findMissingPngPattern(RandomAccessFile raf, List<PngInfo> allPngs) throws IOException {
        System.out.println("\n--- 寻找缺失PNG文件的规律 ---");
        
        // 按偏移排序
        allPngs.sort((a, b) -> Long.compare(a.offset, b.offset));
        
        // 分析文件分布
        System.out.println("PNG文件分布分析:");
        System.out.println("第一个PNG: 0x" + Long.toHexString(allPngs.get(0).offset));
        System.out.println("最后一个PNG: 0x" + Long.toHexString(allPngs.get(allPngs.size() - 1).offset));
        
        // 分析间隔
        List<Long> gaps = new ArrayList<>();
        for (int i = 1; i < allPngs.size(); i++) {
            long gap = allPngs.get(i).offset - (allPngs.get(i-1).offset + allPngs.get(i-1).size);
            if (gap > 0) {
                gaps.add(gap);
            }
        }
        
        // 统计间隔分布
        System.out.println("文件间隔统计 (前20个):");
        for (int i = 0; i < Math.min(20, gaps.size()); i++) {
            System.out.println("  间隔 " + (i+1) + ": " + gaps.get(i) + " 字节");
        }
        
        // 检查是否有规律的间隔
        if (gaps.size() > 10) {
            long avgGap = gaps.stream().mapToLong(Long::longValue).sum() / gaps.size();
            System.out.println("平均间隔: " + avgGap + " 字节");
        }
    }
    
    /**
     * 重构完整的文件列表
     */
    private void reconstructCompleteFileList(RandomAccessFile raf, List<PngInfo> allPngs) throws IOException {
        System.out.println("\n--- 重构完整的文件列表 ---");
        
        // 策略：为每个PNG文件生成文件名和条目信息
        System.out.println("为所有PNG文件生成条目信息:");
        
        List<CompleteEntry> completeEntries = new ArrayList<>();
        
        for (int i = 0; i < allPngs.size(); i++) {
            PngInfo png = allPngs.get(i);
            
            // 生成文件名（使用偏移的十六进制）
            String fileName = String.format("0x%08x.png", png.offset);
            
            // 创建完整条目
            CompleteEntry entry = new CompleteEntry(fileName, png.offset, png.size, png.size);
            completeEntries.add(entry);
            
            // 显示前几个条目
            if (i < 10) {
                System.out.println("  条目 " + (i+1) + ": " + fileName + " (偏移: 0x" + Long.toHexString(png.offset) + ", 大小: " + png.size + ")");
            }
        }
        
        System.out.println("重构完成，总共 " + completeEntries.size() + " 个条目");
        
        // 测试提取前几个文件
        testExtractReconstructedEntries(raf, completeEntries);
    }
    
    /**
     * 测试提取重构的条目
     */
    private void testExtractReconstructedEntries(RandomAccessFile raf, List<CompleteEntry> entries) throws IOException {
        System.out.println("\n测试提取重构的条目 (前10个):");
        
        int successCount = 0;
        
        for (int i = 0; i < Math.min(10, entries.size()); i++) {
            CompleteEntry entry = entries.get(i);
            
            try {
                raf.seek(entry.offset);
                
                // 读取文件头验证
                byte[] header = new byte[8];
                raf.read(header);
                
                if (isPngHeader(header, 0)) {
                    System.out.println("  ✓ " + entry.fileName + " - 有效PNG文件");
                    successCount++;
                } else {
                    System.out.println("  ✗ " + entry.fileName + " - 无效PNG文件头");
                }
                
            } catch (Exception e) {
                System.out.println("  ✗ " + entry.fileName + " - 读取失败: " + e.getMessage());
            }
        }
        
        System.out.println("测试结果: " + successCount + "/10 成功");
        
        if (successCount >= 8) {
            System.out.println("✓ 重构的条目质量很好，可以用于批量提取");
        }
    }
    
    /**
     * 估计PNG文件大小
     */
    private long estimatePngSize(RandomAccessFile raf, long offset) throws IOException {
        // 读取PNG文件的IHDR块来获取更准确的信息
        raf.seek(offset + 8); // 跳过PNG签名
        
        try {
            // 读取第一个块（通常是IHDR）
            int chunkLength = readInt32BE(raf);
            byte[] chunkType = new byte[4];
            raf.read(chunkType);
            
            if ("IHDR".equals(new String(chunkType))) {
                // 跳过IHDR数据和CRC
                raf.skipBytes(chunkLength + 4);
                
                // 继续读取后续块来估计总大小
                long currentPos = raf.getFilePointer();
                long estimatedSize = currentPos - offset;
                
                // 简单估计：基于文件头信息
                if (chunkLength == 13) { // 标准IHDR长度
                    estimatedSize += 1000; // 基础估计
                }
                
                return Math.min(estimatedSize + 10000, 1024 * 1024); // 最大1MB
            }
        } catch (Exception e) {
            // 估计失败，使用默认值
        }
        
        return 50000; // 默认50KB
    }
    
    /**
     * 检查PNG文件头
     */
    private boolean isPngHeader(byte[] data, int offset) {
        if (data.length < offset + 8) return false;
        return data[offset] == (byte)0x89 && data[offset+1] == (byte)0x50 && 
               data[offset+2] == (byte)0x4E && data[offset+3] == (byte)0x47 && 
               data[offset+4] == (byte)0x0D && data[offset+5] == (byte)0x0A && 
               data[offset+6] == (byte)0x1A && data[offset+7] == (byte)0x0A;
    }
    
    /**
     * 读取32位大端序整数
     */
    private int readInt32BE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return (b1 << 24) | (b2 << 16) | (b3 << 8) | b4;
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * PNG信息类
     */
    private static class PngInfo {
        long offset;
        long size;
        
        PngInfo(long offset, long size) {
            this.offset = offset;
            this.size = size;
        }
    }
    
    /**
     * 完整条目类
     */
    private static class CompleteEntry {
        String fileName;
        long offset;
        long compressedSize;
        long originalSize;
        
        CompleteEntry(String fileName, long offset, long compressedSize, long originalSize) {
            this.fileName = fileName;
            this.offset = offset;
            this.compressedSize = compressedSize;
            this.originalSize = originalSize;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        Target5872PngAnalyzer analyzer = new Target5872PngAnalyzer();
        analyzer.analyzeFor5872Pngs();
    }
}
