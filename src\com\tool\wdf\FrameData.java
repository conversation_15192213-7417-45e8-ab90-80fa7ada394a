
package com.tool.wdf;

import java.awt.image.BufferedImage;

public class FrameData {
    private long offset;
    private long centerX;
    private long centerY;
    private long width;
    private long height;
    private long[] rowOffset;
    private BufferedImage bufImage;

    public FrameData() {
    }

    public long getOffset() {
        return this.offset;
    }

    public void setOffset(long offset) {
        this.offset = offset;
    }

    public long getCenterX() {
        return this.centerX;
    }

    public void setCenterX(long centerX) {
        this.centerX = centerX;
    }

    public long getCenterY() {
        return this.centerY;
    }

    public void setCenterY(long centerY) {
        this.centerY = centerY;
    }

    public long getWidth() {
        return this.width;
    }

    public void setWidth(long width) {
        this.width = width;
    }

    public long getHeight() {
        return this.height;
    }

    public void setHeight(long height) {
        this.height = height;
    }

    public long[] getRowOffset() {
        return this.rowOffset;
    }

    public void setRowOffset(long[] rowOffset) {
        this.rowOffset = rowOffset;
    }

    public BufferedImage getBufImage() {
        return this.bufImage;
    }

    public void setBufImage(BufferedImage bufImage) {
        this.bufImage = bufImage;
    }
}
