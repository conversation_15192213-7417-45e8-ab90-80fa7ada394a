package xsl;

import jxl.Cell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.WorkbookSettings;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import java.io.File;
import java.io.IOException;

public class ExcelUtil {

    // 读取Excel文件
    public static String[][] readExcel(String path) {
        try {
            Workbook workbook = Workbook.getWorkbook(new File(path));
            Sheet sheet = workbook.getSheet(0);
            int col = sheet.getColumns();
            int row = sheet.getRows();
            String[][] result = new String[row][col];
            Cell cell;
            for (int i = 0; i < row; i++) {
                for (int j = 0; j < col; j++) {
                    cell = sheet.getCell(j, i);
                    result[i][j] = cell.getContents();
                }
            }
            workbook.close();
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // 保存Excel文件
    public static void saveExcel(String path, String[][] data) {
        try {
            WorkbookSettings wbSettings = new WorkbookSettings();
            wbSettings.setEncoding("ISO-8859-1");

            WritableWorkbook workbook = Workbook.createWorkbook(new File(path), wbSettings);
            WritableSheet sheet = workbook.createSheet("Sheet1", 0);

            for (int i = 0; i < data.length; i++) {
                for (int j = 0; j < data[i].length; j++) {
                    Label label = new Label(j, i, data[i][j]);
                    sheet.addCell(label);
                }
            }

            workbook.write();
            workbook.close();
        } catch (IOException | WriteException e) {
            e.printStackTrace();
        }
    }
}
