package com.tool.skin;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Random;

/**
 * 文件管理功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class FileManagementTest {
    
    private static final String TEST_FOLDER = "test_files";
    private Random random = new Random();
    
    /**
     * 生成随机的8位十六进制ID
     */
    private String generateRandomHexId() {
        long randomValue = random.nextLong() & 0xFFFFFFFFL;
        if (randomValue < 0x50000000L) {
            randomValue += 0x50000000L;
        }
        return String.format("%08X", randomValue);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        return String.format("%.1f MB", size / (1024.0 * 1024.0));
    }
    
    /**
     * 创建测试文件
     */
    public void createTestFiles() {
        System.out.println("=== 创建测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (!Files.exists(testDir)) {
                Files.createDirectory(testDir);
                System.out.println("创建测试目录: " + TEST_FOLDER);
            }
            
            String[] testFileNames = {
                "weapon_sword.png",
                "armor_001.png",
                "item_potion.png",
                "6120.png",
                "9134.png",
                "skill_fireball.png",
                "npc_merchant.png",
                "background_forest.png",
                "temp_file_001.png",
                "old_texture.png"
            };
            
            for (String fileName : testFileNames) {
                Path filePath = testDir.resolve(fileName);
                if (!Files.exists(filePath)) {
                    // 创建一个小的测试文件
                    String content = "Test PNG file: " + fileName + "\nCreated for testing purposes.";
                    Files.write(filePath, content.getBytes());
                    System.out.println("创建测试文件: " + fileName);
                }
            }
            
            System.out.println("测试文件创建完成");
            
        } catch (IOException e) {
            System.out.println("创建测试文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试文件扫描功能
     */
    public void testFileScan() {
        System.out.println("\n=== 测试文件扫描功能 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (!Files.exists(testDir)) {
                System.out.println("测试目录不存在，请先创建测试文件");
                return;
            }
            
            System.out.println("扫描目录: " + testDir.toAbsolutePath());
            System.out.println("文件列表:");
            System.out.println("序号 | 文件名                    | 文件大小 | 修改时间");
            System.out.println("-----|---------------------------|----------|------------------");
            
            int index = 1;
            Files.list(testDir)
                .filter(path -> path.toString().toLowerCase().endsWith(".png"))
                .forEach(path -> {
                    try {
                        File file = path.toFile();
                        String fileName = file.getName();
                        String fileSize = formatFileSize(file.length());
                        String modifyTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                            .format(new java.util.Date(file.lastModified()));
                        
                        System.out.printf("%4d | %-25s | %-8s | %s%n", 
                            index, fileName, fileSize, modifyTime);
                    } catch (Exception e) {
                        System.out.println("处理文件时出错: " + path.getFileName());
                    }
                });
                
        } catch (IOException e) {
            System.out.println("扫描文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试随机重命名功能
     */
    public void testRandomRename() {
        System.out.println("\n=== 测试随机重命名功能 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (!Files.exists(testDir)) {
                System.out.println("测试目录不存在，请先创建测试文件");
                return;
            }
            
            // 选择前3个文件进行重命名测试
            Files.list(testDir)
                .filter(path -> path.toString().toLowerCase().endsWith(".png"))
                .limit(3)
                .forEach(path -> {
                    try {
                        String oldFileName = path.getFileName().toString();
                        String randomHexId = generateRandomHexId();
                        String newFileName = "2025-" + randomHexId + ".png";
                        
                        Path newPath = testDir.resolve(newFileName);
                        
                        // 检查新文件名是否已存在
                        while (Files.exists(newPath)) {
                            randomHexId = generateRandomHexId();
                            newFileName = "2025-" + randomHexId + ".png";
                            newPath = testDir.resolve(newFileName);
                        }
                        
                        Files.move(path, newPath);
                        System.out.println("重命名: " + oldFileName + " -> " + newFileName);
                        
                    } catch (IOException e) {
                        System.out.println("重命名失败: " + path.getFileName() + " - " + e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            System.out.println("测试重命名时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试文件删除功能
     */
    public void testFileDelete() {
        System.out.println("\n=== 测试文件删除功能 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (!Files.exists(testDir)) {
                System.out.println("测试目录不存在，请先创建测试文件");
                return;
            }
            
            // 删除以"temp_"或"old_"开头的文件
            Files.list(testDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    return fileName.startsWith("temp_") || fileName.startsWith("old_");
                })
                .forEach(path -> {
                    try {
                        String fileName = path.getFileName().toString();
                        Files.delete(path);
                        System.out.println("删除文件: " + fileName);
                    } catch (IOException e) {
                        System.out.println("删除失败: " + path.getFileName() + " - " + e.getMessage());
                    }
                });
                
        } catch (IOException e) {
            System.out.println("测试删除时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                Files.list(testDir).forEach(path -> {
                    try {
                        Files.delete(path);
                        System.out.println("删除: " + path.getFileName());
                    } catch (IOException e) {
                        System.out.println("删除失败: " + path.getFileName());
                    }
                });
                
                Files.delete(testDir);
                System.out.println("删除测试目录: " + TEST_FOLDER);
            }
        } catch (IOException e) {
            System.out.println("清理测试文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        createTestFiles();
        testFileScan();
        testRandomRename();
        testFileScan(); // 再次扫描查看重命名结果
        testFileDelete();
        testFileScan(); // 最后扫描查看删除结果
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        FileManagementTest test = new FileManagementTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllTests();
        }
    }
}
