#!/usr/bin/env python3
"""
SkinidSync Logo Generator
生成应用程序图标的Python脚本
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_logo():
    # 创建128x128的图像
    size = 128
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景圆形 - 蓝色渐变
    center = size // 2
    radius = 60
    
    # 绘制主背景圆
    draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                fill=(25, 118, 210, 255), outline=(13, 71, 161, 255), width=2)
    
    # 绘制内部装饰圆环
    inner_radius = 50
    draw.ellipse([center-inner_radius, center-inner_radius, center+inner_radius, center+inner_radius], 
                fill=None, outline=(66, 165, 245, 80), width=1)
    
    # 绘制左侧文件夹 (橙色)
    folder1_x, folder1_y = 29, 49
    draw.rectangle([folder1_x, folder1_y+5, folder1_x+25, folder1_y+25], 
                  fill=(255, 183, 77, 255), outline=(255, 152, 0, 255), width=1)
    draw.rectangle([folder1_x, folder1_y, folder1_x+8, folder1_y+5], 
                  fill=(255, 183, 77, 255))
    
    # 文件夹内的文件图标
    draw.rectangle([folder1_x+3, folder1_y+8, folder1_x+7, folder1_y+14], fill=(255, 255, 255, 200))
    draw.rectangle([folder1_x+8, folder1_y+10, folder1_x+12, folder1_y+14], fill=(255, 255, 255, 200))
    draw.rectangle([folder1_x+13, folder1_y+12, folder1_x+17, folder1_y+14], fill=(255, 255, 255, 200))
    
    # 绘制右侧文件夹 (绿色)
    folder2_x, folder2_y = 74, 49
    draw.rectangle([folder2_x, folder2_y+5, folder2_x+25, folder2_y+25], 
                  fill=(129, 199, 132, 255), outline=(76, 175, 80, 255), width=1)
    draw.rectangle([folder2_x, folder2_y, folder2_x+8, folder2_y+5], 
                  fill=(129, 199, 132, 255))
    
    # 文件夹内的文件图标
    draw.rectangle([folder2_x+3, folder2_y+8, folder2_x+7, folder2_y+14], fill=(255, 255, 255, 200))
    draw.rectangle([folder2_x+8, folder2_y+10, folder2_x+12, folder2_y+14], fill=(255, 255, 255, 200))
    draw.rectangle([folder2_x+13, folder2_y+12, folder2_x+17, folder2_y+14], fill=(255, 255, 255, 200))
    
    # 绘制同步箭头
    arrow_y = 59
    # 向右箭头
    draw.line([52, arrow_y, 67, arrow_y], fill=(255, 255, 255, 255), width=2)
    draw.line([64, arrow_y-3, 67, arrow_y], fill=(255, 255, 255, 255), width=2)
    draw.line([64, arrow_y+3, 67, arrow_y], fill=(255, 255, 255, 255), width=2)
    
    # 向左箭头
    arrow_y2 = 69
    draw.line([76, arrow_y2, 61, arrow_y2], fill=(255, 255, 255, 255), width=2)
    draw.line([64, arrow_y2-3, 61, arrow_y2], fill=(255, 255, 255, 255), width=2)
    draw.line([64, arrow_y2+3, 61, arrow_y2], fill=(255, 255, 255, 255), width=2)
    
    # 绘制底部齿轮图标
    gear_center_x, gear_center_y = 64, 87
    gear_radius = 6
    
    # 齿轮外圆
    draw.ellipse([gear_center_x-gear_radius, gear_center_y-gear_radius, 
                 gear_center_x+gear_radius, gear_center_y+gear_radius], 
                fill=None, outline=(255, 255, 255, 255), width=2)
    
    # 齿轮内圆
    inner_gear_radius = 3
    draw.ellipse([gear_center_x-inner_gear_radius, gear_center_y-inner_gear_radius, 
                 gear_center_x+inner_gear_radius, gear_center_y+inner_gear_radius], 
                fill=(255, 255, 255, 255))
    
    # 齿轮齿
    tooth_size = 2
    # 上下左右齿
    draw.rectangle([gear_center_x-1, gear_center_y-gear_radius-3, gear_center_x+1, gear_center_y-gear_radius], 
                  fill=(255, 255, 255, 255))
    draw.rectangle([gear_center_x-1, gear_center_y+gear_radius, gear_center_x+1, gear_center_y+gear_radius+3], 
                  fill=(255, 255, 255, 255))
    draw.rectangle([gear_center_x-gear_radius-3, gear_center_y-1, gear_center_x-gear_radius, gear_center_y+1], 
                  fill=(255, 255, 255, 255))
    draw.rectangle([gear_center_x+gear_radius, gear_center_y-1, gear_center_x+gear_radius+3, gear_center_y+1], 
                  fill=(255, 255, 255, 255))
    
    # 装饰性光效
    draw.ellipse([42, 32, 48, 38], fill=(255, 255, 255, 150))
    draw.ellipse([83, 38, 87, 42], fill=(255, 255, 255, 100))
    draw.ellipse([87, 82, 92, 87], fill=(255, 255, 255, 125))
    
    # 外边框
    draw.ellipse([2, 2, size-2, size-2], fill=None, outline=(13, 71, 161, 200), width=1)
    
    return img

def main():
    # 创建resource目录
    os.makedirs('resource', exist_ok=True)
    
    # 生成图标
    logo = create_logo()
    
    # 保存为PNG格式
    logo.save('resource/app-icon.png', 'PNG')
    print("✅ 已生成 resource/app-icon.png")
    
    # 生成不同尺寸的图标
    sizes = [16, 24, 32, 48, 64, 128, 256]
    for size in sizes:
        resized = logo.resize((size, size), Image.Resampling.LANCZOS)
        resized.save(f'resource/app-icon-{size}.png', 'PNG')
        print(f"✅ 已生成 resource/app-icon-{size}.png")
    
    print("\n🎨 SkinidSync Logo 生成完成！")
    print("📁 所有图标文件已保存到 resource/ 目录")

if __name__ == "__main__":
    main()
