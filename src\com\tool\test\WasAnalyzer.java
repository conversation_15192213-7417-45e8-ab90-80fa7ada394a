package com.tool.test;

import java.io.*;

/**
 * WAS文件结构分析工具
 */
public class WasAnalyzer {
    
    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("用法: java WasAnalyzer <was文件路径>");
            return;
        }
        
        String wasFilePath = args[0];
        File wasFile = new File(wasFilePath);
        
        if (!wasFile.exists()) {
            System.err.println("WAS文件不存在: " + wasFilePath);
            return;
        }
        
        analyzeWasFile(wasFile);
    }
    
    public static void analyzeWasFile(File wasFile) {
        System.out.println("=== WAS文件分析 ===");
        System.out.println("文件: " + wasFile.getName());
        System.out.println("大小: " + wasFile.length() + " 字节");
        System.out.println();
        
        try (RandomAccessFile raf = new RandomAccessFile(wasFile, "r")) {
            // 读取文件头
            System.out.println("--- 文件头分析 ---");
            
            // 前32字节的十六进制内容
            byte[] header = new byte[Math.min(32, (int)wasFile.length())];
            raf.read(header);
            
            System.out.println("前32字节十六进制:");
            printHexDump(header, 0);
            
            System.out.println("\n前32字节ASCII:");
            printAscii(header);
            
            // 重置到开头
            raf.seek(0);
            
            // 尝试解析头部信息
            System.out.println("\n--- 头部字段解析 ---");
            
            // 读取可能的标识
            byte[] signature = new byte[2];
            raf.read(signature);
            String sigStr = new String(signature);
            System.out.println("标识: " + sigStr + " (0x" + String.format("%02X%02X", signature[0], signature[1]) + ")");
            
            if ("SP".equals(sigStr) || "SH".equals(sigStr)) {
                System.out.println("检测到有效的WAS文件标识");
                
                // 读取后续字段
                int field1 = readInt16LE(raf);
                int field2 = readInt16LE(raf);
                int field3 = readInt16LE(raf);
                int field4 = readInt16LE(raf);
                int field5 = readInt16LE(raf);
                int field6 = readInt16LE(raf);
                int field7 = readInt16LE(raf);
                
                System.out.println("字段1 (可能是头部大小): " + field1);
                System.out.println("字段2 (可能是方向数): " + field2);
                System.out.println("字段3 (可能是帧数): " + field3);
                System.out.println("字段4 (可能是宽度): " + field4);
                System.out.println("字段5 (可能是高度): " + field5);
                System.out.println("字段6 (可能是中心X): " + field6);
                System.out.println("字段7 (可能是中心Y): " + field7);
                
                // 如果有调色板
                if (field1 > 16) {
                    System.out.println("\n--- 可能的调色板数据 ---");
                    int paletteSize = (field1 - 16) / 4;
                    System.out.println("调色板大小: " + paletteSize + " 色");
                    
                    // 显示前几个调色板条目
                    for (int i = 0; i < Math.min(8, paletteSize); i++) {
                        int color = readInt32LE(raf);
                        System.out.printf("调色板[%d]: 0x%08X\n", i, color);
                    }
                }
                
                // 如果有帧数据
                if (field3 > 0) {
                    System.out.println("\n--- 帧数据信息 ---");
                    raf.seek(field1); // 跳到帧数据开始位置
                    
                    for (int i = 0; i < Math.min(field3, 5); i++) {
                        long frameOffset = readInt32LE(raf) & 0xFFFFFFFFL;
                        long centerX = readInt32LE(raf) & 0xFFFFFFFFL;
                        long centerY = readInt32LE(raf) & 0xFFFFFFFFL;
                        long width = readInt32LE(raf) & 0xFFFFFFFFL;
                        long height = readInt32LE(raf) & 0xFFFFFFFFL;
                        
                        System.out.printf("帧[%d]: 偏移=%d, 中心=(%d,%d), 尺寸=%dx%d\n", 
                            i, frameOffset, centerX, centerY, width, height);
                    }
                }
                
            } else {
                System.out.println("未识别的文件格式");
                
                // 尝试查找PNG头部
                raf.seek(0);
                byte[] buffer = new byte[(int)Math.min(1024, wasFile.length())];
                raf.read(buffer);
                
                for (int i = 0; i < buffer.length - 4; i++) {
                    if ((buffer[i] & 0xFF) == 0x89 && 
                        (buffer[i+1] & 0xFF) == 0x50 && 
                        (buffer[i+2] & 0xFF) == 0x4E && 
                        (buffer[i+3] & 0xFF) == 0x47) {
                        System.out.println("在偏移 " + i + " 处发现PNG头部");
                        break;
                    }
                }
            }
            
        } catch (IOException e) {
            System.err.println("分析文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void printHexDump(byte[] data, int offset) {
        for (int i = 0; i < data.length; i += 16) {
            System.out.printf("%04X: ", offset + i);
            
            // 十六进制部分
            for (int j = 0; j < 16; j++) {
                if (i + j < data.length) {
                    System.out.printf("%02X ", data[i + j] & 0xFF);
                } else {
                    System.out.print("   ");
                }
            }
            
            System.out.print(" ");
            
            // ASCII部分
            for (int j = 0; j < 16 && i + j < data.length; j++) {
                byte b = data[i + j];
                if (b >= 32 && b <= 126) {
                    System.out.print((char) b);
                } else {
                    System.out.print(".");
                }
            }
            
            System.out.println();
        }
    }
    
    private static void printAscii(byte[] data) {
        StringBuilder sb = new StringBuilder();
        for (byte b : data) {
            if (b >= 32 && b <= 126) {
                sb.append((char) b);
            } else {
                sb.append(".");
            }
        }
        System.out.println(sb.toString());
    }
    
    private static int readInt16LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[2];
        raf.read(bytes);
        return (bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8);
    }
    
    private static int readInt32LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[4];
        raf.read(bytes);
        return (bytes[0] & 0xFF) | 
               ((bytes[1] & 0xFF) << 8) | 
               ((bytes[2] & 0xFF) << 16) | 
               ((bytes[3] & 0xFF) << 24);
    }
}
