<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="NpkPackEdit" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="73173f94-b937-49ed-8f45-08f66eeae857" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="305ytcqcFVQYUg2kR9qpdMoOkub" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;G:/SkinidSync&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.44827586&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;应用程序.QuickSkinConverter.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.SkinIdConverterTest.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.SkinIdSyncTool.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\SkinidSync\src" />
      <recent name="G:\SkinidSync\src\com\tool\wdf" />
      <recent name="G:\SkinidSync\src\com\tool\skin" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\SkinidSync\src\resource" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.tool.skin" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="SkinIdSyncTool" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.tool.skin.SkinIdSyncTool" />
      <module name="SkinidSync" />
      <option name="VM_PARAMETERS" value="-Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.tool.skin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.SkinIdSyncTool" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.117" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="73173f94-b937-49ed-8f45-08f66eeae857" name="更改" comment="" />
      <created>1752931943820</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752931943820</updated>
      <workItem from="1752931944739" duration="4643000" />
      <workItem from="1752967105918" duration="26932000" />
      <workItem from="1753022203759" duration="154000" />
      <workItem from="1753022660026" duration="100000" />
      <workItem from="1753057207853" duration="1572000" />
      <workItem from="1753083218842" duration="1437000" />
      <workItem from="1753086512827" duration="1274000" />
      <workItem from="1753139790660" duration="751000" />
      <workItem from="1753140560542" duration="265000" />
      <workItem from="1753141099978" duration="9000" />
      <workItem from="1753528527341" duration="3172000" />
      <workItem from="1753541636380" duration="150000" />
      <workItem from="1753569646603" duration="11000" />
      <workItem from="1753839570265" duration="181000" />
      <workItem from="1753839760554" duration="797000" />
      <workItem from="1753846432242" duration="21000" />
      <workItem from="1753847219837" duration="11631000" />
      <workItem from="1753862784875" duration="60000" />
      <workItem from="1753917469656" duration="1000" />
      <workItem from="1753932357428" duration="29000" />
      <workItem from="1754117045724" duration="811000" />
      <workItem from="1754117933044" duration="481000" />
      <workItem from="1754118447496" duration="49000" />
      <workItem from="1754118517558" duration="356000" />
      <workItem from="1754202194987" duration="1484000" />
      <workItem from="1754290141819" duration="686000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>