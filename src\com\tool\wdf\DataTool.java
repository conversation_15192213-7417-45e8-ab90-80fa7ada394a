

package com.tool.wdf;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DataTool {
    public DataTool() {
    }

    public static int byte2int32(byte[] res, int index) {
        int targets = res[index] & 255 | res[index + 1] << 8 & '\uff00' | res[index + 2] << 24 >>> 8 | res[index + 3] << 24;
        return targets;
    }

    public static long byte2uint32(byte[] b, int index) {
        long s0 = (long)(b[index] & 255);
        long s1 = (long)(b[index + 1] & 255);
        long s2 = (long)(b[index + 2] & 255);
        long s3 = (long)(b[index + 3] & 255);
        long s4 = 0L;
        long s5 = 0L;
        long s6 = 0L;
        long s7 = 0L;
        s1 <<= 8;
        s2 <<= 16;
        s3 <<= 24;
        s4 <<= 32;
        s5 <<= 40;
        s6 <<= 48;
        s7 <<= 56;
        return s0 | s1 | s2 | s3 | s4 | s5 | s6 | s7;
    }

    public static int byte2int16(byte[] res, int index) {
        return (short)byte2uint16(res, index);
    }

    public static int byte2uint16(byte[] res, int index) {
        int targets = res[index] & 255 | res[index + 1] << 8 & '\uff00';
        return targets;
    }

    public static String ten2six(String s) {
        String text = "";
        long i = Math.abs(Long.parseLong(s));
        if (i == 0L) {
            return "0";
        } else {
            while(i > 0L) {
                long a = i % 16L;
                i /= 16L;
                text = sixformat(a) + text;
            }

            return "0x" + text;
        }
    }

    private static String sixformat(long i) {
        switch ((int)i) {
            case 10:
                return "A";
            case 11:
                return "B";
            case 12:
                return "C";
            case 13:
                return "D";
            case 14:
                return "E";
            case 15:
                return "F";
            default:
                return String.valueOf(i);
        }
    }

    public static String formatFileSize(long size) {
        if (size < 1024L) {
            return size + " B";
        } else {
            return size < 1048576L ? String.format("%.2f KB", (double)size / (double)1024.0F) : String.format("%.2f MB", (double)size / (double)1048576.0F);
        }
    }

    public static String formatDate(long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date(timestamp));
    }
}
