package com.tool.skin;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试跳过逻辑功能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SkipLogicTest {
    
    /**
     * 模拟皮肤修改记录
     */
    public static class MockSkinModifyRecord {
        private String oldFileName;
        private String newFileName;
        private String oldSkinId;
        private String newSkinId;
        private boolean alreadyConverted;
        private int xlsRowIndex;

        public MockSkinModifyRecord(String oldFileName, String newFileName, String oldSkinId, String newSkinId) {
            this.oldFileName = oldFileName;
            this.newFileName = newFileName;
            this.oldSkinId = oldSkinId;
            this.newSkinId = newSkinId;
            this.alreadyConverted = false;
            this.xlsRowIndex = -1;
        }

        // Getters and Setters
        public String getOldFileName() { return oldFileName; }
        public String getNewFileName() { return newFileName; }
        public String getOldSkinId() { return oldSkinId; }
        public String getNewSkinId() { return newSkinId; }
        public boolean isAlreadyConverted() { return alreadyConverted; }
        public void setAlreadyConverted(boolean alreadyConverted) { this.alreadyConverted = alreadyConverted; }
        public int getXlsRowIndex() { return xlsRowIndex; }
        public void setXlsRowIndex(int xlsRowIndex) { this.xlsRowIndex = xlsRowIndex; }
    }
    
    /**
     * 测试跳过逻辑
     */
    public void testSkipLogic() {
        System.out.println("=== 跳过逻辑测试 ===");
        
        List<MockSkinModifyRecord> records = new ArrayList<>();
        
        // 创建测试数据
        MockSkinModifyRecord record1 = new MockSkinModifyRecord("weapon_sword.png", "2025-A1B2C3D4.png", "weapon_sword", "0xA1B2C3D4");
        record1.setXlsRowIndex(5); // 在XLS中找到
        records.add(record1);
        
        MockSkinModifyRecord record2 = new MockSkinModifyRecord("armor_001.png", "2025-F5E6D7C8.png", "armor_001", "0xF5E6D7C8");
        record2.setXlsRowIndex(-1); // 在XLS中未找到
        records.add(record2);
        
        MockSkinModifyRecord record3 = new MockSkinModifyRecord("9134.png", "2025-B9A8C7D6.png", "9134", "0xB9A8C7D6");
        record3.setXlsRowIndex(10); // 在XLS中找到
        records.add(record3);
        
        MockSkinModifyRecord record4 = new MockSkinModifyRecord("2025-12345678.png", "2025-12345678.png", "old_file", "0x12345678");
        record4.setAlreadyConverted(true); // 已转换
        record4.setXlsRowIndex(15);
        records.add(record4);
        
        MockSkinModifyRecord record5 = new MockSkinModifyRecord("unknown_item.png", "2025-E4F3A2B1.png", "unknown_item", "0xE4F3A2B1");
        record5.setXlsRowIndex(-1); // 在XLS中未找到
        records.add(record5);
        
        // 模拟处理逻辑
        int totalRecords = records.size();
        int successCount = 0;
        int skipCount = 0;
        int convertedCount = 0;
        
        System.out.println("处理文件列表：");
        System.out.println("序号 | 文件名                    | 状态");
        System.out.println("-----|---------------------------|------------------");
        
        for (int i = 0; i < records.size(); i++) {
            MockSkinModifyRecord record = records.get(i);
            String status;
            
            if (record.isAlreadyConverted()) {
                status = "跳过(已转换)";
                convertedCount++;
            } else if (record.getXlsRowIndex() < 0) {
                status = "跳过(XLS中未找到)";
                skipCount++;
            } else {
                status = "处理成功";
                successCount++;
            }
            
            System.out.printf("%4d | %-25s | %s%n", 
                i + 1, record.getOldFileName(), status);
        }
        
        System.out.println("\n=== 处理结果统计 ===");
        System.out.println("总文件数: " + totalRecords);
        System.out.println("成功处理: " + successCount);
        System.out.println("跳过(XLS中未找到): " + skipCount);
        System.out.println("跳过(已转换): " + convertedCount);
        System.out.println("处理率: " + (successCount * 100.0 / totalRecords) + "%");
        System.out.println("跳过率: " + ((skipCount + convertedCount) * 100.0 / totalRecords) + "%");
    }
    
    /**
     * 测试状态显示
     */
    public void testStatusDisplay() {
        System.out.println("\n=== 状态显示测试 ===");
        
        List<MockSkinModifyRecord> records = new ArrayList<>();
        
        // 创建不同状态的记录
        MockSkinModifyRecord record1 = new MockSkinModifyRecord("file1.png", "2025-A1B2C3D4.png", "file1", "0xA1B2C3D4");
        record1.setXlsRowIndex(5);
        
        MockSkinModifyRecord record2 = new MockSkinModifyRecord("file2.png", "2025-F5E6D7C8.png", "file2", "0xF5E6D7C8");
        record2.setXlsRowIndex(-1);
        
        MockSkinModifyRecord record3 = new MockSkinModifyRecord("2025-12345678.png", "2025-12345678.png", "old_file", "0x12345678");
        record3.setAlreadyConverted(true);
        
        records.add(record1);
        records.add(record2);
        records.add(record3);
        
        System.out.println("预览表格状态显示：");
        System.out.println("文件名                | 状态");
        System.out.println("---------------------|------------------");
        
        for (MockSkinModifyRecord record : records) {
            String status;
            if (record.isAlreadyConverted()) {
                status = "已转换";
            } else if (record.getXlsRowIndex() == -1) {
                status = "将跳过(XLS中未找到)";
            } else {
                status = "准备就绪";
            }
            
            System.out.printf("%-20s | %s%n", record.getOldFileName(), status);
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        SkipLogicTest test = new SkipLogicTest();
        test.testSkipLogic();
        test.testStatusDisplay();
    }
}
