package com.tool.skin;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 优化功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class OptimizationTest {
    
    private static final String TEST_FOLDER = "optimization_test";
    
    /**
     * 创建测试文件
     */
    public void createTestFiles() {
        System.out.println("=== 创建优化测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                // 清理旧的测试文件
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
            
            Files.createDirectory(testDir);
            System.out.println("创建测试目录: " + TEST_FOLDER);
            
            // 创建不同格式的测试文件
            String[] testFileNames = {
                // 2025-格式文件（应该显示XLS ID）
                "2025-A1B2C3D4.png",
                "2025-F5E6D7C8.png", 
                "2025-B9A8C7D6.png",
                
                // 普通文件（XLS ID应该为空）
                "weapon_sword.png",
                "armor_001.png",
                "item_potion.png",
                "6120.png",
                "9134.png",
                "skill_fireball.png"
            };
            
            for (String fileName : testFileNames) {
                Path filePath = testDir.resolve(fileName);
                // 创建一个小的测试文件
                String content = "Test PNG file: " + fileName + "\nOptimization test content.";
                Files.write(filePath, content.getBytes());
                System.out.println("创建测试文件: " + fileName);
            }
            
            System.out.println("测试文件创建完成，总计: " + testFileNames.length + " 个文件");
            
        } catch (IOException e) {
            System.out.println("创建测试文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试XLS ID生成
     */
    public void testXlsIdGeneration() {
        System.out.println("\n=== 测试XLS ID生成 ===");
        
        String[] testFileNames = {
            "2025-A1B2C3D4.png",  // 应该生成 0xA1B2C3D4
            "2025-F5E6D7C8.png",  // 应该生成 0xF5E6D7C8
            "weapon_sword.png",   // 应该生成空字符串
            "6120.png",           // 应该生成空字符串
            "2025-12345678.png"   // 应该生成 0x12345678
        };
        
        for (String fileName : testFileNames) {
            String xlsId = generateXlsId(fileName);
            System.out.printf("文件名: %-20s -> XLS ID: %s%n", fileName, xlsId.isEmpty() ? "(空)" : xlsId);
        }
    }
    
    /**
     * 生成XLS需要的ID（复制自主类的逻辑）
     */
    private String generateXlsId(String fileName) {
        if (fileName.startsWith("2025-") && fileName.endsWith(".png")) {
            // 提取十六进制部分并转换为0x格式
            String hexPart = fileName.substring(5, fileName.length() - 4);
            return "0x" + hexPart;
        }
        return ""; // 非2025-格式的文件返回空字符串
    }
    
    /**
     * 测试文件扫描一致性
     */
    public void testScanConsistency() {
        System.out.println("\n=== 测试文件扫描一致性 ===");
        
        Path testDir = Paths.get(TEST_FOLDER);
        if (!Files.exists(testDir)) {
            System.out.println("测试目录不存在，请先创建测试文件");
            return;
        }
        
        // 进行多次扫描，验证结果一致性
        for (int i = 1; i <= 3; i++) {
            try {
                int fileCount = 0;
                
                // 模拟工具的扫描逻辑
                try (java.util.stream.Stream<Path> stream = Files.list(testDir)) {
                    fileCount = (int) stream
                        .filter(path -> path.toString().toLowerCase().endsWith(".png"))
                        .count();
                }
                
                System.out.printf("第%d次扫描: 找到 %d 个PNG文件%n", i, fileCount);
                
            } catch (IOException e) {
                System.out.println("第" + i + "次扫描失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 测试主题切换功能
     */
    public void testThemeToggle() {
        System.out.println("\n=== 测试主题切换功能 ===");
        
        // 模拟主题切换逻辑
        boolean isDarkTheme = false;
        
        System.out.println("当前主题: " + (isDarkTheme ? "深色" : "浅色"));
        
        // 切换主题
        isDarkTheme = !isDarkTheme;
        System.out.println("切换后主题: " + (isDarkTheme ? "深色" : "浅色"));
        
        // 再次切换
        isDarkTheme = !isDarkTheme;
        System.out.println("再次切换后主题: " + (isDarkTheme ? "深色" : "浅色"));
        
        System.out.println("主题切换功能测试完成");
    }
    
    /**
     * 测试字体设置
     */
    public void testFontSetting() {
        System.out.println("\n=== 测试字体设置 ===");
        
        // 检查系统是否支持微软雅黑字体
        java.awt.GraphicsEnvironment ge = java.awt.GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] fontNames = ge.getAvailableFontFamilyNames();
        
        boolean hasMicrosoftYaHei = false;
        for (String fontName : fontNames) {
            if (fontName.equals("微软雅黑")) {
                hasMicrosoftYaHei = true;
                break;
            }
        }
        
        System.out.println("系统支持微软雅黑字体: " + (hasMicrosoftYaHei ? "是" : "否"));
        
        if (hasMicrosoftYaHei) {
            java.awt.Font font = new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12);
            System.out.println("字体创建成功: " + font.getName() + ", 大小: " + font.getSize());
        } else {
            System.out.println("系统不支持微软雅黑字体，将使用默认字体");
        }
    }
    
    /**
     * 测试复制功能
     */
    public void testCopyFunction() {
        System.out.println("\n=== 测试复制功能 ===");
        
        String testText = "0xA1B2C3D4";
        
        try {
            // 模拟复制到剪贴板
            java.awt.datatransfer.StringSelection selection = 
                new java.awt.datatransfer.StringSelection(testText);
            java.awt.Toolkit.getDefaultToolkit().getSystemClipboard().setContents(selection, null);
            
            System.out.println("已复制到剪贴板: " + testText);
            
            // 验证复制是否成功
            java.awt.datatransfer.Transferable contents = 
                java.awt.Toolkit.getDefaultToolkit().getSystemClipboard().getContents(null);
            
            if (contents != null && contents.isDataFlavorSupported(java.awt.datatransfer.DataFlavor.stringFlavor)) {
                String clipboardText = (String) contents.getTransferData(java.awt.datatransfer.DataFlavor.stringFlavor);
                System.out.println("剪贴板内容验证: " + clipboardText);
                System.out.println("复制功能测试: " + (testText.equals(clipboardText) ? "成功" : "失败"));
            }
            
        } catch (Exception e) {
            System.out.println("复制功能测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            System.out.println("删除: " + path.getFileName());
                        } catch (IOException e) {
                            System.out.println("删除失败: " + path.getFileName());
                        }
                    });
                System.out.println("测试文件清理完成");
            }
        } catch (IOException e) {
            System.out.println("清理测试文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        createTestFiles();
        testXlsIdGeneration();
        testScanConsistency();
        testThemeToggle();
        testFontSetting();
        testCopyFunction();
        
        System.out.println("\n=== 优化功能测试完成 ===");
        System.out.println("如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        OptimizationTest test = new OptimizationTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllTests();
        }
    }
}
