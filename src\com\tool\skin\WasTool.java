package jxy2.wdfDome.util;

import com.tool.tcp.Sprite;
import com.tool.tcp.SpriteHead;
import jxy2.jutnil.CheckTimer;
import jxy2.jutnil.StreamTimer;
import jxy2.wdfDome.bean.FrameData;
import jxy2.wdfDome.bean.ImageInfo;
import jxy2.wdfDome.bean.WasData;
import jxy2.wdfDome.bean.WasHead;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.channels.Channels;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class WasTool {

    /**
     * 从指定文件和偏移量处获取Web Application Server的头部信息。
     *
     * @param file   指定的文件对象，用于读取WAS头部信息。
     * @param offset 文件中的偏移量，表示从文件的哪个位置开始读取。
     * @return WasHead对象，包含解析出的WAS头部信息。
     */
    public static WasHead getWasHead(File file, long offset) {
        try {
            RandomAccessFile in = new RandomAccessFile(file, "r");
            in.seek(offset);
            WasHead wasHead = null;
            byte[] buf = new byte[16];
            in.read(buf);
            int flag = DataTool.byte2uint16(buf, 0);
            if (flag == 20563) {
                wasHead = new WasHead();
                wasHead.setFlag(flag);
                wasHead.setHeadSize(DataTool.byte2uint16(buf, 2));
                wasHead.setDirectionNum(DataTool.byte2uint16(buf, 4));
                wasHead.setSpriteFrame(DataTool.byte2uint16(buf, 6));
                wasHead.setSpriteWidth(DataTool.byte2uint16(buf, 8));
                wasHead.setSpriteHeight(DataTool.byte2uint16(buf, 10));
                wasHead.setSpriteCenterX(DataTool.byte2int16(buf, 12));
                wasHead.setSpriteCenterY(DataTool.byte2int16(buf, 14));
                int len = wasHead.getHeadSize() - 12;
                if (len < 0) {
                    System.out.println("帧延时信息错误: " + len);
                }
                for (int i = 0; i < len; ++i) {
                    in.read();
                }
                buf = new byte[512];
                in.read(buf);
                int[] colorBoard = new int[256];
                for (int i = 0; i < 256; ++i) {
                    colorBoard[i] = DataTool.byte2uint16(buf, i * 2);
                }
                wasHead.setColorBoard(colorBoard);
                FrameData[] list = new FrameData[wasHead.getSpriteFrame()];
                buf = new byte[list.length * 4];
                in.read(buf);
                byte[] buf2 = new byte[16];
                int i = 0;
                while (true) {
                    if (i >= list.length) {
                        wasHead.setFrameDataList(list);
                        break;
                    }
                    FrameData frameData = new FrameData();
                    frameData.setOffset(DataTool.byte2uint32(buf, i * 4));
                    in.seek(offset + (long) wasHead.getHeadSize() + 4L + frameData.getOffset());
                    in.read(buf2);
                    frameData.setCenterX(DataTool.byte2int32(buf2, 0));
                    frameData.setCenterY(DataTool.byte2int32(buf2, 4));
                    frameData.setWidth(DataTool.byte2int32(buf2, 8));
                    frameData.setHeight(DataTool.byte2int32(buf2, 12));
                    if (frameData.getHeight() > (long) wasHead.getSpriteHeight()) {
                        frameData.setWidth(wasHead.getSpriteWidth());
                        frameData.setHeight(wasHead.getSpriteHeight());
                    }
                    try {
                        long[] rowOffset = new long[(int) frameData.getHeight()];
                        byte[] buf3 = new byte[rowOffset.length * 4];
                        in.read(buf3);
                        for (int j = 0; j < rowOffset.length; ++j) {
                            rowOffset[j] = DataTool.byte2uint32(buf3, j * 4);
                        }
                        frameData.setRowOffset(rowOffset);
                        int[][] pixels = getPixels(in, offset, wasHead, frameData);
                        BufferedImage bufimage = getBufferedImage((int) ((long) wasHead.getSpriteCenterX() - frameData.getCenterX()), (int) ((long) wasHead.getSpriteCenterY() - frameData.getCenterY()), wasHead.getSpriteWidth(), wasHead.getSpriteHeight(), pixels);
                        frameData.setBufImage(bufimage);
                    } catch (Exception var17) {
                        frameData.setBufImage(new BufferedImage(wasHead.getSpriteWidth(), wasHead.getSpriteHeight(), 2));
                    }
                    list[i] = frameData;
                    ++i;
                }
            }
            in.close();
            return wasHead;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将ImageInfo对象转换为BufferedImage。
     * @param imageInfo 包含图像信息的对象，通常是从某个图像源（如文件、数据库等）获取的。
     * @return BufferedImage 对象，代表了一张图像，可以直接用于图形绘制和处理。
     */
    private static final Map<String, BufferedImage> imageCache = new HashMap<>();
    private static final ExecutorService executorService = Executors.newFixedThreadPool(5); // Thread pool for loading images
    public static Future<BufferedImage> loadCachedImage(ImageInfo imageInfo) {
        return executorService.submit(() -> {
            BufferedImage cachedImage = imageCache.get(imageInfo.cacheKey);
            if (cachedImage != null) {
                CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, cachedImage));
                return cachedImage;
            } else {
                BufferedImage newImage = was2BufferedImage(imageInfo);
                if (newImage != null) {
                    clearCache();
                    imageCache.put(imageInfo.cacheKey, newImage);
                    removeImageFromCache(imageInfo.cacheKey);
                    CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, newImage));
                }
                return newImage;
            }
        });
    }

    public static BufferedImage was2BufferedImage(ImageInfo imageInfo) {
        try (RandomAccessFile in = new RandomAccessFile(imageInfo.file, "r")) {
            in.seek(imageInfo.offset);
            // 将 RandomAccessFile 转为 InputStream，避免中间内存拷贝
            try (InputStream inputStream = new BufferedInputStream(new FileInputStream(in.getFD()))) {
                CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, inputStream));
                return ImageIO.read(inputStream);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }



    public static void clearCache() {
        imageCache.clear();
    }

    public static void removeImageFromCache(String cacheKey) {
        BufferedImage image = imageCache.remove(cacheKey);
        if (image != null) {
            image.flush();
        }
    }


    /**
     * 从指定文件中获取"WasHeadnew"精灵对象。
     * @param file 指定的文件，精灵数据将从此文件中读取。
     * @param offset 读取数据的偏移量，用于指定从文件的哪个位置开始读取精灵数据。
     * @return 返回从文件中读取到的"WasHeadnew"精灵对象。
     */
    public static Map<String, Sprite> spriteCache = new HashMap<>();
    public static Sprite getWasHeadnew(File file, WasData wasData) {
        String wasname = DataTool.ten2six(String.valueOf(wasData.getId()));
        // TODO: 实现从文件中读取"WasHeadnew"精灵对象的逻辑
        Sprite sprite = null;
        try {
            RandomAccessFile in = new RandomAccessFile(file, "r");
            in.seek(wasData.getFileOffset());
            InputStream inputStream = Channels.newInputStream(in.getChannel());
            byte[] buf = new byte[2];
            inputStream.read(buf);
            String fag = new String(buf, StandardCharsets.UTF_8);
            int version = fag.equals("SP") ? 0 : fag.equals("SH") ? 1 : -1;
            if (version >= 0) {
                buf = new byte[inputStream.available()];
                int a, count = 0;
                while (inputStream.available() > 0) {
                    a = in.read(buf, count, inputStream.available());
                    count += a;
                }
                in.close();
                SpriteHead randomIn = new SpriteHead(buf);
                sprite = randomIn.init(null, false, version);
                CheckTimer.add(new StreamTimer(System.currentTimeMillis() + 3 * 1000, inputStream));
                sprite.removeHead();
                inputStream.close();
            } else {
                in.close();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sprite;
    }


    private static int[][] getPixels(RandomAccessFile in, long offset, WasHead wasHead, FrameData frameData) throws Exception {
        int frameWidth = (int)frameData.getWidth();
        int frameHeight = (int)frameData.getHeight();
        int[][] pixels = new int[frameHeight][frameWidth];
        int[] palette = wasHead.getColorBoard();

        label68:
        for(int y = 0; y < frameHeight; ++y) {
            int x = 0;
            in.seek(offset + (long)wasHead.getHeadSize() + 4L + frameData.getOffset() + frameData.getRowOffset()[y]);

            while(true) {
                label63:
                while(true) {
                    if (x >= frameWidth) {
                        continue label68;
                    }

                    int b = in.read();
                    int index;
                    int count;
                    int c;
                    int i;
                    switch (b & 192) {
                        case 0:
                            if ((b & 32) > 0) {
                                index = in.read();
                                c = palette[index];
                                pixels[y][x++] = c + ((b & 31) << 16);
                            } else if (b != 0) {
                                count = b & 31;
                                b = in.read();
                                index = in.read();
                                c = palette[index];
                                i = 0;

                                while(true) {
                                    if (i >= count) {
                                        break;
                                    }

                                    pixels[y][x++] = c + ((b & 31) << 16);
                                    ++i;
                                }
                            } else if (x <= frameWidth && x != 0) {
                                x = frameWidth;
                            }
                            break;
                        case 64:
                            count = b & 63;
                            c = 0;

                            while(true) {
                                if (c >= count) {
                                    continue label63;
                                }

                                index = in.read();
                                pixels[y][x++] = palette[index] + 2031616;
                                ++c;
                            }
                        case 128:
                            count = b & 63;
                            index = in.read();
                            c = palette[index];
                            i = 0;

                            while(true) {
                                if (i >= count) {
                                    continue label63;
                                }

                                pixels[y][x++] = c + 2031616;
                                ++i;
                            }
                        case 192:
                            count = b & 63;
                            if (count == 0) {
                                in.read();
                                in.read();
                            }

                            x += count;
                    }
                }
            }
        }
        return pixels;
    }

    public static BufferedImage getBufferedImage(int x, int y, int w, int h, int[][] c) {
        BufferedImage buf = new BufferedImage(w, h, 2);
        int[] pixels = new int[4];

        for(int row = 0; row < c.length; ++row) {
            for(int cell = 0; cell < c[0].length; ++cell) {
                int pixel = c[row][cell];
                if (pixel == 0) {
                    pixels[0] = 0;
                    pixels[1] = 0;
                    pixels[2] = 0;
                    pixels[3] = 0;
                } else {
                    pixels[0] = (pixel >>> 11 & 31) * 255 / 31;
                    pixels[1] = (pixel >>> 5 & 63) * 255 / 63;
                    pixels[2] = (pixel & 31) * 255 / 31;
                    pixels[3] = (pixel >>> 16 & 31) * 255 / 31;
                }

                if (cell + x >= 0 && row + y >= 0 && cell + x < w && row + y < h) {
                    buf.getRaster().setPixel(cell + x, row + y, pixels);
                } else {
                    buf.getRaster().setPixel(cell, row, pixels);
                }
            }
        }

        return buf;
    }

    public static BufferedImage[] was2Images(WasHead wasHead, int direction) {
        BufferedImage[] bufImage = new BufferedImage[wasHead.getSpriteFrame()];
        for(int i = 0; i < bufImage.length; ++i) {
            bufImage[i] = wasHead.getFrameDataList()[wasHead.getSpriteFrame() * direction + i].getBufImage();
        }
        return bufImage;
    }

    public static BufferedImage[] was2Images(WasHead wasHead) {
        BufferedImage[] bufImage = new BufferedImage[wasHead.getFrameDataList().length];
        for(int i = 0; i < bufImage.length; ++i) {
            bufImage[i] = wasHead.getFrameDataList()[i].getBufImage();
        }
        return bufImage;
    }

    public static BufferedImage was2Imagess(WasHead wasHead) {
        BufferedImage[] bufImage = new BufferedImage[wasHead.getFrameDataList().length];
        for(int i = 0; i < bufImage.length; ++i) {
            return wasHead.getFrameDataList()[i].getBufImage();
        }
        return null;
    }



}
