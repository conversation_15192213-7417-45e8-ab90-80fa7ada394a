package com.tool.wdf;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.zip.CRC32;

/**
 * WDF文件打包器
 * 基于常见的WDF文件格式实现
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class WdfPacker {
    
    // WDF文件头标识
    private static final byte[] WDF_SIGNATURE = {'W', 'D', 'F', 0x01};
    
    // 文件条目结构
    public static class WdfEntry {
        public String fileName;
        public long offset;
        public long size;
        public long crc32;
        public byte[] data;
        
        public WdfEntry(String fileName, byte[] data) {
            this.fileName = fileName;
            this.data = data;
            this.size = data.length;
            
            // 计算CRC32
            CRC32 crc = new CRC32();
            crc.update(data);
            this.crc32 = crc.getValue();
        }
    }
    
    /**
     * 打包文件夹到WDF文件
     */
    public static void packFolder(String sourceFolder, String outputWdf, ProgressCallback callback) throws IOException {
        File sourceDir = new File(sourceFolder);
        if (!sourceDir.exists() || !sourceDir.isDirectory()) {
            throw new IOException("源文件夹不存在或不是目录: " + sourceFolder);
        }
        
        // 收集所有文件
        List<WdfEntry> entries = new ArrayList<>();
        collectFiles(sourceDir, sourceDir, entries, callback);
        
        if (entries.isEmpty()) {
            throw new IOException("源文件夹中没有找到文件");
        }
        
        // 写入WDF文件
        writeWdfFile(outputWdf, entries, callback);
    }
    
    /**
     * 递归收集文件
     */
    private static void collectFiles(File baseDir, File currentDir, List<WdfEntry> entries, ProgressCallback callback) throws IOException {
        File[] files = currentDir.listFiles();
        if (files == null) return;
        
        for (File file : files) {
            if (file.isDirectory()) {
                // 递归处理子目录
                collectFiles(baseDir, file, entries, callback);
            } else {
                // 读取文件数据
                byte[] data = Files.readAllBytes(file.toPath());
                
                // 计算相对路径
                String relativePath = baseDir.toPath().relativize(file.toPath()).toString();
                relativePath = relativePath.replace('\\', '/'); // 统一使用正斜杠
                
                WdfEntry entry = new WdfEntry(relativePath, data);
                entries.add(entry);
                
                if (callback != null) {
                    callback.onFileProcessed(relativePath, data.length);
                }
            }
        }
    }
    
    /**
     * 写入WDF文件
     */
    private static void writeWdfFile(String outputWdf, List<WdfEntry> entries, ProgressCallback callback) throws IOException {
        try (DataOutputStream dos = new DataOutputStream(new BufferedOutputStream(new FileOutputStream(outputWdf)))) {
            
            // 写入文件头
            dos.write(WDF_SIGNATURE);
            dos.writeInt(Integer.reverseBytes(entries.size())); // 文件数量（小端序）
            
            // 计算文件数据起始位置
            long dataOffset = 8; // 文件头大小
            dataOffset += entries.size() * (4 + 4 + 8 + 8 + 256); // 每个条目的索引大小
            
            // 写入文件索引
            long currentOffset = dataOffset;
            for (WdfEntry entry : entries) {
                entry.offset = currentOffset;
                
                // 写入文件名长度和文件名
                byte[] nameBytes = entry.fileName.getBytes("UTF-8");
                dos.writeInt(Integer.reverseBytes(nameBytes.length));
                
                // 文件名固定256字节
                byte[] nameBuffer = new byte[256];
                System.arraycopy(nameBytes, 0, nameBuffer, 0, Math.min(nameBytes.length, 256));
                dos.write(nameBuffer);
                
                // 写入文件信息
                dos.writeLong(Long.reverseBytes(entry.offset));  // 偏移量（小端序）
                dos.writeLong(Long.reverseBytes(entry.size));    // 大小（小端序）
                dos.writeInt(Integer.reverseBytes((int)entry.crc32)); // CRC32（小端序）
                
                currentOffset += entry.size;
            }
            
            // 写入文件数据
            for (WdfEntry entry : entries) {
                dos.write(entry.data);
                
                if (callback != null) {
                    callback.onFileWritten(entry.fileName, entry.size);
                }
            }
            
            dos.flush();
        }
        
        if (callback != null) {
            callback.onCompleted(outputWdf, entries.size());
        }
    }
    
    /**
     * 进度回调接口
     */
    public interface ProgressCallback {
        void onFileProcessed(String fileName, long size);
        void onFileWritten(String fileName, long size);
        void onCompleted(String outputFile, int fileCount);
        void onError(String message);
    }
    
    /**
     * 获取WDF文件信息
     */
    public static WdfInfo getWdfInfo(String wdfFile) throws IOException {
        try (DataInputStream dis = new DataInputStream(new BufferedInputStream(new FileInputStream(wdfFile)))) {
            
            // 读取文件头
            byte[] signature = new byte[4];
            dis.readFully(signature);
            
            if (!Arrays.equals(signature, WDF_SIGNATURE)) {
                throw new IOException("不是有效的WDF文件");
            }
            
            int fileCount = Integer.reverseBytes(dis.readInt());
            
            WdfInfo info = new WdfInfo();
            info.fileCount = fileCount;
            info.totalSize = new File(wdfFile).length();
            
            return info;
        }
    }
    
    /**
     * WDF文件信息
     */
    public static class WdfInfo {
        public int fileCount;
        public long totalSize;
        
        @Override
        public String toString() {
            return String.format("文件数量: %d, 总大小: %d 字节", fileCount, totalSize);
        }
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }
}
