package com.tool.config;

import java.io.*;
import java.util.Properties;

/**
 * 路径配置管理类
 * 从外部配置文件读取默认路径设置
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class PathConfig {
    
    private static final String CONFIG_FILE = "config.properties";
    private static Properties properties;
    private static PathConfig instance;
    
    // 默认路径（当配置文件不存在或读取失败时使用）
    private static final String DEFAULT_ITEM_FOLDER = "G:\\JXy2o\\GameClient3.0\\res\\item";
    private static final String DEFAULT_ITEM_BASE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_XLS_PATH = "G:\\JXy2o\\GameClient3.0\\res\\config\\item.xls";
    private static final String DEFAULT_WDF_SOURCE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_WDF_OUTPUT = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_SOURCE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_OUTPUT = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_NPK_FILE = "G:\\JXy2o\\GameClient3.0\\res";
    private static final String DEFAULT_FALLBACK = "C:\\";
    
    private PathConfig() {
        loadConfig();
    }
    
    /**
     * 获取单例实例
     */
    public static PathConfig getInstance() {
        if (instance == null) {
            instance = new PathConfig();
        }
        return instance;
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        properties = new Properties();
        
        try {
            // 尝试从当前目录加载配置文件
            File configFile = new File(CONFIG_FILE);
            if (configFile.exists()) {
                try (FileInputStream fis = new FileInputStream(configFile);
                     InputStreamReader isr = new InputStreamReader(fis, "UTF-8")) {
                    properties.load(isr);
                    System.out.println("成功加载配置文件: " + configFile.getAbsolutePath());
                }
            } else {
                System.out.println("配置文件不存在，使用默认路径: " + configFile.getAbsolutePath());
                createDefaultConfig();
            }
        } catch (IOException e) {
            System.err.println("加载配置文件失败，使用默认路径: " + e.getMessage());
        }
    }
    
    /**
     * 创建默认配置文件
     */
    private void createDefaultConfig() {
        try {
            properties.setProperty("default.item.folder", DEFAULT_ITEM_FOLDER);
            properties.setProperty("default.item.base", DEFAULT_ITEM_BASE);
            properties.setProperty("default.xls.path", DEFAULT_XLS_PATH);
            properties.setProperty("default.wdf.source", DEFAULT_WDF_SOURCE);
            properties.setProperty("default.wdf.output", DEFAULT_WDF_OUTPUT);
            properties.setProperty("default.npk.source", DEFAULT_NPK_SOURCE);
            properties.setProperty("default.npk.output", DEFAULT_NPK_OUTPUT);
            properties.setProperty("default.npk.file", DEFAULT_NPK_FILE);
            properties.setProperty("fallback.path", DEFAULT_FALLBACK);
            
            saveConfig();
            System.out.println("已创建默认配置文件: " + CONFIG_FILE);
        } catch (Exception e) {
            System.err.println("创建默认配置文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try (FileOutputStream fos = new FileOutputStream(CONFIG_FILE);
             OutputStreamWriter osw = new OutputStreamWriter(fos, "UTF-8")) {
            properties.store(osw, "皮肤ID批量转换工具 - 默认路径配置文件");
            System.out.println("配置文件已保存: " + CONFIG_FILE);
        } catch (IOException e) {
            System.err.println("保存配置文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        loadConfig();
    }
    
    /**
     * 获取配置值，如果不存在则返回默认值
     */
    private String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    // 获取各种路径的方法
    public String getItemFolder() {
        return getProperty("default.item.folder", DEFAULT_ITEM_FOLDER);
    }
    
    public String getItemBase() {
        return getProperty("default.item.base", DEFAULT_ITEM_BASE);
    }
    
    public String getXlsPath() {
        return getProperty("default.xls.path", DEFAULT_XLS_PATH);
    }
    
    public String getWdfSource() {
        return getProperty("default.wdf.source", DEFAULT_WDF_SOURCE);
    }
    
    public String getWdfOutput() {
        return getProperty("default.wdf.output", DEFAULT_WDF_OUTPUT);
    }
    
    public String getNpkSource() {
        return getProperty("default.npk.source", DEFAULT_NPK_SOURCE);
    }
    
    public String getNpkOutput() {
        return getProperty("default.npk.output", DEFAULT_NPK_OUTPUT);
    }
    
    public String getNpkFile() {
        return getProperty("default.npk.file", DEFAULT_NPK_FILE);
    }
    
    public String getFallbackPath() {
        return getProperty("fallback.path", DEFAULT_FALLBACK);
    }
    
    /**
     * 设置配置值
     */
    public void setProperty(String key, String value) {
        properties.setProperty(key, value);
    }
    
    /**
     * 验证路径是否存在
     */
    public boolean isPathValid(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false;
        }
        File file = new File(path);
        return file.exists();
    }
    
    /**
     * 获取有效的路径（如果主路径不存在，返回备用路径）
     */
    public String getValidPath(String primaryPath) {
        if (isPathValid(primaryPath)) {
            return primaryPath;
        }
        String fallback = getFallbackPath();
        if (isPathValid(fallback)) {
            return fallback;
        }
        return System.getProperty("user.home", "C:\\");
    }
    
    /**
     * 打印当前配置信息
     */
    public void printConfig() {
        System.out.println("=== 当前路径配置 ===");
        System.out.println("皮肤文件夹: " + getItemFolder());
        System.out.println("游戏资源目录: " + getItemBase());
        System.out.println("XLS配置文件: " + getXlsPath());
        System.out.println("WDF源文件夹: " + getWdfSource());
        System.out.println("WDF输出目录: " + getWdfOutput());
        System.out.println("NPK源文件夹: " + getNpkSource());
        System.out.println("NPK输出目录: " + getNpkOutput());
        System.out.println("NPK文件目录: " + getNpkFile());
        System.out.println("备用路径: " + getFallbackPath());
        System.out.println("==================");
    }
}
