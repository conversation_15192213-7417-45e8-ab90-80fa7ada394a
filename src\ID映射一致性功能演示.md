# ID映射一致性功能演示

## 功能概述

新版本的皮肤ID同步工具实现了ID映射一致性功能，确保相同的原始ID使用相同的转换后ID，从而实现皮肤文件的共享。

## 核心特性

### 1. ID映射一致性
- 相同的原始皮肤ID会被转换为相同的新ID
- 确保多个物品可以共享同一个皮肤文件
- 减少资源冗余，节省存储空间

### 2. 批量更新
- 同时更新XLS中所有匹配的行
- 保证数据的完整性和一致性
- 提高处理效率

## 工作流程

### 第1步：分析XLS文件
```
正在分析XLS文件，建立ID映射...
为原始ID 6120 生成新ID: 0xDDFAFD8E
为原始ID 9134 生成新ID: 0xC4F14BCE
为原始ID 255 生成新ID: 0x6475391E
ID映射表建立完成，共 154 个映射
```

### 第2步：扫描PNG文件
```
正在扫描PNG文件...
扫描完成，总计 2088 个文件
  - 准备转换: 154 个
  - 将跳过: 1934 个 (XLS中未找到)
  - 已转换: 0 个
```

### 第3步：执行转换
```
开始执行修改操作...
文件重命名: 6120.png -> 2025-DDFAFD8E.png
已更新XLS中所有 6120 -> 0xDDFAFD8E
  更新行1207: 6120 -> 0xDDFAFD8E
  更新行1208: 6120 -> 0xDDFAFD8E
  更新行1209: 6120 -> 0xDDFAFD8E
  更新行1210: 6120 -> 0xDDFAFD8E
  共更新了 4 行
```

## 实际案例演示

### 案例：多情环的皮肤ID转换

**转换前的XLS数据：**
```
物品名    描述    皮肤ID
多情环    装备    6120
多情环    装备    6120
多情环    装备    6120
多情环    装备    6120
```

**转换后的XLS数据：**
```
物品名    描述    皮肤ID
多情环    装备    0xDDFAFD8E
多情环    装备    0xDDFAFD8E
多情环    装备    0xDDFAFD8E
多情环    装备    0xDDFAFD8E
```

**文件转换：**
```
6120.png -> 2025-DDFAFD8E.png
```

**结果：**
- 所有"多情环"物品都使用相同的皮肤ID：0xDDFAFD8E
- 它们共享同一个皮肤文件：2025-DDFAFD8E.png
- 游戏中所有多情环的外观保持一致

## 技术实现

### 1. ID映射表
```java
private Map<String, String> idMappingCache = new HashMap<>();

// 建立映射：原始ID -> 新ID
idMappingCache.put("6120", "0xDDFAFD8E");
idMappingCache.put("9134", "0xC4F14BCE");
```

### 2. 批量更新逻辑
```java
private void updateAllMatchingXlsRows(String oldSkinId, String newSkinId) {
    for (int i = 0; i < xlsData.length; i++) {
        if (xlsData[i][2].equals(oldSkinId)) {
            xlsData[i][2] = newSkinId;
            // 更新所有匹配的行
        }
    }
}
```

### 3. 文件名生成
```java
String newSkinId = idMappingCache.get(oldSkinId);
if (newSkinId != null) {
    String hexPart = newSkinId.substring(2); // 去掉"0x"
    String newFileName = "2025-" + hexPart + ".png";
}
```

## 优势分析

### 1. 资源优化
- **减少文件数量**：相同ID的物品共享皮肤文件
- **节省存储空间**：避免重复的皮肤资源
- **提高加载效率**：减少游戏资源加载时间

### 2. 数据一致性
- **统一外观**：相同ID的物品外观完全一致
- **避免错误**：防止同一物品在不同场景下外观不同
- **便于维护**：修改皮肤时只需更新一个文件

### 3. 开发效率
- **批量处理**：一次操作更新所有相关条目
- **自动化**：无需手动处理重复ID
- **可追溯**：详细的日志记录每个更新操作

## 使用建议

### 1. 转换前准备
- 备份原始的XLS文件和PNG文件
- 确认哪些物品应该共享皮肤
- 检查XLS文件中的数据完整性

### 2. 转换过程
- 仔细查看预览表格，确认映射关系
- 注意日志中的更新信息
- 确保所有相同ID都被正确更新

### 3. 转换后验证
- 检查XLS文件中相同ID是否都被更新为相同值
- 验证生成的皮肤文件是否正确
- 在游戏中测试物品外观的一致性

## 注意事项

1. **备份重要性**：转换操作不可逆，务必备份原始文件
2. **ID冲突**：确保新生成的ID不与现有ID冲突
3. **文件权限**：确保对XLS文件和PNG文件夹有读写权限
4. **游戏兼容性**：转换后需要在游戏中测试功能是否正常

## 故障排除

### 问题1：某些相同ID没有被更新
**原因**：XLS文件中可能存在格式不一致的问题
**解决**：检查XLS文件中的空格、特殊字符等

### 问题2：生成的文件名重复
**原因**：随机ID生成算法可能产生重复
**解决**：重新运行工具，算法会生成新的随机ID

### 问题3：游戏中物品外观异常
**原因**：皮肤文件路径或ID可能不正确
**解决**：检查生成的文件名和XLS中的ID是否匹配

## 总结

ID映射一致性功能确保了皮肤资源的高效利用和数据的一致性，是游戏资源管理的重要改进。通过这个功能，开发者可以更好地管理游戏中的皮肤资源，提高游戏性能和维护效率。
