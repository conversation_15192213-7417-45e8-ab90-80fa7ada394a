package com.tool.npk;

import javax.swing.*;
import java.awt.*;
import java.awt.datatransfer.DataFlavor;
import java.awt.datatransfer.Transferable;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import javax.imageio.ImageIO;

/**
 * NPK查看器辅助类
 * 提供NPK文件加载、图像预览等功能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkViewerHelper {
    
    /**
     * 异步加载NPK文件
     * 
     * @param npkFilePath NPK文件路径
     * @param progressCallback 进度回调 (进度百分比, 状态信息)
     * @return CompletableFuture<NpkFile>
     */
    public static CompletableFuture<NpkFile> loadNpkFileAsync(String npkFilePath, 
                                                             java.util.function.BiConsumer<Integer, String> progressCallback) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (progressCallback != null) {
                    progressCallback.accept(10, "开始加载NPK文件...");
                }
                
                File npkFile = new File(npkFilePath);
                if (!npkFile.exists()) {
                    throw new RuntimeException("NPK文件不存在: " + npkFilePath);
                }
                
                if (progressCallback != null) {
                    progressCallback.accept(30, "解析NPK文件结构...");
                }
                
                NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, (progress, message) -> {
                    if (progressCallback != null) {
                        // 将解析进度映射到30-90%
                        int mappedProgress = 30 + (progress * 60 / 100);
                        progressCallback.accept(mappedProgress, message);
                    }
                });
                
                if (progressCallback != null) {
                    progressCallback.accept(100, "NPK文件加载完成");
                }
                
                return parsedFile;
                
            } catch (Exception e) {
                if (progressCallback != null) {
                    progressCallback.accept(-1, "加载失败: " + e.getMessage());
                }
                throw new RuntimeException("加载NPK文件失败", e);
            }
        });
    }
    
    /**
     * 异步预览图像
     * 
     * @param npkFile NPK文件对象
     * @param entry NPK条目
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return CompletableFuture<ImageIcon>
     */
    public static CompletableFuture<ImageIcon> previewImageAsync(NpkFile npkFile, NpkEntry entry, 
                                                               int maxWidth, int maxHeight) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 提取图像数据
                byte[] imageData = NpkTool.extractFile(npkFile, entry, null);
                if (imageData == null || imageData.length == 0) {
                    return createErrorIcon("提取失败", maxWidth, maxHeight);
                }
                
                // 验证PNG格式
                if (!isPngData(imageData)) {
                    return createErrorIcon("非PNG格式", maxWidth, maxHeight);
                }
                
                // 读取图像
                BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageData));
                if (bufferedImage == null) {
                    return createErrorIcon("图像损坏", maxWidth, maxHeight);
                }
                
                // 计算缩放尺寸
                Dimension scaledSize = calculateScaledSize(
                    bufferedImage.getWidth(), bufferedImage.getHeight(), 
                    maxWidth, maxHeight
                );
                
                // 缩放图像
                Image scaledImage = bufferedImage.getScaledInstance(
                    scaledSize.width, scaledSize.height, Image.SCALE_SMOOTH
                );
                
                return new ImageIcon(scaledImage);
                
            } catch (Exception e) {
                return createErrorIcon("预览失败: " + e.getMessage(), maxWidth, maxHeight);
            }
        });
    }
    
    /**
     * 获取图像的原始尺寸
     * 
     * @param npkFile NPK文件对象
     * @param entry NPK条目
     * @return 图像尺寸，失败返回null
     */
    public static Dimension getImageDimensions(NpkFile npkFile, NpkEntry entry) {
        try {
            byte[] imageData = NpkTool.extractFile(npkFile, entry, null);
            if (imageData == null || imageData.length == 0) {
                return null;
            }
            
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (bufferedImage == null) {
                return null;
            }
            
            return new Dimension(bufferedImage.getWidth(), bufferedImage.getHeight());
            
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * 保存图像到文件
     * 
     * @param npkFile NPK文件对象
     * @param entry NPK条目
     * @param outputFile 输出文件
     * @return 是否成功
     */
    public static boolean saveImage(NpkFile npkFile, NpkEntry entry, File outputFile) {
        try {
            byte[] imageData = NpkTool.extractFile(npkFile, entry, null);
            if (imageData == null || imageData.length == 0) {
                return false;
            }
            
            java.nio.file.Files.write(outputFile.toPath(), imageData);
            return true;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 复制图像到剪贴板
     * 
     * @param npkFile NPK文件对象
     * @param entry NPK条目
     * @return 是否成功
     */
    public static boolean copyImageToClipboard(NpkFile npkFile, NpkEntry entry) {
        try {
            byte[] imageData = NpkTool.extractFile(npkFile, entry, null);
            if (imageData == null || imageData.length == 0) {
                return false;
            }
            
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (bufferedImage == null) {
                return false;
            }
            
            // 复制到剪贴板
            java.awt.datatransfer.Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
            Transferable transferable = new Transferable() {
                @Override
                public DataFlavor[] getTransferDataFlavors() {
                    return new DataFlavor[]{DataFlavor.imageFlavor};
                }

                @Override
                public boolean isDataFlavorSupported(DataFlavor flavor) {
                    return DataFlavor.imageFlavor.equals(flavor);
                }

                @Override
                public Object getTransferData(DataFlavor flavor) {
                    return bufferedImage;
                }
            };
            
            clipboard.setContents(transferable, null);
            return true;
            
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 搜索NPK文件中的条目
     * 
     * @param entries 所有条目
     * @param searchText 搜索文本
     * @return 匹配的条目列表
     */
    public static java.util.List<NpkEntry> searchEntries(List<NpkEntry> entries, String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return entries;
        }
        
        String lowerSearchText = searchText.toLowerCase();
        return entries.stream()
                .filter(entry -> entry.getFileName().toLowerCase().contains(lowerSearchText))
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 创建错误图标
     */
    private static ImageIcon createErrorIcon(String errorMessage, int width, int height) {
        BufferedImage errorImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = errorImage.createGraphics();
        
        // 设置背景
        g2d.setColor(Color.LIGHT_GRAY);
        g2d.fillRect(0, 0, width, height);
        
        // 设置文字
        g2d.setColor(Color.RED);
        g2d.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));
        
        FontMetrics fm = g2d.getFontMetrics();
        int textWidth = fm.stringWidth(errorMessage);
        int textHeight = fm.getHeight();
        
        int x = (width - textWidth) / 2;
        int y = (height - textHeight) / 2 + fm.getAscent();
        
        g2d.drawString(errorMessage, x, y);
        g2d.dispose();
        
        return new ImageIcon(errorImage);
    }
    
    /**
     * 计算缩放尺寸
     */
    private static Dimension calculateScaledSize(int originalWidth, int originalHeight, 
                                               int maxWidth, int maxHeight) {
        double scaleX = (double) maxWidth / originalWidth;
        double scaleY = (double) maxHeight / originalHeight;
        double scale = Math.min(scaleX, scaleY);
        
        // 如果原图比目标尺寸小，不放大
        if (scale > 1.0) {
            scale = 1.0;
        }
        
        int scaledWidth = (int) (originalWidth * scale);
        int scaledHeight = (int) (originalHeight * scale);
        
        return new Dimension(scaledWidth, scaledHeight);
    }
    
    /**
     * 检查是否是PNG数据
     */
    private static boolean isPngData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }
        
        return data[0] == (byte)0x89 && 
               data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && 
               data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && 
               data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && 
               data[7] == (byte)0x0A;
    }
}
