package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/**
 * 精确的6165条目分析器
 * 目标：找到所有6165个条目，其中5872个PNG + 293个其他文件
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class Precise6165EntryAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 精确分析6165个条目
     */
    public void analyzePrecise6165Entries() {
        System.out.println("=== 精确6165条目分析 ===");
        System.out.println("目标: 找到所有6165个条目 (5872个PNG + 293个其他文件)");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            System.out.println("文件大小: " + formatFileSize(raf.length()));
            
            // 重新计算正确的条目大小
            recalculateEntrySize(raf);
            
            // 尝试不同的解析策略
            tryDifferentParsingStrategies(raf);
            
            // 分析为什么只能解析3083个条目
            analyzeParsingLimitation(raf);
            
            // 寻找剩余条目的位置
            findRemainingEntries(raf);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 重新计算条目大小
     */
    private void recalculateEntrySize(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 重新计算条目大小 ---");
        
        long indexOffset = 0x7354149L;
        long fileSize = raf.length();
        long indexSize = fileSize - indexOffset;
        
        System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
        System.out.println("索引大小: " + indexSize + " 字节");
        System.out.println("期望条目数: 6165");
        
        // 精确计算条目大小
        double exactEntrySize = (double)indexSize / 6165;
        System.out.println("精确条目大小: " + String.format("%.6f", exactEntrySize) + " 字节");
        
        // 尝试不同的整数条目大小
        int[] testSizes = {56, 57, 58, 59, 60, 61, 62};
        
        for (int size : testSizes) {
            int maxEntries = (int)(indexSize / size);
            double accuracy = Math.abs(maxEntries - 6165) / 6165.0 * 100;
            
            System.out.printf("条目大小 %d 字节: 最大条目数 %d, 误差 %.2f%%\n", size, maxEntries, accuracy);
            
            if (accuracy < 1.0) {
                System.out.println("  *** 可能是正确的条目大小! ***");
            }
        }
    }
    
    /**
     * 尝试不同的解析策略
     */
    private void tryDifferentParsingStrategies(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 尝试不同的解析策略 ---");
        
        long indexOffset = 0x7354149L;
        
        // 策略1: 使用精确的浮点步长
        System.out.println("策略1: 使用精确浮点步长");
        tryFloatingPointStep(raf, indexOffset);
        
        // 策略2: 混合固定和变长解析
        System.out.println("\n策略2: 混合固定和变长解析");
        tryMixedParsing(raf, indexOffset);
        
        // 策略3: 基于数据验证的自适应解析
        System.out.println("\n策略3: 自适应解析");
        tryAdaptiveParsing(raf, indexOffset);
    }
    
    /**
     * 使用精确浮点步长
     */
    private void tryFloatingPointStep(RandomAccessFile raf, long indexOffset) throws IOException {
        long indexSize = raf.length() - indexOffset;
        double exactStep = (double)indexSize / 6165;
        
        System.out.println("使用精确步长: " + String.format("%.6f", exactStep) + " 字节");
        
        int validEntries = 0;
        int invalidEntries = 0;
        
        for (int i = 0; i < 6165; i++) {
            long entryPos = indexOffset + (long)(i * exactStep);
            
            if (entryPos + 16 > raf.length()) {
                break;
            }
            
            try {
                raf.seek(entryPos);
                long f1 = readInt32LE(raf) & 0xFFFFFFFFL;
                long f2 = readInt32LE(raf) & 0xFFFFFFFFL;
                long f3 = readInt32LE(raf) & 0xFFFFFFFFL;
                long f4 = readInt32LE(raf) & 0xFFFFFFFFL;
                
                // 验证数据合理性
                if (f2 < raf.length() && f3 > 0 && f3 < 100 * 1024 * 1024) {
                    validEntries++;
                    
                    // 显示前几个有效条目
                    if (validEntries <= 5) {
                        System.out.println("  有效条目 " + validEntries + ": 偏移=0x" + Long.toHexString(f2) + ", 大小=" + f3);
                    }
                } else {
                    invalidEntries++;
                    
                    // 显示前几个无效条目
                    if (invalidEntries <= 5) {
                        System.out.println("  无效条目 " + invalidEntries + ": 偏移=0x" + Long.toHexString(f2) + ", 大小=" + f3);
                    }
                }
                
            } catch (Exception e) {
                invalidEntries++;
            }
            
            // 每1000个条目报告一次
            if ((i + 1) % 1000 == 0) {
                System.out.println("  进度: " + (i + 1) + "/6165, 有效: " + validEntries + ", 无效: " + invalidEntries);
            }
        }
        
        System.out.println("浮点步长结果: 有效 " + validEntries + ", 无效 " + invalidEntries);
        System.out.println("有效率: " + (validEntries * 100.0 / (validEntries + invalidEntries)) + "%");
    }
    
    /**
     * 混合固定和变长解析
     */
    private void tryMixedParsing(RandomAccessFile raf, long indexOffset) throws IOException {
        System.out.println("前3083个条目用56字节步长，后续尝试不同策略");
        
        int validEntries = 0;
        
        // 前3083个条目用固定56字节步长
        for (int i = 0; i < 3083; i++) {
            long entryPos = indexOffset + (i * 56);
            
            if (isValidEntry(raf, entryPos)) {
                validEntries++;
            }
        }
        
        System.out.println("前3083个条目: " + validEntries + " 个有效");
        
        // 分析剩余空间
        long remainingStart = indexOffset + (3083 * 56);
        long remainingSize = raf.length() - remainingStart;
        int remainingEntries = 6165 - 3083; // 3082个剩余条目
        
        System.out.println("剩余空间: " + remainingSize + " 字节");
        System.out.println("剩余条目: " + remainingEntries + " 个");
        
        if (remainingSize > 0 && remainingEntries > 0) {
            double avgRemainingSize = (double)remainingSize / remainingEntries;
            System.out.println("剩余条目平均大小: " + String.format("%.2f", avgRemainingSize) + " 字节");
            
            // 尝试用计算出的平均大小解析剩余条目
            int remainingValid = 0;
            for (int i = 0; i < remainingEntries; i++) {
                long entryPos = remainingStart + (long)(i * avgRemainingSize);
                
                if (entryPos + 16 > raf.length()) {
                    break;
                }
                
                if (isValidEntry(raf, entryPos)) {
                    remainingValid++;
                }
            }
            
            System.out.println("剩余条目中有效: " + remainingValid + " 个");
            System.out.println("总有效条目: " + (validEntries + remainingValid) + "/6165");
        }
    }
    
    /**
     * 自适应解析
     */
    private void tryAdaptiveParsing(RandomAccessFile raf, long indexOffset) throws IOException {
        System.out.println("基于数据验证的自适应解析");
        
        List<Long> validPositions = new ArrayList<>();
        long currentPos = indexOffset;
        long endPos = raf.length();
        
        // 扫描整个索引区域，寻找有效的条目位置
        while (currentPos < endPos - 16 && validPositions.size() < 6165) {
            if (isValidEntry(raf, currentPos)) {
                validPositions.add(currentPos);
                
                if (validPositions.size() % 500 == 0) {
                    System.out.println("  找到 " + validPositions.size() + " 个有效条目位置...");
                }
                
                // 尝试不同的步长
                currentPos += 56; // 默认步长
            } else {
                currentPos += 4; // 小步长搜索
            }
        }
        
        System.out.println("自适应解析找到 " + validPositions.size() + " 个有效条目位置");
        
        if (validPositions.size() >= 6000) {
            System.out.println("✓ 接近目标6165个条目!");
            
            // 分析找到的条目位置的间隔
            analyzeEntrySpacing(validPositions);
        }
    }
    
    /**
     * 分析条目间隔
     */
    private void analyzeEntrySpacing(List<Long> positions) {
        System.out.println("\n分析条目间隔:");
        
        List<Long> spacings = new ArrayList<>();
        for (int i = 1; i < Math.min(100, positions.size()); i++) {
            long spacing = positions.get(i) - positions.get(i-1);
            spacings.add(spacing);
        }
        
        // 统计间隔分布
        long sum = spacings.stream().mapToLong(Long::longValue).sum();
        double avgSpacing = (double)sum / spacings.size();
        
        System.out.println("平均间隔: " + String.format("%.2f", avgSpacing) + " 字节");
        
        // 显示前10个间隔
        System.out.println("前10个间隔:");
        for (int i = 0; i < Math.min(10, spacings.size()); i++) {
            System.out.println("  间隔 " + (i+1) + ": " + spacings.get(i) + " 字节");
        }
    }
    
    /**
     * 分析解析限制
     */
    private void analyzeParsingLimitation(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 分析为什么只能解析3083个条目 ---");
        
        long indexOffset = 0x7354149L;
        
        // 检查3083个条目后的数据
        long pos3083 = indexOffset + (3083 * 56);
        System.out.println("第3083个条目后的位置: 0x" + Long.toHexString(pos3083));
        
        raf.seek(pos3083);
        byte[] data = new byte[128];
        raf.read(data);
        
        System.out.println("第3083个条目后的数据:");
        for (int i = 0; i < data.length; i += 16) {
            System.out.printf("0x%08X: ", (int)(pos3083 + i));
            for (int j = 0; j < 16 && i + j < data.length; j++) {
                System.out.printf("%02X ", data[i + j] & 0xFF);
            }
            System.out.print(" | ");
            for (int j = 0; j < 16 && i + j < data.length; j++) {
                char c = (char)(data[i + j] & 0xFF);
                System.out.print(c >= 32 && c < 127 ? c : '.');
            }
            System.out.println();
        }
        
        // 检查数据是否看起来像文本
        String text = new String(data, "UTF-8");
        if (text.contains("png") || text.contains("jpg") || text.contains("bmp")) {
            System.out.println("⚠ 数据包含文件扩展名，可能是文件名表");
        }
    }
    
    /**
     * 寻找剩余条目
     */
    private void findRemainingEntries(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 寻找剩余条目的位置 ---");
        
        // 可能的策略：
        // 1. 文件名表 + 索引表分离
        // 2. 不同的条目结构
        // 3. 压缩的索引数据
        
        System.out.println("可能的剩余条目位置:");
        System.out.println("1. 文件开头到索引之间的区域");
        System.out.println("2. 索引区域的不同解析方式");
        System.out.println("3. 文件末尾的额外索引");
        
        // 检查文件开头是否有额外的索引信息
        checkFileHeader(raf);
    }
    
    /**
     * 检查文件头是否有额外信息
     */
    private void checkFileHeader(RandomAccessFile raf) throws IOException {
        System.out.println("\n检查文件头是否有额外的索引信息:");
        
        raf.seek(0);
        
        // 读取文件头
        String signature = readString(raf, 4);
        int entryCount = readInt32LE(raf);
        int field1 = readInt32LE(raf);
        int field2 = readInt32LE(raf);
        int field3 = readInt32LE(raf);
        long indexOffset = readInt32LE(raf) & 0xFFFFFFFFL;
        
        System.out.println("文件头信息:");
        System.out.println("  签名: " + signature);
        System.out.println("  条目数: " + entryCount);
        System.out.println("  字段1: " + field1);
        System.out.println("  字段2: " + field2);
        System.out.println("  字段3: " + field3);
        System.out.println("  索引偏移: 0x" + Long.toHexString(indexOffset));
        
        if (entryCount == 6165) {
            System.out.println("✓ 文件头确认有6165个条目");
        } else {
            System.out.println("⚠ 文件头条目数与期望不符");
        }
    }
    
    /**
     * 检查条目是否有效
     */
    private boolean isValidEntry(RandomAccessFile raf, long pos) throws IOException {
        if (pos + 16 > raf.length()) {
            return false;
        }
        
        raf.seek(pos + 4); // 跳到偏移字段
        long offset = readInt32LE(raf) & 0xFFFFFFFFL;
        long size = readInt32LE(raf) & 0xFFFFFFFFL;
        
        return offset < raf.length() && size > 0 && size < 100 * 1024 * 1024;
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 读取字符串
     */
    private String readString(RandomAccessFile raf, int length) throws IOException {
        byte[] bytes = new byte[length];
        raf.read(bytes);
        return new String(bytes, "UTF-8");
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        Precise6165EntryAnalyzer analyzer = new Precise6165EntryAnalyzer();
        analyzer.analyzePrecise6165Entries();
    }
}
