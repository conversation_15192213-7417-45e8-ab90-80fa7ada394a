package com.tool.test;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 文件格式测试工具 - 验证0x前缀格式修复
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class FileFormatTest {
    
    public static void main(String[] args) {
        System.out.println("=== 文件格式测试 - 0x前缀修复验证 ===");
        
        // 测试正则表达式
        testRegexPattern();
        
        // 测试文件名格式检查
        testFileNameFormat();
        
        // 测试十六进制ID生成
        testHexIdGeneration();
        
        // 测试XLS ID生成
        testXlsIdGeneration();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试正则表达式
     */
    private static void testRegexPattern() {
        System.out.println("\n--- 测试正则表达式 ---");
        
        Pattern newFormatPattern = Pattern.compile("^0x([0-9A-Fa-f]{8})\\.png$");
        
        String[] testFiles = {
            "0x12345678.png",     // 应该匹配
            "0xABCDEF12.png",     // 应该匹配
            "0xabcdef12.png",     // 应该匹配
            "0x1234567G.png",     // 不应该匹配（包含G）
            "0x123456.png",       // 不应该匹配（长度不够）
            "0x123456789.png",    // 不应该匹配（长度过长）
            "2025-12345678.png",  // 不应该匹配（旧格式）
            "12345678.png",       // 不应该匹配（无前缀）
            "0x12345678.jpg"      // 不应该匹配（扩展名错误）
        };
        
        for (String fileName : testFiles) {
            Matcher matcher = newFormatPattern.matcher(fileName);
            boolean matches = matcher.matches();
            String hexId = matches ? matcher.group(1) : "N/A";
            
            System.out.printf("文件名: %-20s 匹配: %-5s 十六进制ID: %s\n", 
                fileName, matches ? "✅" : "❌", hexId);
        }
    }
    
    /**
     * 测试文件名格式检查
     */
    private static void testFileNameFormat() {
        System.out.println("\n--- 测试文件名格式检查 ---");
        
        String[] testFiles = {
            "0x12345678.png",
            "0xABCDEF12.was",
            "0x00000001.png",
            "0xFFFFFFFF.was",
            "0x1234567.png",      // 长度不够
            "0x123456789.png",    // 长度过长
            "0xGHIJKLMN.png",     // 无效字符
            "weapon_sword.png",   // 旧格式
            "0x12345678.txt"      // 错误扩展名
        };
        
        for (String fileName : testFiles) {
            boolean isValid = checkFileNameFormat(fileName);
            System.out.printf("文件名: %-20s 格式正确: %s\n", 
                fileName, isValid ? "✅" : "❌");
        }
    }
    
    /**
     * 检查文件名格式
     */
    private static boolean checkFileNameFormat(String name) {
        if (name.length() > 11 && name.startsWith("0x")) {
            int dotIndex = name.lastIndexOf('.');
            if (dotIndex > 0) {
                String extension = name.substring(dotIndex);
                if (".png".equals(extension) || ".was".equals(extension)) {
                    String hexPart = name.substring(2, dotIndex);
                    return hexPart.length() == 8 && hexPart.matches("[0-9A-Fa-f]{8}");
                }
            }
        }
        return false;
    }
    
    /**
     * 测试十六进制ID生成
     */
    private static void testHexIdGeneration() {
        System.out.println("\n--- 测试十六进制ID生成 ---");
        
        java.util.Random random = new java.util.Random();
        
        for (int i = 0; i < 10; i++) {
            String hexId = generateRandomHexId(random);
            String fileName = "0x" + hexId + ".png";
            boolean isValid = checkFileNameFormat(fileName);
            
            System.out.printf("生成ID: %s 文件名: %-15s 格式正确: %s\n", 
                hexId, fileName, isValid ? "✅" : "❌");
        }
    }
    
    /**
     * 生成随机十六进制ID
     */
    private static String generateRandomHexId(java.util.Random random) {
        // 生成一个随机的32位整数，确保结果是8位十六进制
        long randomValue = random.nextLong() & 0xFFFFFFFFL; // 确保是正数且在32位范围内
        // 确保生成的十六进制至少以1开头（避免以0开头的值）
        if (randomValue < 0x10000000L) {
            randomValue += 0x10000000L;
        }
        return String.format("%08X", randomValue);
    }
    
    /**
     * 测试XLS ID生成
     */
    private static void testXlsIdGeneration() {
        System.out.println("\n--- 测试XLS ID生成 ---");
        
        String[] testFiles = {
            "0x12345678.png",
            "0xABCDEF12.png",
            "0x00000001.png",
            "weapon_sword.png",   // 无效格式
            "0x1234567.png",      // 长度不够
            "0xGHIJKLMN.png"      // 无效字符
        };
        
        for (String fileName : testFiles) {
            String xlsId = generateXlsId(fileName);
            System.out.printf("文件名: %-20s XLS ID: %s\n", 
                fileName, xlsId.isEmpty() ? "❌ (无效)" : xlsId);
        }
    }
    
    /**
     * 生成XLS需要的ID
     */
    private static String generateXlsId(String fileName) {
        if (fileName.startsWith("0x") && fileName.endsWith(".png")) {
            // 提取十六进制部分并转换为0x格式
            String hexPart = fileName.substring(2, fileName.length() - 4);
            if (hexPart.length() == 8 && hexPart.matches("[0-9A-Fa-f]{8}")) {
                return "0x" + hexPart.toUpperCase();
            }
        }
        return ""; // 非0x格式的文件返回空字符串
    }
    
    /**
     * 测试字符串操作性能
     */
    private static void testStringOperationPerformance() {
        System.out.println("\n--- 测试字符串操作性能 ---");
        
        String testFileName = "0x12345678.png";
        int iterations = 1000000;
        
        // 测试方法1：使用split
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            String baseName = testFileName.substring(0, testFileName.lastIndexOf('.'));
            String[] parts = baseName.split("x");
            if (parts.length >= 2 && parts[0].equals("0") && parts[1].matches("[0-9A-Fa-f]{8}")) {
                // 处理
            }
        }
        long splitTime = System.currentTimeMillis() - startTime;
        
        // 测试方法2：使用substring
        startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            if (testFileName.startsWith("0x") && testFileName.length() >= 14) {
                String hexPart = testFileName.substring(2, testFileName.lastIndexOf('.'));
                if (hexPart.length() == 8 && hexPart.matches("[0-9A-Fa-f]{8}")) {
                    // 处理
                }
            }
        }
        long substringTime = System.currentTimeMillis() - startTime;
        
        System.out.printf("Split方法耗时: %d ms\n", splitTime);
        System.out.printf("Substring方法耗时: %d ms\n", substringTime);
        System.out.printf("性能提升: %.1fx\n", (double)splitTime / substringTime);
    }
}
