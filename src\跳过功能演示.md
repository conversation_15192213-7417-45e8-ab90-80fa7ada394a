# 跳过功能演示

## 功能说明

新版本的皮肤ID同步工具增加了智能跳过功能，会自动跳过在XLS文件中找不到对应ID的文件，避免错误操作。

## 工作流程

### 1. 扫描阶段

工具会扫描所有PNG文件，并尝试在XLS文件中查找对应的条目：

```
正在扫描PNG文件...
找到文件: weapon_sword.png -> 在XLS中找到对应条目
找到文件: armor_001.png -> XLS中未找到对应条目
找到文件: 9134.png -> 在XLS中找到对应条目
找到文件: unknown_item.png -> XLS中未找到对应条目
找到文件: 2025-12345678.png -> 已转换文件
```

### 2. 预览阶段

在预览表格中，用户可以清楚地看到每个文件的处理状态：

| 原文件名 | 新文件名 | 原皮肤ID | 新皮肤ID | 文件存在 | XLS行号 | 状态 |
|---------|---------|---------|---------|---------|---------|------|
| weapon_sword.png | 2025-A1B2C3D4.png | weapon_sword | 0xA1B2C3D4 | 是 | 5 | 准备就绪 |
| armor_001.png | 2025-F5E6D7C8.png | armor_001 | 0xF5E6D7C8 | 是 | 未找到 | **将跳过(XLS中未找到)** |
| 9134.png | 2025-B9A8C7D6.png | 9134 | 0xB9A8C7D6 | 是 | 10 | 准备就绪 |
| unknown_item.png | 2025-E4F3A2B1.png | unknown_item | 0xE4F3A2B1 | 是 | 未找到 | **将跳过(XLS中未找到)** |
| 2025-12345678.png | 2025-12345678.png | old_file | 0x12345678 | 是 | 15 | 已转换 |

### 3. 统计信息

扫描完成后，工具会显示详细的统计信息：

```
扫描完成，总计 5 个文件
  - 准备转换: 2 个
  - 将跳过: 2 个 (XLS中未找到)
  - 已转换: 1 个
```

### 4. 执行阶段

在执行修改时，工具会：

1. **跳过已转换的文件**
   ```
   跳过已转换文件: 2025-12345678.png
   ```

2. **跳过XLS中未找到的文件**
   ```
   跳过XLS中未找到的文件: armor_001.png
   跳过XLS中未找到的文件: unknown_item.png
   ```

3. **处理准备就绪的文件**
   ```
   文件重命名: weapon_sword.png -> 2025-A1B2C3D4.png
   已更新XLS第6行: weapon_sword -> 0xA1B2C3D4
   文件重命名: 9134.png -> 2025-B9A8C7D6.png
   已更新XLS第11行: 9134 -> 0xB9A8C7D6
   ```

### 5. 最终结果

```
修改操作完成！成功: 2, 跳过: 3, 失败: 0
```

## 优势

1. **安全性**：避免处理无效的文件，防止数据不一致
2. **清晰性**：用户可以在执行前清楚地看到哪些文件会被跳过
3. **效率性**：不浪费时间处理无效文件
4. **完整性**：提供详细的统计信息，便于用户了解处理结果

## 使用建议

1. **检查预览**：执行前仔细查看预览表格，确认跳过的文件是否符合预期
2. **完善XLS**：如果发现重要文件被跳过，可以先完善XLS文件中的条目
3. **分批处理**：对于大量文件，可以分批处理，先处理有XLS条目的文件
4. **备份数据**：虽然有跳过功能保护，但仍建议在操作前备份重要数据

## 注意事项

- 跳过的文件不会被重命名，也不会修改XLS文件
- 跳过的文件在下次扫描时仍会被检测到
- 如果需要处理被跳过的文件，需要先在XLS中添加对应的条目
