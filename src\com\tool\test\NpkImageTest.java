package com.tool.test;

import com.tool.npk.NpkImageUtils;
import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * NPK ImageIcon使用示例和测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkImageTest extends JFrame {
    
    private JTextField skinField;
    private JTextField widthField;
    private JTextField heightField;
    private JComboBox<String> npkFileCombo;
    private JLabel imageLabel;
    private JTextArea logArea;
    
    public NpkImageTest() {
        initializeUI();
    }
    
    private void initializeUI() {
        setTitle("NPK ImageIcon 测试工具");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // 创建输入面板
        JPanel inputPanel = createInputPanel();
        add(inputPanel, BorderLayout.NORTH);
        
        // 创建图像显示面板
        JPanel imagePanel = createImagePanel();
        add(imagePanel, BorderLayout.CENTER);
        
        // 创建日志面板
        JPanel logPanel = createLogPanel();
        add(logPanel, BorderLayout.SOUTH);
        
        pack();
        setLocationRelativeTo(null);
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder("NPK图像读取测试"));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // 皮肤名称输入
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("皮肤名称:"), gbc);
        
        skinField = new JTextField("04b4a97c", 15);
        gbc.gridx = 1; gbc.gridy = 0; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(skinField, gbc);
        
        // 宽度输入
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("宽度:"), gbc);
        
        widthField = new JTextField("64", 10);
        gbc.gridx = 1; gbc.gridy = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(widthField, gbc);
        
        // 高度输入
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("高度:"), gbc);
        
        heightField = new JTextField("64", 10);
        gbc.gridx = 1; gbc.gridy = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(heightField, gbc);
        
        // NPK文件选择
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("NPK文件:"), gbc);
        
        npkFileCombo = new JComboBox<>(new String[]{
            "item.npk", "items.npk", "weapon.npk", "equip.npk", 
            "character.npk", "skill.npk"
        });
        gbc.gridx = 1; gbc.gridy = 3; gbc.fill = GridBagConstraints.HORIZONTAL;
        panel.add(npkFileCombo, gbc);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        
        JButton loadButton = new JButton("加载图像");
        loadButton.addActionListener(new LoadImageListener());
        buttonPanel.add(loadButton);
        
        JButton clearButton = new JButton("清空");
        clearButton.addActionListener(e -> {
            imageLabel.setIcon(null);
            imageLabel.setText("图像显示区域");
            logArea.setText("");
        });
        buttonPanel.add(clearButton);
        
        JButton testButton = new JButton("批量测试");
        testButton.addActionListener(new BatchTestListener());
        buttonPanel.add(testButton);
        
        gbc.gridx = 0; gbc.gridy = 4; gbc.gridwidth = 2;
        panel.add(buttonPanel, gbc);
        
        return panel;
    }
    
    private JPanel createImagePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("图像显示"));
        
        imageLabel = new JLabel("图像显示区域", SwingConstants.CENTER);
        imageLabel.setPreferredSize(new Dimension(300, 300));
        imageLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        
        panel.add(imageLabel, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("日志信息"));
        
        logArea = new JTextArea(8, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(logArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private class LoadImageListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                String skin = skinField.getText().trim();
                int width = Integer.parseInt(widthField.getText().trim());
                int height = Integer.parseInt(heightField.getText().trim());
                String npkFile = (String) npkFileCombo.getSelectedItem();
                
                logArea.append("开始加载图像...\n");
                logArea.append("皮肤: " + skin + "\n");
                logArea.append("尺寸: " + width + "x" + height + "\n");
                logArea.append("NPK文件: " + npkFile + "\n");
                
                long startTime = System.currentTimeMillis();
                
                // 使用NPK ImageIcon工具加载图像
                ImageIcon icon = NpkImageUtils.getNpkPng(skin, width, height, npkFile);
                
                long endTime = System.currentTimeMillis();
                
                if (icon != null) {
                    imageLabel.setIcon(icon);
                    imageLabel.setText("");
                    logArea.append("✓ 图像加载成功! 耗时: " + (endTime - startTime) + "ms\n");
                    logArea.append("图像尺寸: " + icon.getIconWidth() + "x" + icon.getIconHeight() + "\n");
                } else {
                    imageLabel.setIcon(null);
                    imageLabel.setText("图像加载失败");
                    logArea.append("✗ 图像加载失败!\n");
                }
                
                logArea.append("缓存信息: " + NpkImageUtils.getCacheInfo() + "\n");
                logArea.append("---\n");
                
            } catch (Exception ex) {
                logArea.append("错误: " + ex.getMessage() + "\n");
                ex.printStackTrace();
            }
        }
    }
    
    private class BatchTestListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            logArea.append("开始批量测试...\n");
            
            // 测试不同的皮肤名称
            String[] testSkins = {
                "04b4a97c", "02ac48b8", "0714e743", "030a0ac0", "0421106c"
            };
            
            String npkFile = (String) npkFileCombo.getSelectedItem();
            int successCount = 0;
            int totalCount = testSkins.length;
            
            long totalStartTime = System.currentTimeMillis();
            
            for (String skin : testSkins) {
                try {
                    long startTime = System.currentTimeMillis();
                    ImageIcon icon = NpkImageUtils.getNpkPng(skin, 32, 32, npkFile);
                    long endTime = System.currentTimeMillis();
                    
                    if (icon != null) {
                        successCount++;
                        logArea.append("✓ " + skin + " 加载成功 (" + (endTime - startTime) + "ms)\n");
                    } else {
                        logArea.append("✗ " + skin + " 加载失败\n");
                    }
                    
                } catch (Exception ex) {
                    logArea.append("✗ " + skin + " 异常: " + ex.getMessage() + "\n");
                }
            }
            
            long totalEndTime = System.currentTimeMillis();
            
            logArea.append("批量测试完成!\n");
            logArea.append("成功: " + successCount + "/" + totalCount + "\n");
            logArea.append("总耗时: " + (totalEndTime - totalStartTime) + "ms\n");
            logArea.append("平均耗时: " + ((totalEndTime - totalStartTime) / totalCount) + "ms\n");
            logArea.append("缓存信息: " + NpkImageUtils.getCacheInfo() + "\n");
            logArea.append("===\n");
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            new NpkImageTest().setVisible(true);
        });
    }
}
