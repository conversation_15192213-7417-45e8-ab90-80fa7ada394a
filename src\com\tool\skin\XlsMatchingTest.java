package com.tool.skin;

/**
 * 测试XLS匹配逻辑
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class XlsMatchingTest {
    
    /**
     * 模拟XLS数据
     */
    private String[][] createMockXlsData() {
        return new String[][] {
            {"header1", "header2", "header3"},
            {"item1", "data1", "6123"},           // 数字格式
            {"item2", "data2", "0x17EB"},         // 十六进制格式 (6123的十六进制)
            {"item3", "data3", "weapon_sword"},   // 文字格式
            {"item4", "data4", "9134"},           // 数字格式
            {"item5", "data5", "0x23AE"},         // 十六进制格式 (9134的十六进制)
            {"item6", "data6", "armor_001"},      // 文字格式
            {"item7", "data7", "255"},            // 数字格式
            {"item8", "data8", "0xFF"},           // 十六进制格式 (255的十六进制)
        };
    }
    
    /**
     * 模拟查找XLS行的方法
     */
    private int findXlsRowByFileName(String originalFileName, String[][] xlsData) {
        if (xlsData == null) return -1;
        
        // 尝试多种匹配方式
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                String xlsValue = xlsData[i][2]; // 第3列
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim(); // 去除空格
                    
                    // 1. 直接匹配文件名
                    if (xlsValue.equals(originalFileName)) {
                        return i;
                    }
                    
                    // 2. 如果原文件名是纯数字，尝试与XLS中的数字匹配
                    if (originalFileName.matches("\\d+")) {
                        try {
                            // 2.1 XLS中也是数字，直接比较
                            if (xlsValue.matches("\\d+") && originalFileName.equals(xlsValue)) {
                                return i;
                            }
                            
                            // 2.2 XLS中是十六进制格式，转换后比较
                            if (xlsValue.startsWith("0x") || xlsValue.startsWith("0X")) {
                                String hexPart = xlsValue.substring(2);
                                long decimalValue = Long.parseLong(hexPart, 16);
                                if (originalFileName.equals(String.valueOf(decimalValue))) {
                                    return i;
                                }
                            }
                        } catch (Exception e) {
                            // 忽略转换错误
                        }
                    }
                    
                    // 3. 如果XLS中是数字，原文件名也尝试作为数字匹配
                    try {
                        if (xlsValue.matches("\\d+") && originalFileName.equals(xlsValue)) {
                            return i;
                        }
                    } catch (Exception e) {
                        // 忽略转换错误
                    }
                    
                    // 4. 如果XLS中是十六进制格式，尝试转换匹配
                    try {
                        if (xlsValue.startsWith("0x") || xlsValue.startsWith("0X")) {
                            String hexPart = xlsValue.substring(2);
                            long decimalValue = Long.parseLong(hexPart, 16);
                            if (originalFileName.equals(String.valueOf(decimalValue))) {
                                return i;
                            }
                        }
                    } catch (Exception e) {
                        // 忽略转换错误
                    }
                }
            }
        }
        return -1;
    }
    
    /**
     * 测试XLS匹配逻辑
     */
    public void testXlsMatching() {
        System.out.println("=== XLS匹配逻辑测试 ===");
        
        String[][] xlsData = createMockXlsData();
        
        // 显示XLS数据
        System.out.println("模拟XLS数据:");
        for (int i = 0; i < xlsData.length; i++) {
            System.out.printf("行%d: %s | %s | %s%n", 
                i, xlsData[i][0], xlsData[i][1], xlsData[i][2]);
        }
        
        System.out.println("\n=== 匹配测试 ===");
        
        // 测试用例
        String[] testFileNames = {
            "6123",           // 应该匹配行1 (数字格式) 和行2 (十六进制格式)
            "9134",           // 应该匹配行4 (数字格式) 和行5 (十六进制格式)
            "255",            // 应该匹配行7 (数字格式) 和行8 (十六进制格式)
            "weapon_sword",   // 应该匹配行3 (文字格式)
            "armor_001",      // 应该匹配行6 (文字格式)
            "unknown_item",   // 应该不匹配任何行
            "12345"           // 应该不匹配任何行
        };
        
        for (String fileName : testFileNames) {
            int foundRow = findXlsRowByFileName(fileName, xlsData);
            if (foundRow >= 0) {
                System.out.printf("文件名: %-15s -> 找到匹配: 行%d (%s)%n", 
                    fileName, foundRow, xlsData[foundRow][2]);
            } else {
                System.out.printf("文件名: %-15s -> 未找到匹配%n", fileName);
            }
        }
        
        System.out.println("\n=== 特殊测试：数字转换 ===");
        
        // 测试数字转换
        System.out.println("6123 (十进制) = " + Integer.toHexString(6123).toUpperCase() + " (十六进制)");
        System.out.println("9134 (十进制) = " + Integer.toHexString(9134).toUpperCase() + " (十六进制)");
        System.out.println("255 (十进制) = " + Integer.toHexString(255).toUpperCase() + " (十六进制)");
        
        // 反向测试
        System.out.println("0x17EB (十六进制) = " + Long.parseLong("17EB", 16) + " (十进制)");
        System.out.println("0x23AE (十六进制) = " + Long.parseLong("23AE", 16) + " (十进制)");
        System.out.println("0xFF (十六进制) = " + Long.parseLong("FF", 16) + " (十进制)");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        XlsMatchingTest test = new XlsMatchingTest();
        test.testXlsMatching();
    }
}
