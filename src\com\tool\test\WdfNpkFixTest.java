package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * WDF和NPK修复功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class WdfNpkFixTest {
    
    private static final String TEST_NPK = "test_nxpk.npk";
    
    /**
     * 创建模拟的NXPK文件用于测试
     */
    public void createMockNxpkFile() {
        System.out.println("=== 创建模拟NXPK文件 ===");
        
        try {
            File testFile = new File(TEST_NPK);
            
            try (RandomAccessFile raf = new RandomAccessFile(testFile, "rw")) {
                // 写入NXPK文件头
                raf.write("NXPK".getBytes()); // 魔数头
                writeInt32LE(raf, 3); // 条目数量
                writeInt32LE(raf, 0); // 未知变量
                writeInt32LE(raf, 0); // 加密模式
                writeInt32LE(raf, 0); // 哈希模式
                writeInt32LE(raf, 64); // 索引偏移 (32位)
                writeInt32LE(raf, 0);  // 填充到64位
                
                // 填充到索引位置
                while (raf.getFilePointer() < 64) {
                    raf.write(0);
                }
                
                // 写入文件索引
                for (int i = 0; i < 3; i++) {
                    String fileName = "test_file_" + i + ".png";
                    byte[] nameBytes = fileName.getBytes("UTF-8");
                    
                    // 文件名长度
                    writeInt32LE(raf, nameBytes.length);
                    // 文件名
                    raf.write(nameBytes);
                    
                    // 文件信息
                    writeInt64LE(raf, 1000 + i * 100); // 偏移
                    writeInt64LE(raf, 50 + i * 10);    // 压缩大小
                    writeInt64LE(raf, 100 + i * 20);   // 原始大小
                    writeInt32LE(raf, 1);              // 压缩类型
                    writeInt32LE(raf, 0x12345678 + i); // CRC32
                }
            }
            
            System.out.println("创建模拟NXPK文件: " + TEST_NPK);
            System.out.println("文件大小: " + testFile.length() + " 字节");
            
        } catch (IOException e) {
            System.out.println("创建模拟NXPK文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试NXPK文件解析
     */
    public void testNxpkParsing() {
        System.out.println("\n=== 测试NXPK文件解析 ===");
        
        File npkFile = new File(TEST_NPK);
        if (!npkFile.exists()) {
            System.out.println("NXPK文件不存在，请先创建测试文件");
            return;
        }
        
        try {
            System.out.println("开始解析NXPK文件...");
            
            NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                System.out.println("进度 " + progress + "%: " + status);
            });
            
            System.out.println("NXPK文件解析成功！");
            
        } catch (Exception e) {
            System.out.println("NXPK文件解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WDF固定输出目录
     */
    public void testWdfFixedOutputDir() {
        System.out.println("\n=== 测试WDF固定输出目录 ===");
        
        String fixedOutputDir = "G:\\JXy2o\\GameClient3.0\\res";
        System.out.println("固定输出目录: " + fixedOutputDir);
        
        // 检查目录是否存在
        File outputDir = new File(fixedOutputDir);
        if (!outputDir.exists()) {
            System.out.println("输出目录不存在，尝试创建...");
            if (outputDir.mkdirs()) {
                System.out.println("输出目录创建成功");
            } else {
                System.out.println("输出目录创建失败");
                return;
            }
        } else {
            System.out.println("输出目录已存在");
        }
        
        // 测试自动生成文件名
        String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new java.util.Date());
        String autoFileName = "item_" + timestamp + ".wdf";
        File autoFile = new File(outputDir, autoFileName);
        
        System.out.println("自动生成的文件名: " + autoFileName);
        System.out.println("完整路径: " + autoFile.getAbsolutePath());
        
        // 测试文件创建
        try {
            if (autoFile.createNewFile()) {
                System.out.println("测试文件创建成功");
                autoFile.delete(); // 删除测试文件
                System.out.println("测试文件已删除");
            }
        } catch (IOException e) {
            System.out.println("测试文件创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试NPK文件头验证
     */
    public void testNpkHeaderValidation() {
        System.out.println("\n=== 测试NPK文件头验证 ===");
        
        // 测试正确的NXPK头
        System.out.println("测试正确的NXPK文件头...");
        testNxpkParsing();
        
        // 创建错误的文件头进行测试
        System.out.println("\n测试错误的文件头...");
        try {
            File wrongFile = new File("wrong_header.npk");
            try (RandomAccessFile raf = new RandomAccessFile(wrongFile, "rw")) {
                raf.write("WRONG".getBytes()); // 错误的魔数头
                writeInt32LE(raf, 1);
            }
            
            try {
                NpkTool.parseNpkFile(wrongFile, null);
                System.out.println("错误：应该抛出异常但没有");
            } catch (IOException e) {
                System.out.println("正确：检测到错误的文件头 - " + e.getMessage());
            }
            
            wrongFile.delete();
            
        } catch (IOException e) {
            System.out.println("测试错误文件头时发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 写入32位小端序整数
     */
    private void writeInt32LE(RandomAccessFile raf, int value) throws IOException {
        raf.write(value & 0xFF);
        raf.write((value >> 8) & 0xFF);
        raf.write((value >> 16) & 0xFF);
        raf.write((value >> 24) & 0xFF);
    }
    
    /**
     * 写入64位小端序整数
     */
    private void writeInt64LE(RandomAccessFile raf, long value) throws IOException {
        raf.write((int)(value & 0xFF));
        raf.write((int)((value >> 8) & 0xFF));
        raf.write((int)((value >> 16) & 0xFF));
        raf.write((int)((value >> 24) & 0xFF));
        raf.write((int)((value >> 32) & 0xFF));
        raf.write((int)((value >> 40) & 0xFF));
        raf.write((int)((value >> 48) & 0xFF));
        raf.write((int)((value >> 56) & 0xFF));
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        String[] testFiles = {TEST_NPK, "wrong_header.npk"};
        for (String fileName : testFiles) {
            File file = new File(fileName);
            if (file.exists()) {
                if (file.delete()) {
                    System.out.println("删除: " + fileName);
                } else {
                    System.out.println("删除失败: " + fileName);
                }
            }
        }
        
        System.out.println("测试文件清理完成");
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        System.out.println("=== WDF和NPK修复功能测试 ===");
        
        createMockNxpkFile();
        testNxpkParsing();
        testWdfFixedOutputDir();
        testNpkHeaderValidation();
        
        System.out.println("\n=== 修复功能测试完成 ===");
        System.out.println("主要修复内容:");
        System.out.println("1. WDF输出目录固定为: G:\\JXy2o\\GameClient3.0\\res");
        System.out.println("2. NPK文件魔数头修正为: NXPK");
        System.out.println("3. NPK文件索引偏移负数问题修复");
        System.out.println("4. NPK文件条目读取边界检查");
        System.out.println("\n如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        WdfNpkFixTest test = new WdfNpkFixTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllTests();
        }
    }
}
