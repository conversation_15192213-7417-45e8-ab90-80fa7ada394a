package com.tool.test;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * 内存优化测试工具
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class MemoryOptimizationTest {
    
    private static final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    
    public static void main(String[] args) {
        System.out.println("=== 内存优化测试 ===");
        
        // 显示初始内存状态
        printMemoryUsage("程序启动");
        
        // 测试文件扫描内存使用
        testFileScanMemory();
        
        // 测试大量对象创建
        testObjectCreation();
        
        // 测试垃圾回收效果
        testGarbageCollection();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试文件扫描的内存使用
     */
    private static void testFileScanMemory() {
        System.out.println("\n--- 文件扫描内存测试 ---");
        
        printMemoryUsage("扫描前");
        
        // 模拟扫描大量文件
        java.util.List<String> fileList = new java.util.ArrayList<>(10000);
        for (int i = 0; i < 10000; i++) {
            fileList.add("2025-" + String.format("%08X", i) + ".png");
        }
        
        printMemoryUsage("创建10000个文件名");
        
        // 清理
        fileList.clear();
        fileList = null;
        System.gc();
        
        printMemoryUsage("清理后");
    }
    
    /**
     * 测试对象创建的内存使用
     */
    private static void testObjectCreation() {
        System.out.println("\n--- 对象创建内存测试 ---");
        
        printMemoryUsage("对象创建前");
        
        // 模拟创建大量WasData对象
        java.util.List<Object> objectList = new java.util.ArrayList<>(5000);
        for (int i = 0; i < 5000; i++) {
            // 模拟WasData对象
            java.util.Map<String, Object> wasData = new java.util.HashMap<>();
            wasData.put("id", (long)i);
            wasData.put("fileSize", 1024L * i);
            wasData.put("fileSpace", 1024L * i);
            objectList.add(wasData);
        }
        
        printMemoryUsage("创建5000个对象");
        
        // 清理
        objectList.clear();
        objectList = null;
        System.gc();
        
        printMemoryUsage("清理后");
    }
    
    /**
     * 测试垃圾回收效果
     */
    private static void testGarbageCollection() {
        System.out.println("\n--- 垃圾回收测试 ---");
        
        printMemoryUsage("GC测试前");
        
        // 创建大量临时对象
        for (int i = 0; i < 1000; i++) {
            java.util.List<String> tempList = new java.util.ArrayList<>(1000);
            for (int j = 0; j < 1000; j++) {
                tempList.add("临时字符串_" + i + "_" + j);
            }
            // 不保存引用，让对象变成垃圾
        }
        
        printMemoryUsage("创建大量临时对象后");
        
        // 强制垃圾回收
        System.gc();
        try {
            Thread.sleep(100); // 等待GC完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        printMemoryUsage("强制GC后");
    }
    
    /**
     * 打印内存使用情况
     */
    private static void printMemoryUsage(String operation) {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        long heapUsed = heapUsage.getUsed();
        long heapMax = heapUsage.getMax();
        long heapCommitted = heapUsage.getCommitted();
        
        double usedMB = heapUsed / 1024.0 / 1024.0;
        double maxMB = heapMax / 1024.0 / 1024.0;
        double committedMB = heapCommitted / 1024.0 / 1024.0;
        double usagePercent = (heapUsed * 100.0) / heapMax;
        
        System.out.printf("%s:\n", operation);
        System.out.printf("  堆内存: %.1fMB / %.1fMB (%.1f%%) [已分配: %.1fMB]\n", 
            usedMB, maxMB, usagePercent, committedMB);
        System.out.printf("  非堆内存: %.1fMB\n", 
            nonHeapUsage.getUsed() / 1024.0 / 1024.0);
        
        // 内存使用警告
        if (usagePercent > 80) {
            System.out.println("  ⚠️  内存使用率较高！");
        } else if (usagePercent > 60) {
            System.out.println("  ⚠️  内存使用率中等");
        } else {
            System.out.println("  ✅ 内存使用率正常");
        }
        
        System.out.println();
    }
    
    /**
     * 获取可读的文件大小
     */
    private static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / 1024.0 / 1024.0);
        } else {
            return String.format("%.1f GB", size / 1024.0 / 1024.0 / 1024.0);
        }
    }
    
    /**
     * 模拟文件扫描操作
     */
    public static void simulateFileScan(String directory) {
        System.out.println("模拟扫描目录: " + directory);
        printMemoryUsage("扫描开始");
        
        File dir = new File(directory);
        if (!dir.exists() || !dir.isDirectory()) {
            System.out.println("目录不存在或不是目录");
            return;
        }
        
        // 递归扫描文件
        scanDirectory(dir, 0);
        
        printMemoryUsage("扫描完成");
    }
    
    /**
     * 递归扫描目录
     */
    private static void scanDirectory(File dir, int depth) {
        if (depth > 3) return; // 限制递归深度
        
        File[] files = dir.listFiles();
        if (files == null) return;
        
        for (File file : files) {
            if (file.isDirectory()) {
                scanDirectory(file, depth + 1);
            } else if (file.getName().toLowerCase().endsWith(".png")) {
                // 模拟处理PNG文件
                String fileName = file.getName();
                long fileSize = file.length();
                // 这里可以添加更多处理逻辑
            }
        }
    }
}
