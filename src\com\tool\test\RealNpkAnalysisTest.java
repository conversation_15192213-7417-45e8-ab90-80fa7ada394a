package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.List;

/**
 * 真实NPK文件分析测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class RealNpkAnalysisTest {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 分析真实的item.npk文件
     */
    public void analyzeRealNpkFile() {
        System.out.println("=== 分析真实的item.npk文件 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在于根目录");
            return;
        }
        
        System.out.println("文件路径: " + npkFile.getAbsolutePath());
        System.out.println("文件大小: " + formatFileSize(npkFile.length()));
        
        try {
            // 分析文件头
            analyzeFileHeader(npkFile);
            
            // 尝试解析NPK文件
            System.out.println("\n--- 尝试解析NPK文件 ---");
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                System.out.println("解析进度 " + progress + "%: " + status);
            });
            
            if (parsedFile != null) {
                analyzeNpkStructure(parsedFile);
            } else {
                System.out.println("NPK文件解析失败");
            }
            
        } catch (Exception e) {
            System.out.println("分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析文件头
     */
    private void analyzeFileHeader(File npkFile) throws IOException {
        System.out.println("\n--- 分析文件头 ---");
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            // 读取前64字节进行分析
            byte[] header = new byte[64];
            raf.read(header);
            
            // 分析魔数头
            String signature = new String(header, 0, 4);
            System.out.println("文件签名: " + signature + " (期望: NXPK)");
            
            // 显示十六进制头部
            System.out.println("文件头十六进制:");
            for (int i = 0; i < Math.min(32, header.length); i += 16) {
                System.out.printf("%08X: ", i);
                for (int j = 0; j < 16 && i + j < header.length; j++) {
                    System.out.printf("%02X ", header[i + j] & 0xFF);
                }
                System.out.println();
            }
            
            // 尝试读取基本信息
            raf.seek(4);
            int entryCount = readInt32LE(raf);
            int unknownVar = readInt32LE(raf);
            int encryptionMode = readInt32LE(raf);
            int hashMode = readInt32LE(raf);
            long indexOffset = readInt64LE(raf);
            
            System.out.println("\n基本信息:");
            System.out.println("条目数量: " + entryCount);
            System.out.println("未知变量: " + unknownVar);
            System.out.println("加密模式: " + encryptionMode);
            System.out.println("哈希模式: " + hashMode);
            System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset) + " (" + indexOffset + ")");
            
            // 验证索引偏移的合理性
            long fileSize = raf.length();
            if (indexOffset > 0 && indexOffset < fileSize) {
                System.out.println("索引偏移看起来合理");
                
                // 尝试读取索引区域的前几个字节
                raf.seek(indexOffset);
                byte[] indexSample = new byte[32];
                raf.read(indexSample);
                
                System.out.println("索引区域样本:");
                for (int i = 0; i < indexSample.length; i += 16) {
                    System.out.printf("%08X: ", (int)(indexOffset + i));
                    for (int j = 0; j < 16 && i + j < indexSample.length; j++) {
                        System.out.printf("%02X ", indexSample[i + j] & 0xFF);
                    }
                    System.out.println();
                }
            } else {
                System.out.println("警告: 索引偏移可能不正确");
            }
        }
    }
    
    /**
     * 分析NPK结构
     */
    private void analyzeNpkStructure(NpkFile npkFile) {
        System.out.println("\n--- 分析NPK结构 ---");
        
        NpkHeader header = npkFile.getHeader();
        List<NpkEntry> entries = npkFile.getEntries();
        
        System.out.println("NPK头信息:");
        System.out.println("  签名: " + header.getSignature());
        System.out.println("  条目数量: " + header.getEntryCount());
        System.out.println("  索引偏移: 0x" + Long.toHexString(header.getIndexOffset()));
        
        System.out.println("\n文件条目分析:");
        System.out.println("  解析到的条目数: " + entries.size());
        System.out.println("  期望的条目数: " + header.getEntryCount());
        
        if (!entries.isEmpty()) {
            // 分析文件类型分布
            int pngCount = 0;
            int otherCount = 0;
            long totalSize = 0;
            
            for (NpkEntry entry : entries) {
                String fileName = entry.getFileName();
                if (fileName != null && fileName.toLowerCase().endsWith(".png")) {
                    pngCount++;
                } else {
                    otherCount++;
                }
                totalSize += entry.getOriginalSize();
            }
            
            System.out.println("\n文件类型分布:");
            System.out.println("  PNG文件: " + pngCount + " 个");
            System.out.println("  其他文件: " + otherCount + " 个");
            System.out.println("  总大小: " + formatFileSize(totalSize));
            
            // 显示前几个条目的详细信息
            System.out.println("\n前10个文件条目:");
            for (int i = 0; i < Math.min(10, entries.size()); i++) {
                NpkEntry entry = entries.get(i);
                System.out.println("  " + (i + 1) + ". " + entry.getFileName());
                System.out.println("     偏移: 0x" + Long.toHexString(entry.getOffset()));
                System.out.println("     大小: " + entry.getOriginalSize() + " 字节");
                System.out.println("     压缩: " + (entry.isCompressed() ? "是" : "否"));
            }
        }
    }
    
    /**
     * 测试PNG文件识别准确性
     */
    public void testPngIdentificationAccuracy() {
        System.out.println("\n=== 测试PNG文件识别准确性 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try {
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            if (parsedFile == null || parsedFile.getEntries().isEmpty()) {
                System.out.println("NPK文件解析失败");
                return;
            }
            
            List<NpkEntry> allEntries = parsedFile.getEntries();
            List<NpkEntry> pngEntries = NpkTool.filterPngFiles(allEntries);
            
            System.out.println("文件统计:");
            System.out.println("  总文件数: " + allEntries.size());
            System.out.println("  识别的PNG文件: " + pngEntries.size());
            System.out.println("  PNG比例: " + String.format("%.1f%%", pngEntries.size() * 100.0 / allEntries.size()));
            
            // 如果用户说6165个都是PNG文件，那我们的识别可能有问题
            if (allEntries.size() == 6165) {
                System.out.println("\n注意: 用户反馈所有6165个文件都是PNG文件");
                System.out.println("当前识别到的PNG文件数: " + pngEntries.size());
                System.out.println("可能的问题:");
                System.out.println("1. 文件名解析不正确");
                System.out.println("2. PNG文件过滤逻辑有误");
                System.out.println("3. NPK索引结构解析错误");
                
                // 分析文件名模式
                analyzeFileNamePatterns(allEntries);
            }
            
        } catch (Exception e) {
            System.out.println("PNG识别测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析文件名模式
     */
    private void analyzeFileNamePatterns(List<NpkEntry> entries) {
        System.out.println("\n--- 分析文件名模式 ---");
        
        int validNames = 0;
        int pngNames = 0;
        int emptyNames = 0;
        int generatedNames = 0;
        
        for (NpkEntry entry : entries) {
            String fileName = entry.getFileName();
            if (fileName == null || fileName.isEmpty()) {
                emptyNames++;
            } else if (fileName.startsWith("file_") || fileName.startsWith("image_")) {
                generatedNames++;
                if (fileName.endsWith(".png")) {
                    pngNames++;
                }
            } else {
                validNames++;
                if (fileName.toLowerCase().endsWith(".png")) {
                    pngNames++;
                }
            }
        }
        
        System.out.println("文件名统计:");
        System.out.println("  有效文件名: " + validNames);
        System.out.println("  生成的文件名: " + generatedNames);
        System.out.println("  空文件名: " + emptyNames);
        System.out.println("  PNG文件名: " + pngNames);
        
        if (generatedNames > validNames) {
            System.out.println("\n警告: 大部分文件名是生成的，说明NPK索引解析可能有问题");
        }
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 读取64位小端序整数
     */
    private long readInt64LE(RandomAccessFile raf) throws IOException {
        long low = readInt32LE(raf) & 0xFFFFFFFFL;
        long high = readInt32LE(raf) & 0xFFFFFFFFL;
        return low | (high << 32);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 运行所有分析测试
     */
    public void runAllAnalysisTests() {
        System.out.println("=== 真实NPK文件分析测试 ===");
        System.out.println("目标: 分析根目录下的item.npk文件");
        System.out.println("问题: 6165个文件都是PNG，但只识别到2044个");
        
        analyzeRealNpkFile();
        testPngIdentificationAccuracy();
        
        System.out.println("\n=== 分析完成 ===");
        System.out.println("下一步: 根据分析结果改进NPK解析逻辑");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        RealNpkAnalysisTest test = new RealNpkAnalysisTest();
        test.runAllAnalysisTests();
    }
}
