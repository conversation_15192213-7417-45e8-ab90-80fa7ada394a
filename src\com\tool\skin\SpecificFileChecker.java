package com.tool.skin;

import xsl.ReadExelTool;

/**
 * 检查特定文件在XLS中的存在情况
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SpecificFileChecker {
    
    private static final String DEFAULT_XLS_PATH = "G:\\JXy2o\\GameClient3.0\\res\\config\\item.xls";
    
    /**
     * 检查特定数字在XLS中的所有可能格式
     */
    public void checkSpecificNumber(String xlsPath, long number) {
        System.out.println("=== 检查数字 " + number + " 在XLS中的存在情况 ===");
        
        try {
            String[][] xlsData = ReadExelTool.getResult(xlsPath);
            if (xlsData == null) {
                System.out.println("错误: 无法读取XLS文件");
                return;
            }
            
            String numberStr = String.valueOf(number);
            String hexStr = "0x" + Long.toHexString(number).toUpperCase();
            String hexStrLower = "0x" + Long.toHexString(number).toLowerCase();
            
            System.out.println("查找格式:");
            System.out.println("  十进制: " + numberStr);
            System.out.println("  十六进制(大写): " + hexStr);
            System.out.println("  十六进制(小写): " + hexStrLower);
            
            boolean found = false;
            
            for (int i = 0; i < xlsData.length; i++) {
                if (xlsData[i] != null && xlsData[i].length > 2) {
                    String xlsValue = xlsData[i][2];
                    if (xlsValue != null) {
                        xlsValue = xlsValue.trim();
                        
                        if (xlsValue.equals(numberStr)) {
                            System.out.println("找到十进制匹配: 行" + (i + 1) + ", 值: " + xlsValue);
                            found = true;
                        } else if (xlsValue.equalsIgnoreCase(hexStr)) {
                            System.out.println("找到十六进制匹配: 行" + (i + 1) + ", 值: " + xlsValue);
                            found = true;
                        }
                    }
                }
            }
            
            if (!found) {
                System.out.println("未找到任何匹配");
                
                // 查找相近的值
                System.out.println("\n查找相近的值:");
                for (int i = 0; i < xlsData.length; i++) {
                    if (xlsData[i] != null && xlsData[i].length > 2) {
                        String xlsValue = xlsData[i][2];
                        if (xlsValue != null) {
                            xlsValue = xlsValue.trim();
                            
                            // 检查是否包含目标数字
                            if (xlsValue.contains(numberStr)) {
                                System.out.println("包含目标数字的值: 行" + (i + 1) + ", 值: " + xlsValue);
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("检查时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 显示XLS中第3列的一些样本数据
     */
    public void showSampleData(String xlsPath, int startRow, int count) {
        System.out.println("\n=== XLS第3列样本数据 (从行" + startRow + "开始，显示" + count + "行) ===");
        
        try {
            String[][] xlsData = ReadExelTool.getResult(xlsPath);
            if (xlsData == null) {
                System.out.println("错误: 无法读取XLS文件");
                return;
            }
            
            for (int i = startRow - 1; i < Math.min(startRow - 1 + count, xlsData.length); i++) {
                if (xlsData[i] != null && xlsData[i].length > 2) {
                    String value = xlsData[i][2];
                    System.out.printf("行%d: %s%n", i + 1, value);
                } else {
                    System.out.printf("行%d: (空或列数不足)%n", i + 1);
                }
            }
            
        } catch (Exception e) {
            System.out.println("显示样本数据时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        SpecificFileChecker checker = new SpecificFileChecker();
        
        String xlsPath = DEFAULT_XLS_PATH;
        if (args.length >= 1) {
            xlsPath = args[0];
        }
        
        // 检查几个特定的数字
        long[] testNumbers = {6123, 9134, 255, 1000, 100001};
        
        for (long number : testNumbers) {
            checker.checkSpecificNumber(xlsPath, number);
            System.out.println();
        }
        
        // 显示一些样本数据
        checker.showSampleData(xlsPath, 1200, 20);  // 6123附近的数据
        checker.showSampleData(xlsPath, 1, 20);     // 开头的数据
    }
}
