package com.tool.skin;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 测试ID映射功能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class IdMappingTest {
    
    private Random random = new Random();
    private Map<String, String> idMappingCache = new HashMap<>();
    
    /**
     * 生成随机的8位十六进制ID
     */
    private String generateRandomHexId() {
        long randomValue = random.nextLong() & 0xFFFFFFFFL;
        if (randomValue < 0x50000000L) {
            randomValue += 0x50000000L;
        }
        return String.format("%08X", randomValue);
    }
    
    /**
     * 模拟XLS数据
     */
    private String[][] createMockXlsData() {
        return new String[][] {
            {"物品名", "描述", "皮肤"},
            {"多情环", "装备", "6120"},
            {"多情环", "装备", "6120"},
            {"多情环", "装备", "6120"},
            {"多情环", "装备", "6120"},
            {"火焰剑", "武器", "9134"},
            {"火焰剑", "武器", "9134"},
            {"冰霜盾", "防具", "255"},
            {"冰霜盾", "防具", "255"},
            {"雷电法杖", "法器", "1000"},
            {"已转换物品", "测试", "0xDDFAFD8E"},
            {"文本物品", "测试", "weapon_sword"},
        };
    }
    
    /**
     * 建立ID映射表
     */
    private void buildIdMapping(String[][] xlsData) {
        idMappingCache.clear();
        
        System.out.println("=== 建立ID映射表 ===");
        
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                String xlsValue = xlsData[i][2];
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim();
                    
                    // 跳过已经是十六进制格式的值
                    if (xlsValue.startsWith("0x") || xlsValue.startsWith("0X")) {
                        continue;
                    }
                    
                    // 跳过标题行和非数字值
                    if (xlsValue.equals("皮肤") || !xlsValue.matches("\\d+")) {
                        continue;
                    }
                    
                    // 如果这个原始ID还没有映射，为它生成一个新的十六进制ID
                    if (!idMappingCache.containsKey(xlsValue)) {
                        String newHexId = generateRandomHexId();
                        String newSkinId = "0x" + newHexId;
                        idMappingCache.put(xlsValue, newSkinId);
                        System.out.println("为原始ID " + xlsValue + " 生成新ID: " + newSkinId);
                    }
                }
            }
        }
        
        System.out.println("ID映射表建立完成，共 " + idMappingCache.size() + " 个映射");
    }
    
    /**
     * 测试文件名转换
     */
    private void testFileNameConversion() {
        System.out.println("\n=== 文件名转换测试 ===");
        
        String[] testFiles = {
            "6120.png",
            "6120_variant.png", // 假设这是6120的变体
            "9134.png",
            "255.png",
            "1000.png",
            "weapon_sword.png",
            "unknown_item.png"
        };
        
        for (String fileName : testFiles) {
            String oldSkinId = fileName.substring(0, fileName.lastIndexOf('.'));
            
            // 检查是否在映射表中有对应的转换ID
            String newSkinId = idMappingCache.get(oldSkinId);
            String newFileName;
            
            if (newSkinId != null) {
                // 使用映射表中的ID
                String hexPart = newSkinId.substring(2); // 去掉"0x"前缀
                newFileName = "2025-" + hexPart + ".png";
                System.out.printf("文件: %-20s -> %-25s (使用映射: %s)%n", 
                    fileName, newFileName, newSkinId);
            } else {
                // 如果映射表中没有，生成新的随机ID
                String randomHexId = generateRandomHexId();
                newFileName = "2025-" + randomHexId + ".png";
                newSkinId = "0x" + randomHexId;
                idMappingCache.put(oldSkinId, newSkinId);
                System.out.printf("文件: %-20s -> %-25s (新生成: %s)%n", 
                    fileName, newFileName, newSkinId);
            }
        }
    }
    
    /**
     * 模拟更新XLS中所有匹配的行
     */
    private void updateAllMatchingXlsRows(String[][] xlsData, String oldSkinId, String newSkinId) {
        System.out.println("\n=== 更新XLS中的 " + oldSkinId + " -> " + newSkinId + " ===");
        
        int updateCount = 0;
        
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                String xlsValue = xlsData[i][2];
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim();
                    
                    if (xlsValue.equals(oldSkinId)) {
                        String oldValue = xlsData[i][2];
                        xlsData[i][2] = newSkinId;
                        updateCount++;
                        System.out.printf("  行%d: %s -> %s (物品: %s)%n", 
                            i + 1, oldValue, newSkinId, xlsData[i][0]);
                    }
                }
            }
        }
        
        System.out.println("  共更新了 " + updateCount + " 行");
    }
    
    /**
     * 显示最终的XLS数据
     */
    private void showFinalXlsData(String[][] xlsData) {
        System.out.println("\n=== 最终XLS数据 ===");
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                System.out.printf("行%d: %-10s %-10s %s%n", 
                    i + 1, xlsData[i][0], xlsData[i][1], xlsData[i][2]);
            }
        }
    }
    
    /**
     * 主测试方法
     */
    public void runTest() {
        // 创建模拟数据
        String[][] xlsData = createMockXlsData();
        
        System.out.println("=== 原始XLS数据 ===");
        showFinalXlsData(xlsData);
        
        // 建立ID映射
        buildIdMapping(xlsData);
        
        // 测试文件名转换
        testFileNameConversion();
        
        // 模拟更新过程
        for (String originalId : idMappingCache.keySet()) {
            String newId = idMappingCache.get(originalId);
            updateAllMatchingXlsRows(xlsData, originalId, newId);
        }
        
        // 显示最终结果
        showFinalXlsData(xlsData);
        
        System.out.println("\n=== 验证一致性 ===");
        // 验证相同的原始ID是否都被转换为相同的新ID
        Map<String, String> seenMappings = new HashMap<>();
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                String value = xlsData[i][2];
                if (value != null && value.startsWith("0x")) {
                    String itemName = xlsData[i][0];
                    if (seenMappings.containsKey(itemName)) {
                        String expectedValue = seenMappings.get(itemName);
                        if (!expectedValue.equals(value)) {
                            System.out.println("错误: " + itemName + " 的ID不一致! " + expectedValue + " vs " + value);
                        }
                    } else {
                        seenMappings.put(itemName, value);
                    }
                }
            }
        }
        System.out.println("一致性检查完成");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        IdMappingTest test = new IdMappingTest();
        test.runTest();
    }
}
