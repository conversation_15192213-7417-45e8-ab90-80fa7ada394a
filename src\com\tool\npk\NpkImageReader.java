package com.tool.npk;

import javax.swing.ImageIcon;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.imageio.ImageIO;

/**
 * NPK文件ImageIcon读取工具类
 * 提供从NPK文件中读取图像并转换为ImageIcon的功能
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkImageReader {
    
    // NPK文件缓存
    private static final Map<String, NpkFile> npkFileCache = new HashMap<>();
    
    // ImageIcon缓存
    private static final Map<String, ImageIcon> imageCache = new HashMap<>();
    
    /**
     * 从NPK文件中读取指定皮肤的ImageIcon
     * 
     * @param skin 皮肤名称（十六进制偏移，如"0x1a5de2bd"）
     * @param w 目标宽度
     * @param h 目标高度
     * @param npkFileName NPK文件名（如"items.npk"）
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getNpkPng(String skin, int w, int h, String npkFileName) {
        try {
            // 生成缓存键
            String cacheKey = npkFileName + "_" + skin + "_" + w + "x" + h;
            
            // 检查缓存
            if (imageCache.containsKey(cacheKey)) {
                return imageCache.get(cacheKey);
            }
            
            // 加载NPK文件
            NpkFile npkFile = loadNpkFile(npkFileName);
            if (npkFile == null) {
                return null;
            }
            
            // 查找指定的皮肤文件
            NpkEntry targetEntry = findEntryBySkin(npkFile, skin);
            if (targetEntry == null) {
                return null;
            }
            
            // 提取图像数据
            byte[] imageData = NpkTool.extractFile(npkFile, targetEntry, null);
            if (imageData == null || imageData.length == 0) {
                return null;
            }
            
            // 转换为BufferedImage
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageData));
            if (bufferedImage == null) {
                return null;
            }
            
            // 缩放到指定尺寸
            Image scaledImage = bufferedImage.getScaledInstance(w, h, Image.SCALE_SMOOTH);
            ImageIcon imageIcon = new ImageIcon(scaledImage);
            
            // 缓存结果
            imageCache.put(cacheKey, imageIcon);
            
            return imageIcon;
            
        } catch (Exception e) {
            System.err.println("读取NPK图像失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 从NPK文件中读取指定皮肤的原始ImageIcon（不缩放）
     * 
     * @param skin 皮肤名称（十六进制偏移，如"0x1a5de2bd"）
     * @param npkFileName NPK文件名（如"items.npk"）
     * @return ImageIcon对象，如果失败返回null
     */
    public static ImageIcon getNpkPng(String skin, String npkFileName) {
        try {
            // 生成缓存键
            String cacheKey = npkFileName + "_" + skin + "_original";
            
            // 检查缓存
            if (imageCache.containsKey(cacheKey)) {
                return imageCache.get(cacheKey);
            }
            
            // 加载NPK文件
            NpkFile npkFile = loadNpkFile(npkFileName);
            if (npkFile == null) {
                return null;
            }
            
            // 查找指定的皮肤文件
            NpkEntry targetEntry = findEntryBySkin(npkFile, skin);
            if (targetEntry == null) {
                return null;
            }
            
            // 提取图像数据
            byte[] imageData = NpkTool.extractFile(npkFile, targetEntry, null);
            if (imageData == null || imageData.length == 0) {
                return null;
            }
            
            // 转换为ImageIcon
            ImageIcon imageIcon = new ImageIcon(imageData);
            
            // 缓存结果
            imageCache.put(cacheKey, imageIcon);
            
            return imageIcon;
            
        } catch (Exception e) {
            System.err.println("读取NPK图像失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 批量预加载NPK文件中的图像到缓存
     * 
     * @param npkFileName NPK文件名
     * @param skinList 要预加载的皮肤列表
     */
    public static void preloadImages(String npkFileName, List<String> skinList) {
        try {
            NpkFile npkFile = loadNpkFile(npkFileName);
            if (npkFile == null) {
                return;
            }
            
            for (String skin : skinList) {
                // 预加载原始尺寸图像
                getNpkPng(skin, npkFileName);
            }
            
        } catch (Exception e) {
            System.err.println("预加载NPK图像失败: " + e.getMessage());
        }
    }
    
    /**
     * 清空图像缓存
     */
    public static void clearImageCache() {
        imageCache.clear();
    }
    
    /**
     * 清空NPK文件缓存
     */
    public static void clearNpkCache() {
        npkFileCache.clear();
    }
    
    /**
     * 清空所有缓存
     */
    public static void clearAllCache() {
        clearImageCache();
        clearNpkCache();
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计字符串
     */
    public static String getCacheStats() {
        return String.format("NPK文件缓存: %d个, 图像缓存: %d个", 
                           npkFileCache.size(), imageCache.size());
    }
    
    /**
     * 加载NPK文件
     */
    private static NpkFile loadNpkFile(String npkFileName) {
        try {
            // 检查缓存
            if (npkFileCache.containsKey(npkFileName)) {
                return npkFileCache.get(npkFileName);
            }
            
            // 查找NPK文件
            File npkFile = findNpkFile(npkFileName);
            if (npkFile == null || !npkFile.exists()) {
                System.err.println("NPK文件不存在: " + npkFileName);
                return null;
            }
            
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            
            // 缓存结果
            npkFileCache.put(npkFileName, parsedFile);
            
            return parsedFile;
            
        } catch (Exception e) {
            System.err.println("加载NPK文件失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 查找NPK文件
     */
    private static File findNpkFile(String npkFileName) {
        // 首先在当前目录查找
        File currentDir = new File(npkFileName);
        if (currentDir.exists()) {
            return currentDir;
        }
        
        // 在常见路径中查找
        String[] searchPaths = {
            ".",
            "./",
            "../",
            "G:/JXy2o/GameClient3.0/res/",
            "res/",
            "data/"
        };
        
        for (String path : searchPaths) {
            File file = new File(path, npkFileName);
            if (file.exists()) {
                return file;
            }
        }
        
        return null;
    }
    
    /**
     * 根据皮肤名称查找NPK条目
     */
    private static NpkEntry findEntryBySkin(NpkFile npkFile, String skin) {
        List<NpkEntry> entries = npkFile.getEntries();
        
        // 确保皮肤名称格式正确
        String targetSkin = skin;
        if (!targetSkin.startsWith("0x")) {
            targetSkin = "0x" + targetSkin;
        }
        if (!targetSkin.endsWith(".png")) {
            targetSkin = targetSkin + ".png";
        }
        
        // 查找匹配的条目
        for (NpkEntry entry : entries) {
            String fileName = entry.getFileName();
            if (fileName != null && fileName.equals(targetSkin)) {
                return entry;
            }
        }
        
        // 如果没找到，尝试模糊匹配
        String hexPart = targetSkin.replace("0x", "").replace(".png", "");
        for (NpkEntry entry : entries) {
            String fileName = entry.getFileName();
            if (fileName != null && fileName.contains(hexPart)) {
                return entry;
            }
        }
        
        return null;
    }
}
