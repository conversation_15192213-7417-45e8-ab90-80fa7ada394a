
package com.tool.wdf;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
public class WdfTool {
    public WdfTool() {
    }

    public static WdfHead getWdfHead(File file) {
        try {
            RandomAccessFile in = new RandomAccessFile(file, "r");
            WdfHead wdfHead = new WdfHead();
            byte[] buf = new byte[12];
            in.read(buf);
            long flag = DataTool.byte2uint32(buf, 0);
            wdfHead.setFlag(flag);
            wdfHead.setFileSum(DataTool.byte2uint32(buf, 4));
            wdfHead.setOffset(DataTool.byte2uint32(buf, 8));
            ArrayList<WasData> list = new ArrayList();
            byte[] filelist = new byte[16 * (int)wdfHead.getFileSum()];
            in.seek(wdfHead.getOffset());
            in.read(filelist);
            buf = new byte[16];

            for(long i = 0L; i < wdfHead.getFileSum(); ++i) {
                System.arraycopy(filelist, (int)(i * 16L), buf, 0, 16);
                long fileoffset = DataTool.byte2uint32(buf, 4);
                in.seek(fileoffset);
                byte[] buf2 = new byte[2];
                in.read(buf2);
                WasData wasFile = new WasData();
                wasFile.setId(DataTool.byte2uint32(buf, 0));
                wasFile.setFileOffset(fileoffset);
                wasFile.setFileSize(DataTool.byte2uint32(buf, 8));
                wasFile.setFileSpace(DataTool.byte2uint32(buf, 12));
                list.add(wasFile);
            }

            WasData[] array = new WasData[list.size()];
            list.toArray(array);
            wdfHead.setWasDataList(array);
            in.close();
            return wdfHead;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String bytesToHex(byte[] bytes) {
        StringBuffer buffer = new StringBuffer();

        for(byte b : bytes) {
            buffer.append(String.format("%02x", b));
        }

        return buffer.toString();
    }

    public static File[] getWdfFiles(File file) {
        return file.listFiles(new Wdfone());
    }

    public static File[] getDirectories(File file) {
        return file.listFiles(new Wdftow());
    }

    public static void packWdfFile(File sourceDir, File targetFile, List<WasData> wasDataList, BiConsumer<Integer, String> progressCallback) throws IOException {
        if (wasDataList == null || wasDataList.isEmpty()) {
            return;
        }

        // 预建文件映射 - 这是性能优化的关键！
        java.util.Map<Long, File> fileMap = new java.util.HashMap<>();
        long mapStartTime = System.currentTimeMillis();
        buildFileMapRecursive(sourceDir, fileMap);
        long mapEndTime = System.currentTimeMillis();

        if (progressCallback != null) {
            progressCallback.accept(0, "文件映射构建完成，耗时: " + (mapEndTime - mapStartTime) + "ms，找到 " + fileMap.size() + " 个文件");
        }

        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(targetFile);
             java.io.BufferedOutputStream bos = new java.io.BufferedOutputStream(fos, 1048576)) { // 1MB缓冲区

            // 写入文件头
            writeIntToStream(bos, 1346782295);
            writeIntToStream(bos, wasDataList.size());
            writeIntToStream(bos, 12);

            // 计算并写入索引
            long dataOffset = 12L + (long)wasDataList.size() * 16L;
            for(int i = 0; i < wasDataList.size(); ++i) {
                WasData wasData = wasDataList.get(i);
                writeIntToStream(bos, (int)wasData.getId());
                writeIntToStream(bos, (int)dataOffset);
                writeIntToStream(bos, (int)wasData.getFileSize());
                writeIntToStream(bos, (int)wasData.getFileSize());
                dataOffset += wasData.getFileSize();

                // 减少进度回调频率
                if (progressCallback != null && (i + 1) % 100 == 0) {
                    progressCallback.accept(i + 1, "正在写入索引: " + (i + 1) + "/" + wasDataList.size());
                }
            }

            // 写入文件数据
            byte[] buffer = new byte[1048576]; // 1MB缓冲区
            for(int i = 0; i < wasDataList.size(); ++i) {
                WasData wasData = wasDataList.get(i);
                File sourceFile = fileMap.get(wasData.getId());

                if (sourceFile == null) {
                    throw new IOException("找不到源文件ID: " + String.format("%08X", wasData.getId()));
                }

                if (sourceFile.length() > 2147483647L) {
                    throw new IOException("文件过大: " + sourceFile.getName());
                }

                // 使用高性能的文件复制
                try (java.io.FileInputStream fis = new java.io.FileInputStream(sourceFile);
                     java.io.BufferedInputStream bis = new java.io.BufferedInputStream(fis, 262144)) { // 256KB读缓冲

                    int bytesRead;
                    while ((bytesRead = bis.read(buffer)) != -1) {
                        bos.write(buffer, 0, bytesRead);
                    }
                }

                // 减少进度回调频率
                if (progressCallback != null && (i + 1) % 20 == 0) {
                    progressCallback.accept(i + 1, "正在写入文件: " + (i + 1) + "/" + wasDataList.size());
                }
            }

            bos.flush(); // 确保所有数据写入

            if (progressCallback != null) {
                progressCallback.accept(wasDataList.size(), "打包完成");
            }
        }
    }

    /**
     * 递归构建文件映射 - 性能优化的核心
     */
    private static void buildFileMapRecursive(File dir, java.util.Map<Long, File> fileMap) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    buildFileMapRecursive(file, fileMap);
                } else {
                    String name = file.getName();
                    if (name.startsWith("2024-")|| name.startsWith("2025-") && (name.endsWith(".png") || name.endsWith(".was"))) {
                        try {
                            String hexPart = name.substring(5, name.lastIndexOf('.'));
                            if (hexPart.length() == 8) { // 确保是8位十六进制
                                long id = Long.parseLong(hexPart, 16);
                                fileMap.put(id, file);
                            }
                        } catch (NumberFormatException e) {
                            // 忽略无效文件名
                        }
                    }
                }
            }
        }
    }

    /**
     * 高性能的整数写入方法
     */
    private static void writeIntToStream(java.io.OutputStream out, int value) throws IOException {
        out.write(value & 0xFF);
        out.write((value >> 8) & 0xFF);
        out.write((value >> 16) & 0xFF);
        out.write((value >> 24) & 0xFF);
    }

    private static void writeInt(RandomAccessFile file, int value) throws IOException {
        byte[] bytes = new byte[4];
        bytes[0] = (byte)(value & 255);
        bytes[1] = (byte)(value >> 8 & 255);
        bytes[2] = (byte)(value >> 16 & 255);
        bytes[3] = (byte)(value >> 24 & 255);
        file.write(bytes);
    }

    public static void importFileToWdf(File wdfFile, File importFile, long fileId, String fileType, BiConsumer<Integer, String> progressCallback) throws IOException {
        WdfHead wdfHead = getWdfHead(wdfFile);
        if (wdfHead == null) {
            throw new IOException("无法读取WDF文件头");
        } else {
            boolean isUpdate = false;
            WasData existingData = null;

            for(WasData wasData : wdfHead.getWasDataList()) {
                if (wasData.getId() == fileId) {
                    isUpdate = true;
                    existingData = wasData;
                    break;
                }
            }

            try (RandomAccessFile raf = new RandomAccessFile(wdfFile, "rw")) {
                if (isUpdate) {
                    progressCallback.accept(30, "正在更新文件...");
                    if (importFile.length() <= existingData.getFileSpace()) {
                        raf.seek(existingData.getFileOffset());
                        writeFileContent(raf, importFile);
                        raf.seek((long)(12 + findWasDataIndex(wdfHead, fileId) * 16 + 8));
                        writeInt(raf, (int)importFile.length());
                    } else {
                        appendFile(raf, wdfHead, importFile, fileId, progressCallback);
                    }
                } else {
                    appendFile(raf, wdfHead, importFile, fileId, progressCallback);
                }
            }

        }
    }

    private static void appendFile(RandomAccessFile raf, WdfHead wdfHead, File importFile, long fileId, BiConsumer<Integer, String> progressCallback) throws IOException {
        long fileOffset = raf.length();
        progressCallback.accept(50, "正在写入文件内容...");
        raf.seek(fileOffset);
        writeFileContent(raf, importFile);
        progressCallback.accept(70, "正在更新文件头...");
        raf.seek(4L);
        writeInt(raf, wdfHead.getWasDataList().length + 1);
        progressCallback.accept(90, "正在更新索引表...");
        raf.seek((long)(12 + wdfHead.getWasDataList().length * 16));
        writeInt(raf, (int)fileId);
        writeInt(raf, (int)fileOffset);
        writeInt(raf, (int)importFile.length());
        writeInt(raf, (int)importFile.length());
    }

    private static void writeFileContent(RandomAccessFile raf, File file) throws IOException {
        try (RandomAccessFile source = new RandomAccessFile(file, "r")) {
            // 增大缓冲区大小以提高性能
            byte[] buffer = new byte[65536]; // 64KB缓冲区

            int read;
            for(long remaining = file.length(); remaining > 0L; remaining -= (long)read) {
                int toRead = (int)Math.min((long)buffer.length, remaining);
                read = source.read(buffer, 0, toRead);
                if (read == -1) {
                    break;
                }

                raf.write(buffer, 0, read);
            }
        }

    }

    private static int findWasDataIndex(WdfHead wdfHead, long fileId) {
        for(int i = 0; i < wdfHead.getWasDataList().length; ++i) {
            if (wdfHead.getWasDataList()[i].getId() == fileId) {
                return i;
            }
        }

        return -1;
    }

    public static void deleteFileFromWdf(File wdfFile, WasData wasData, BiConsumer<Integer, String> progressCallback) throws IOException {
        WdfHead wdfHead = getWdfHead(wdfFile);
        if (wdfHead == null) {
            throw new IOException("无法读取WDF文件头");
        } else {
            try (RandomAccessFile raf = new RandomAccessFile(wdfFile, "rw")) {
                progressCallback.accept(30, "正在更新文件头...");
                raf.seek(4L);
                writeInt(raf, wdfHead.getWasDataList().length - 1);
                progressCallback.accept(60, "正在更新索引表...");
                int index = findWasDataIndex(wdfHead, wasData.getId());
                if (index >= 0) {
                    byte[] buffer = new byte[16];

                    for(int i = index + 1; i < wdfHead.getWasDataList().length; ++i) {
                        raf.seek((long)(12 + i * 16));
                        raf.read(buffer);
                        raf.seek((long)(12 + (i - 1) * 16));
                        raf.write(buffer);
                    }
                }

                progressCallback.accept(100, "删除完成");
            }

        }
    }
}
