package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.util.List;
import java.util.Set;
import java.util.HashSet;

/**
 * NPK文件名一致性测试
 * 验证提取和打包后的文件名是否保持一致
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkConsistencyTest {
    
    private static final String ORIGINAL_NPK = "item.npk";
    private static final String EXTRACT_FOLDER = "extracted_test";
    private static final String PACKED_NPK = "repacked_test.npk";
    private static final String REEXTRACT_FOLDER = "reextracted_test";
    
    /**
     * 执行完整的一致性测试
     */
    public void runConsistencyTest() {
        System.out.println("=== NPK文件名一致性测试 ===");
        System.out.println("目标: 验证提取->打包->再提取的文件名一致性");
        System.out.println();
        
        try {
            // 步骤1: 从原始NPK提取文件
            System.out.println("步骤1: 从原始NPK提取文件...");
            Set<String> originalFileNames = extractFromOriginalNpk();
            System.out.println("原始提取文件数: " + originalFileNames.size());
            System.out.println();
            
            // 步骤2: 将提取的文件打包成新NPK
            System.out.println("步骤2: 将提取的文件打包成新NPK...");
            boolean packSuccess = packExtractedFiles();
            if (!packSuccess) {
                System.out.println("✗ 打包失败，测试终止");
                return;
            }
            System.out.println("✓ 打包成功");
            System.out.println();
            
            // 步骤3: 从新NPK再次提取文件
            System.out.println("步骤3: 从新NPK再次提取文件...");
            Set<String> reextractedFileNames = extractFromPackedNpk();
            System.out.println("重新提取文件数: " + reextractedFileNames.size());
            System.out.println();
            
            // 步骤4: 比较文件名一致性
            System.out.println("步骤4: 比较文件名一致性...");
            compareFileNames(originalFileNames, reextractedFileNames);
            
        } catch (Exception e) {
            System.out.println("测试过程发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 从原始NPK提取文件
     */
    private Set<String> extractFromOriginalNpk() {
        Set<String> fileNames = new HashSet<>();
        
        try {
            File npkFile = new File(ORIGINAL_NPK);
            if (!npkFile.exists()) {
                System.out.println("原始NPK文件不存在: " + ORIGINAL_NPK);
                return fileNames;
            }
            
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            List<NpkEntry> entries = parsedFile.getEntries();
            
            // 创建提取目录
            File extractDir = new File(EXTRACT_FOLDER);
            if (extractDir.exists()) {
                deleteDirectory(extractDir);
            }
            extractDir.mkdirs();
            
            // 提取前100个文件进行测试
            int extractCount = Math.min(100, entries.size());
            int successCount = 0;
            
            for (int i = 0; i < extractCount; i++) {
                NpkEntry entry = entries.get(i);
                
                try {
                    byte[] data = NpkTool.extractFile(parsedFile, entry, null);
                    if (data != null && data.length > 0 && isPngData(data)) {
                        String fileName = entry.getFileName();
                        File outputFile = new File(extractDir, fileName);
                        
                        java.nio.file.Files.write(outputFile.toPath(), data);
                        fileNames.add(fileName);
                        successCount++;
                        
                        if (successCount <= 10) {
                            System.out.println("  提取: " + fileName + " (" + data.length + " 字节)");
                        }
                    }
                } catch (Exception e) {
                    // 提取失败，跳过
                }
            }
            
            System.out.println("  成功提取: " + successCount + "/" + extractCount + " 个文件");
            
        } catch (Exception e) {
            System.out.println("从原始NPK提取失败: " + e.getMessage());
        }
        
        return fileNames;
    }
    
    /**
     * 打包提取的文件
     */
    private boolean packExtractedFiles() {
        try {
            File extractDir = new File(EXTRACT_FOLDER);
            if (!extractDir.exists()) {
                System.out.println("提取目录不存在: " + EXTRACT_FOLDER);
                return false;
            }
            
            // 删除旧的打包文件
            File packedFile = new File(PACKED_NPK);
            if (packedFile.exists()) {
                packedFile.delete();
            }
            
            // 执行打包
            boolean success = NpkPacker.packFolder(
                EXTRACT_FOLDER,
                PACKED_NPK,
                1, // 使用Deflate压缩
                (progress, message) -> {
                    if (progress >= 0 && progress % 20 == 0) {
                        System.out.println("  打包进度: " + progress + "%");
                    }
                }
            );
            
            if (success && packedFile.exists()) {
                long fileSize = packedFile.length();
                System.out.println("  打包文件大小: " + formatFileSize(fileSize));
                return true;
            }
            
        } catch (Exception e) {
            System.out.println("打包失败: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * 从打包的NPK提取文件
     */
    private Set<String> extractFromPackedNpk() {
        Set<String> fileNames = new HashSet<>();
        
        try {
            File npkFile = new File(PACKED_NPK);
            if (!npkFile.exists()) {
                System.out.println("打包的NPK文件不存在: " + PACKED_NPK);
                return fileNames;
            }
            
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            List<NpkEntry> entries = parsedFile.getEntries();
            
            // 创建重新提取目录
            File reextractDir = new File(REEXTRACT_FOLDER);
            if (reextractDir.exists()) {
                deleteDirectory(reextractDir);
            }
            reextractDir.mkdirs();
            
            int successCount = 0;
            
            for (NpkEntry entry : entries) {
                try {
                    byte[] data = NpkTool.extractFile(parsedFile, entry, null);
                    if (data != null && data.length > 0 && isPngData(data)) {
                        String fileName = entry.getFileName();
                        File outputFile = new File(reextractDir, fileName);
                        
                        java.nio.file.Files.write(outputFile.toPath(), data);
                        fileNames.add(fileName);
                        successCount++;
                        
                        if (successCount <= 10) {
                            System.out.println("  重新提取: " + fileName + " (" + data.length + " 字节)");
                        }
                    }
                } catch (Exception e) {
                    // 提取失败，跳过
                }
            }
            
            System.out.println("  成功重新提取: " + successCount + " 个文件");
            
        } catch (Exception e) {
            System.out.println("从打包NPK提取失败: " + e.getMessage());
        }
        
        return fileNames;
    }
    
    /**
     * 比较文件名一致性
     */
    private void compareFileNames(Set<String> originalNames, Set<String> reextractedNames) {
        System.out.println("=== 文件名一致性分析 ===");
        
        // 统计
        int originalCount = originalNames.size();
        int reextractedCount = reextractedNames.size();
        
        // 找出相同的文件名
        Set<String> commonNames = new HashSet<>(originalNames);
        commonNames.retainAll(reextractedNames);
        
        // 找出缺失的文件名
        Set<String> missingNames = new HashSet<>(originalNames);
        missingNames.removeAll(reextractedNames);
        
        // 找出新增的文件名
        Set<String> extraNames = new HashSet<>(reextractedNames);
        extraNames.removeAll(originalNames);
        
        System.out.println("原始文件数: " + originalCount);
        System.out.println("重新提取文件数: " + reextractedCount);
        System.out.println("相同文件名数: " + commonNames.size());
        System.out.println("缺失文件名数: " + missingNames.size());
        System.out.println("新增文件名数: " + extraNames.size());
        
        // 计算一致性百分比
        double consistencyRate = (commonNames.size() * 100.0) / Math.max(originalCount, reextractedCount);
        System.out.println("文件名一致性: " + String.format("%.2f", consistencyRate) + "%");
        
        // 显示示例
        if (!commonNames.isEmpty()) {
            System.out.println("\n相同文件名示例:");
            commonNames.stream().limit(5).forEach(name -> System.out.println("  ✓ " + name));
        }
        
        if (!missingNames.isEmpty()) {
            System.out.println("\n缺失文件名示例:");
            missingNames.stream().limit(5).forEach(name -> System.out.println("  ✗ " + name));
        }
        
        if (!extraNames.isEmpty()) {
            System.out.println("\n新增文件名示例:");
            extraNames.stream().limit(5).forEach(name -> System.out.println("  + " + name));
        }
        
        // 结论
        System.out.println("\n=== 测试结论 ===");
        if (consistencyRate >= 95.0) {
            System.out.println("✓ 文件名一致性测试通过! 一致性达到 " + String.format("%.2f", consistencyRate) + "%");
        } else if (consistencyRate >= 80.0) {
            System.out.println("⚠ 文件名一致性一般，一致性为 " + String.format("%.2f", consistencyRate) + "%，需要优化");
        } else {
            System.out.println("✗ 文件名一致性测试失败! 一致性仅为 " + String.format("%.2f", consistencyRate) + "%");
        }
    }
    
    /**
     * 检查是否是PNG数据
     */
    private boolean isPngData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }
        
        return data[0] == (byte)0x89 && 
               data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && 
               data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && 
               data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && 
               data[7] == (byte)0x0A;
    }
    
    /**
     * 删除目录
     */
    private void deleteDirectory(File dir) {
        if (dir.exists()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            dir.delete();
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        NpkConsistencyTest test = new NpkConsistencyTest();
        test.runConsistencyTest();
    }
}
