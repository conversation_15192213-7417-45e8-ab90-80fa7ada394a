package com.tool.test;

import com.tool.npk.NpkPacker;
import java.io.File;

/**
 * 测试NPK打包对WAS文件的支持
 */
public class NpkWasPackTest {
    
    public static void main(String[] args) {
        // 测试文件夹路径（需要包含PNG和WAS文件）
        String testFolder = "test_files";
        String outputNpk = "test_output.npk";
        
        System.out.println("=== NPK WAS文件支持测试 ===");
        System.out.println("测试文件夹: " + testFolder);
        System.out.println("输出NPK: " + outputNpk);
        System.out.println();
        
        // 创建测试文件夹（如果不存在）
        File folder = new File(testFolder);
        if (!folder.exists()) {
            System.out.println("创建测试文件夹: " + testFolder);
            folder.mkdirs();
        }
        
        // 执行打包测试
        boolean success = NpkPacker.packFolder(
            testFolder, 
            outputNpk, 
            0, // 无压缩
            (progress, message) -> {
                if (progress >= 0) {
                    System.out.println("[" + progress + "%] " + message);
                } else {
                    System.out.println(message);
                }
            }
        );
        
        System.out.println();
        if (success) {
            System.out.println("✓ NPK打包测试成功！");
            
            // 检查输出文件
            File outputFile = new File(outputNpk);
            if (outputFile.exists()) {
                System.out.println("输出文件大小: " + outputFile.length() + " 字节");
            }
        } else {
            System.out.println("✗ NPK打包测试失败！");
        }
    }
}
