package com.tool.ui;

import javax.swing.*;

/**
 * Theme Manager - Simplified version using system default theme
 *
 * <AUTHOR>
 * @date 2024/12/16
 */
public class ThemeManager {

    /**
     * Initialize theme system - using system default theme
     */
    public static void initialize() {
        try {
            // Set system default look and feel
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            e.printStackTrace();
            // If failed, use cross-platform default look and feel
            try {
                UIManager.setLookAndFeel(UIManager.getCrossPlatformLookAndFeelClassName());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
