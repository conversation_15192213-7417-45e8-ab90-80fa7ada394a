package com.tool.wdf;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import javax.swing.filechooser.FileFilter;

public class PakMain {


    public PakMain() {

    }


    private void packCurrentWdf() {
        JFileChooser dirChooser = new JFileChooser();
        dirChooser.setDialogTitle("选择要打包的文件夹");
        dirChooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        if (dirChooser.showDialog(null, "选择") == JFileChooser.APPROVE_OPTION) {
            JFileChooser saveChooser = new JFileChooser();
            saveChooser.setDialogTitle("保存WDF文件");
            saveChooser.setFileFilter(new FileFilter() {
                public boolean accept(File f) {
                    return f.isDirectory() || f.getName().toLowerCase().endsWith(".wdf");
                }

                public String getDescription() {
                    return "WDF Files (*.wdf)";
                }
            });
            if (saveChooser.showSaveDialog(null) == JFileChooser.APPROVE_OPTION) {
                File targetFile = !saveChooser.getSelectedFile().getName().toLowerCase().endsWith(".wdf") ? new File(saveChooser.getSelectedFile().getAbsolutePath() + ".wdf") : saveChooser.getSelectedFile();
                if (targetFile.exists()) {
                    int result = JOptionPane.showConfirmDialog(null, "文件已存在，是否覆盖？", "确认覆盖", JOptionPane.YES_NO_OPTION);
                    if (result != JOptionPane.YES_OPTION) {
                        return;
                    }
                }

                File sourceDir = dirChooser.getSelectedFile();
                java.util.List<File> files = new ArrayList();
                this.collectFiles(sourceDir, files);
                if (files.isEmpty()) {
                    JOptionPane.showMessageDialog(null, "未找到可打包的文件！");
                } else {
                    JDialog progressDialog = new JDialog((Frame)null, "打包进度", true);
                    JProgressBar progressBar = new JProgressBar(0, files.size());
                    JLabel statusLabel = new JLabel("准备打包...");
                    progressDialog.setLayout(new BorderLayout(5, 5));
                    progressDialog.add(statusLabel, "North");
                    progressDialog.add(progressBar, "Center");
                    progressDialog.setSize(300, 100);
                    progressDialog.setLocationRelativeTo(null);
                    CompletableFuture.runAsync(() -> {
                        try {
                            long dataOffset = 12L + (long)files.size() * 16L;
                            java.util.List<WasData> wasDataList = new ArrayList();

                            for(File file : files) {
                                WasData wasData = new WasData();
                                String fileName = file.getName();
                                String[] nameParts = fileName.substring(0, fileName.lastIndexOf(46)).split("-");
                                wasData.setId(Long.parseLong(nameParts[1], 16));
                                wasData.setFileOffset(dataOffset);
                                wasData.setFileSize(file.length());
                                wasData.setFileSpace(file.length());
                                wasDataList.add(wasData);
                                dataOffset += file.length();
                            }

                            WdfTool.packWdfFile(dirChooser.getSelectedFile(), targetFile, wasDataList, (progress, status) -> SwingUtilities.invokeLater(() -> {
                                progressBar.setValue(progress);
                                statusLabel.setText(status);
                            }));

                            try (RandomAccessFile target = new RandomAccessFile(targetFile, "rw")) {
                                target.seek(12L + (long)files.size() * 16L);

                                for(int i = 0; i < files.size(); ++i) {
                                    File file = (File)files.get(i);
                                    RandomAccessFile source = new RandomAccessFile(file, "r");
                                    Throwable var15 = null;

                                    try {
                                        byte[] buffer = new byte[8192];

                                        int read;
                                        for(long remaining = file.length(); remaining > 0L; remaining -= (long)read) {
                                            read = source.read(buffer, 0, (int)Math.min((long)buffer.length, remaining));
                                            if (read == -1) {
                                                break;
                                            }

                                            target.write(buffer, 0, read);
                                        }
                                    } catch (Throwable var43) {
                                        var15 = var43;
                                        throw var43;
                                    } finally {
                                        if (source != null) {
                                            if (var15 != null) {
                                                try {
                                                    source.close();
                                                } catch (Throwable var42) {
                                                    var15.addSuppressed(var42);
                                                }
                                            } else {
                                                source.close();
                                            }
                                        }

                                    }
                                }
                            }

                            SwingUtilities.invokeLater(() -> {
                                progressDialog.dispose();
                                JOptionPane.showMessageDialog(null, "打包完成！");
                            });
                        } catch (Exception ex) {
                            SwingUtilities.invokeLater(() -> {
                                progressDialog.dispose();
                                JOptionPane.showMessageDialog(null, "打包失败: " + ex.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                            });
                            ex.printStackTrace();
                        }

                    });
                    progressDialog.setVisible(true);
                }
            }
        }
    }

    private void collectFiles(File dir, java.util.List<File> files) {
        File[] fileList = dir.listFiles();
        if (fileList != null) {
            for(File file : fileList) {
                if (file.isDirectory()) {
                    this.collectFiles(file, files);
                } else {
                    String name = file.getName().toLowerCase();
                    if (name.endsWith(".png") || name.endsWith(".was")) {
                        String baseName = name.substring(0, name.lastIndexOf(46));
                        String[] parts = baseName.split("-");
                        if (parts.length == 2 && parts[1].matches("[0-9A-Fa-f]{8}")) {
                            files.add(file);
                        }
                    }
                }
            }
        }

    }

}