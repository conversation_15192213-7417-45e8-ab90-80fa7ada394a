package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;

/**
 * 条目数量调试器
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class EntryCountDebugger {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 调试条目数量问题
     */
    public void debugEntryCount() {
        System.out.println("=== 条目数量调试 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            long indexOffset = 0x7354149L;
            long fileSize = raf.length();
            long indexSize = fileSize - indexOffset;
            int stepSize = 56;
            
            System.out.println("文件大小: " + fileSize);
            System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
            System.out.println("索引大小: " + indexSize);
            System.out.println("步长: " + stepSize);
            
            int maxPossibleEntries = (int)(indexSize / stepSize);
            System.out.println("理论最大条目数: " + maxPossibleEntries);
            System.out.println("期望条目数: 6165");
            
            // 模拟我们的读取逻辑
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < 6165; i++) {
                long entryPos = indexOffset + (i * stepSize);
                
                if (entryPos + stepSize > fileSize) {
                    System.out.println("条目 " + i + " 超出文件范围: 位置=" + entryPos + ", 需要=" + (entryPos + stepSize) + ", 文件大小=" + fileSize);
                    break;
                }
                
                try {
                    raf.seek(entryPos);
                    
                    // 读取条目数据
                    long f1 = readInt32LE(raf) & 0xFFFFFFFFL;
                    long f2 = readInt32LE(raf) & 0xFFFFFFFFL;
                    long f3 = readInt32LE(raf) & 0xFFFFFFFFL;
                    long f4 = readInt32LE(raf) & 0xFFFFFFFFL;
                    
                    // 验证数据合理性
                    if (f2 >= fileSize || f3 <= 0 || f3 > 100 * 1024 * 1024) {
                        failCount++;
                        if (failCount <= 10) {
                            System.out.println("条目 " + i + " 数据不合理: 偏移=0x" + Long.toHexString(f2) + ", 大小=" + f3);
                        }
                        continue;
                    }
                    
                    successCount++;
                    
                    // 显示前几个和最后几个成功的条目
                    if (i < 5 || i >= 6160) {
                        System.out.println("条目 " + i + " 成功: 偏移=0x" + Long.toHexString(f2) + ", 大小=" + f3);
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    if (failCount <= 10) {
                        System.out.println("条目 " + i + " 读取异常: " + e.getMessage());
                    }
                }
                
                // 每1000个条目报告一次进度
                if ((i + 1) % 1000 == 0) {
                    System.out.println("进度: " + (i + 1) + "/6165, 成功: " + successCount + ", 失败: " + failCount);
                }
            }
            
            System.out.println("\n=== 调试结果 ===");
            System.out.println("总处理条目: " + (successCount + failCount));
            System.out.println("成功条目: " + successCount);
            System.out.println("失败条目: " + failCount);
            System.out.println("成功率: " + (successCount * 100.0 / (successCount + failCount)) + "%");
            
            if (successCount < 6165) {
                System.out.println("\n可能的问题:");
                System.out.println("1. 条目大小不是56字节");
                System.out.println("2. 索引结构更复杂");
                System.out.println("3. 有些条目是无效的");
            }
            
        } catch (IOException e) {
            System.out.println("调试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 尝试不同的步长
     */
    public void tryDifferentStepSizes() {
        System.out.println("\n=== 尝试不同的步长 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            long indexOffset = 0x7354149L;
            long fileSize = raf.length();
            long indexSize = fileSize - indexOffset;
            
            // 尝试不同的步长
            double[] stepSizes = {56.0, 56.79, 57.0, 58.0};
            
            for (double stepSize : stepSizes) {
                System.out.println("\n测试步长: " + stepSize);
                
                int successCount = 0;
                
                for (int i = 0; i < 6165; i++) {
                    long entryPos = indexOffset + (long)(i * stepSize);
                    
                    if (entryPos + 32 > fileSize) {
                        break;
                    }
                    
                    try {
                        raf.seek(entryPos);
                        
                        long f2 = readInt32LEAt(raf, entryPos + 4) & 0xFFFFFFFFL;
                        long f3 = readInt32LEAt(raf, entryPos + 8) & 0xFFFFFFFFL;
                        
                        if (f2 < fileSize && f3 > 0 && f3 < 100 * 1024 * 1024) {
                            successCount++;
                        }
                        
                    } catch (Exception e) {
                        // 忽略错误
                    }
                }
                
                System.out.println("  成功条目: " + successCount + "/6165 (" + (successCount * 100.0 / 6165) + "%)");
                
                if (successCount > 6000) {
                    System.out.println("  *** 可能是正确的步长! ***");
                }
            }
            
        } catch (IOException e) {
            System.out.println("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 在指定位置读取32位小端序整数
     */
    private int readInt32LEAt(RandomAccessFile raf, long pos) throws IOException {
        raf.seek(pos);
        return readInt32LE(raf);
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 运行所有调试测试
     */
    public void runAllDebugTests() {
        debugEntryCount();
        tryDifferentStepSizes();
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        EntryCountDebugger debugger = new EntryCountDebugger();
        debugger.runAllDebugTests();
    }
}
