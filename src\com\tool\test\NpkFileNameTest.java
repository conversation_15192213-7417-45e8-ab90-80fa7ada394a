package com.tool.test;

import com.tool.npk.NpkPacker;
import com.tool.npk.NpkTool;
import com.tool.npk.NpkFile;
import com.tool.npk.NpkEntry;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 测试NPK文件名保持的完整流程
 */
public class NpkFileNameTest {
    
    public static void main(String[] args) {
        String testDir = "test_filename";
        String npkFile = "test_filename.npk";
        
        try {
            // 第一步：创建测试文件
            createTestFiles(testDir);
            
            // 第二步：打包到NPK
            System.out.println("=== 开始NPK打包 ===");
            boolean packSuccess = NpkPacker.packFolder(
                testDir, 
                npkFile, 
                0, // 无压缩
                (progress, message) -> {
                    System.out.println("[打包] " + message);
                }
            );
            
            if (!packSuccess) {
                System.err.println("NPK打包失败！");
                return;
            }
            
            // 第三步：解析NPK文件
            System.out.println("\n=== 开始NPK解析 ===");
            NpkFile parsedNpk = NpkTool.parseNpkFile(new File(npkFile), (progress, message) -> {
                System.out.println("[解析] " + message);
            });
            
            if (parsedNpk == null) {
                System.err.println("NPK解析失败！");
                return;
            }
            
            // 第四步：检查文件名
            System.out.println("\n=== 文件名检查结果 ===");
            List<NpkEntry> entries = parsedNpk.getEntries();
            for (int i = 0; i < entries.size(); i++) {
                NpkEntry entry = entries.get(i);
                System.out.println(String.format("[%03d] %s (大小: %d 字节)", 
                    i, entry.getFileName(), entry.getOriginalSize()));
            }
            
            System.out.println("\n✓ 测试完成！");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void createTestFiles(String testDir) throws IOException {
        File dir = new File(testDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        // 创建一些测试WAS文件
        String[] testFileNames = {
            "0xE83A1600.was",
            "0xE83A1601.was", 
            "0xE83A1602.was",
            "1712-E83A1603.was",
            "1712-E83A1604.was"
        };
        
        for (String fileName : testFileNames) {
            File testFile = new File(dir, fileName);
            try (FileOutputStream fos = new FileOutputStream(testFile)) {
                // 写入一些测试数据
                String content = "Test WAS file: " + fileName;
                fos.write(content.getBytes("UTF-8"));
            }
            System.out.println("创建测试文件: " + fileName);
        }
    }
}
