package com.tool.npk;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * NPK文件排序辅助类
 * 提供各种排序方式的实现
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class NpkSortHelper {
    
    /**
     * 根据排序类型对NPK条目进行排序
     * 
     * @param entries 要排序的条目列表
     * @param sortType 排序类型
     * @return 排序后的条目列表
     */
    public static List<NpkEntry> sortEntries(List<NpkEntry> entries, String sortType) {
        if (entries == null || entries.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建副本以避免修改原列表
        List<NpkEntry> sortedEntries = new ArrayList<>(entries);
        
        switch (sortType) {
            case "文件名(A-Z)":
                sortedEntries.sort(getFileNameComparator(false));
                break;
                
            case "文件名(Z-A)":
                sortedEntries.sort(getFileNameComparator(true));
                break;
                
            case "文件大小(小到大)":
                sortedEntries.sort(getFileSizeComparator(false));
                break;
                
            case "文件大小(大到小)":
                sortedEntries.sort(getFileSizeComparator(true));
                break;
                
            case "压缩率(低到高)":
                sortedEntries.sort(getCompressionRatioComparator(false));
                break;
                
            case "压缩率(高到低)":
                sortedEntries.sort(getCompressionRatioComparator(true));
                break;
                
            case "默认顺序":
            default:
                // 保持原有顺序，不进行排序
                break;
        }
        
        return sortedEntries;
    }
    
    /**
     * 获取文件名比较器
     * 
     * @param reverse 是否反向排序
     * @return 文件名比较器
     */
    private static Comparator<NpkEntry> getFileNameComparator(boolean reverse) {
        Comparator<NpkEntry> comparator = (entry1, entry2) -> {
            String name1 = entry1.getFileName();
            String name2 = entry2.getFileName();
            
            if (name1 == null && name2 == null) return 0;
            if (name1 == null) return -1;
            if (name2 == null) return 1;
            
            return name1.compareToIgnoreCase(name2);
        };
        
        return reverse ? comparator.reversed() : comparator;
    }
    
    /**
     * 获取文件大小比较器
     * 
     * @param reverse 是否反向排序
     * @return 文件大小比较器
     */
    private static Comparator<NpkEntry> getFileSizeComparator(boolean reverse) {
        Comparator<NpkEntry> comparator = (entry1, entry2) -> {
            long size1 = entry1.getOriginalSize();
            long size2 = entry2.getOriginalSize();
            
            int result = Long.compare(size1, size2);
            
            // 如果大小相同，按文件名排序
            if (result == 0) {
                String name1 = entry1.getFileName();
                String name2 = entry2.getFileName();
                if (name1 != null && name2 != null) {
                    result = name1.compareToIgnoreCase(name2);
                }
            }
            
            return result;
        };
        
        return reverse ? comparator.reversed() : comparator;
    }
    
    /**
     * 获取压缩率比较器
     * 
     * @param reverse 是否反向排序
     * @return 压缩率比较器
     */
    private static Comparator<NpkEntry> getCompressionRatioComparator(boolean reverse) {
        Comparator<NpkEntry> comparator = (entry1, entry2) -> {
            double ratio1 = calculateCompressionRatio(entry1);
            double ratio2 = calculateCompressionRatio(entry2);
            
            int result = Double.compare(ratio1, ratio2);
            
            // 如果压缩率相同，按文件大小排序
            if (result == 0) {
                result = Long.compare(entry1.getOriginalSize(), entry2.getOriginalSize());
            }
            
            // 如果大小也相同，按文件名排序
            if (result == 0) {
                String name1 = entry1.getFileName();
                String name2 = entry2.getFileName();
                if (name1 != null && name2 != null) {
                    result = name1.compareToIgnoreCase(name2);
                }
            }
            
            return result;
        };
        
        return reverse ? comparator.reversed() : comparator;
    }
    
    /**
     * 计算压缩率
     * 
     * @param entry NPK条目
     * @return 压缩率 (0.0 - 1.0)
     */
    private static double calculateCompressionRatio(NpkEntry entry) {
        long originalSize = entry.getOriginalSize();
        long compressedSize = entry.getCompressedSize();
        
        if (originalSize <= 0) {
            return 0.0;
        }
        
        return 1.0 - ((double) compressedSize / originalSize);
    }
    
    /**
     * 获取排序统计信息
     * 
     * @param entries 条目列表
     * @return 统计信息字符串
     */
    public static String getSortStatistics(List<NpkEntry> entries) {
        if (entries == null || entries.isEmpty()) {
            return "无数据";
        }
        
        long totalOriginalSize = 0;
        long totalCompressedSize = 0;
        long minSize = Long.MAX_VALUE;
        long maxSize = 0;
        
        for (NpkEntry entry : entries) {
            long originalSize = entry.getOriginalSize();
            long compressedSize = entry.getCompressedSize();
            
            totalOriginalSize += originalSize;
            totalCompressedSize += compressedSize;
            
            minSize = Math.min(minSize, originalSize);
            maxSize = Math.max(maxSize, originalSize);
        }
        
        double avgCompressionRatio = 0.0;
        if (totalOriginalSize > 0) {
            avgCompressionRatio = (1.0 - ((double) totalCompressedSize / totalOriginalSize)) * 100;
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("文件数: ").append(entries.size()).append("\n");
        sb.append("总原始大小: ").append(formatFileSize(totalOriginalSize)).append("\n");
        sb.append("总压缩大小: ").append(formatFileSize(totalCompressedSize)).append("\n");
        sb.append("平均压缩率: ").append(String.format("%.1f%%", avgCompressionRatio)).append("\n");
        sb.append("最小文件: ").append(formatFileSize(minSize)).append("\n");
        sb.append("最大文件: ").append(formatFileSize(maxSize));
        
        return sb.toString();
    }
    
    /**
     * 格式化文件大小
     * 
     * @param bytes 字节数
     * @return 格式化的文件大小字符串
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取支持的排序类型列表
     * 
     * @return 排序类型数组
     */
    public static String[] getSupportedSortTypes() {
        return new String[]{
            "默认顺序", 
            "文件名(A-Z)", 
            "文件名(Z-A)", 
            "文件大小(小到大)", 
            "文件大小(大到小)",
            "压缩率(低到高)", 
            "压缩率(高到低)"
        };
    }
    
    /**
     * 检查排序类型是否有效
     * 
     * @param sortType 排序类型
     * @return 是否有效
     */
    public static boolean isValidSortType(String sortType) {
        if (sortType == null) {
            return false;
        }
        
        String[] supportedTypes = getSupportedSortTypes();
        for (String type : supportedTypes) {
            if (type.equals(sortType)) {
                return true;
            }
        }
        
        return false;
    }
}
