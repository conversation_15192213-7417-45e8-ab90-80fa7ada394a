package com.tool.skin;

import xsl.ReadExelTool;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 生成跳过文件的详细报告
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class SkippedFilesReport {
    
    private static final String DEFAULT_ITEM_FOLDER = "G:\\JXy2o\\GameClient3.0\\res\\item";
    private static final String DEFAULT_XLS_PATH = "G:\\JXy2o\\GameClient3.0\\res\\config\\item.xls";
    
    /**
     * 文件分析结果
     */
    public static class FileAnalysisResult {
        private String fileName;
        private String nameWithoutExt;
        private boolean foundInXls;
        private int xlsRowIndex;
        private String xlsValue;
        private String fileType; // "numeric", "text", "converted"
        
        public FileAnalysisResult(String fileName) {
            this.fileName = fileName;
            this.nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            this.foundInXls = false;
            this.xlsRowIndex = -1;
            this.xlsValue = "";
            
            // 判断文件类型
            if (fileName.matches("^2025-[0-9A-Fa-f]{8}\\.png$")) {
                this.fileType = "converted";
            } else if (nameWithoutExt.matches("\\d+")) {
                this.fileType = "numeric";
            } else {
                this.fileType = "text";
            }
        }
        
        // Getters and Setters
        public String getFileName() { return fileName; }
        public String getNameWithoutExt() { return nameWithoutExt; }
        public boolean isFoundInXls() { return foundInXls; }
        public void setFoundInXls(boolean foundInXls) { this.foundInXls = foundInXls; }
        public int getXlsRowIndex() { return xlsRowIndex; }
        public void setXlsRowIndex(int xlsRowIndex) { this.xlsRowIndex = xlsRowIndex; }
        public String getXlsValue() { return xlsValue; }
        public void setXlsValue(String xlsValue) { this.xlsValue = xlsValue; }
        public String getFileType() { return fileType; }
    }
    
    /**
     * 分析所有PNG文件
     */
    public List<FileAnalysisResult> analyzeAllFiles(String itemFolder, String xlsPath) {
        List<FileAnalysisResult> results = new ArrayList<>();
        
        try {
            // 读取XLS文件
            String[][] xlsData = ReadExelTool.getResult(xlsPath);
            if (xlsData == null) {
                System.out.println("错误: 无法读取XLS文件");
                return results;
            }
            
            // 扫描PNG文件
            Path folderPath = Paths.get(itemFolder);
            Files.list(folderPath)
                .filter(path -> path.toString().toLowerCase().endsWith(".png"))
                .forEach(path -> {
                    String fileName = path.getFileName().toString();
                    FileAnalysisResult result = new FileAnalysisResult(fileName);
                    
                    // 查找XLS中的匹配
                    int foundRow = findXlsRowByFileName(result.getNameWithoutExt(), xlsData);
                    if (foundRow >= 0) {
                        result.setFoundInXls(true);
                        result.setXlsRowIndex(foundRow);
                        result.setXlsValue(xlsData[foundRow][2]);
                    }
                    
                    results.add(result);
                });
                
        } catch (IOException e) {
            System.out.println("分析文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        return results;
    }
    
    /**
     * 查找XLS行（与主工具相同的逻辑）
     */
    private int findXlsRowByFileName(String originalFileName, String[][] xlsData) {
        if (xlsData == null) return -1;
        
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                String xlsValue = xlsData[i][2];
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim();
                    
                    // 1. 直接匹配
                    if (xlsValue.equals(originalFileName)) {
                        return i;
                    }
                    
                    // 2. 数字匹配
                    if (originalFileName.matches("\\d+")) {
                        try {
                            if (xlsValue.matches("\\d+") && originalFileName.equals(xlsValue)) {
                                return i;
                            }
                            
                            if (xlsValue.startsWith("0x") || xlsValue.startsWith("0X")) {
                                String hexPart = xlsValue.substring(2);
                                long decimalValue = Long.parseLong(hexPart, 16);
                                if (originalFileName.equals(String.valueOf(decimalValue))) {
                                    return i;
                                }
                            }
                        } catch (Exception e) {
                            // 忽略转换错误
                        }
                    }
                }
            }
        }
        return -1;
    }
    
    /**
     * 生成报告
     */
    public void generateReport(List<FileAnalysisResult> results, String outputPath) {
        try (FileWriter writer = new FileWriter(outputPath)) {
            writer.write("皮肤文件分析报告\n");
            writer.write("生成时间: " + new java.util.Date() + "\n");
            writer.write("================================================================================\n\n");
            
            // 统计信息
            long totalFiles = results.size();
            long numericFiles = results.stream().filter(r -> "numeric".equals(r.getFileType())).count();
            long textFiles = results.stream().filter(r -> "text".equals(r.getFileType())).count();
            long convertedFiles = results.stream().filter(r -> "converted".equals(r.getFileType())).count();
            long foundInXls = results.stream().filter(FileAnalysisResult::isFoundInXls).count();
            long skippedFiles = results.stream().filter(r -> !r.isFoundInXls() && !"converted".equals(r.getFileType())).count();
            
            writer.write("统计信息:\n");
            writer.write("  总文件数: " + totalFiles + "\n");
            writer.write("  数字文件名: " + numericFiles + "\n");
            writer.write("  文本文件名: " + textFiles + "\n");
            writer.write("  已转换文件: " + convertedFiles + "\n");
            writer.write("  在XLS中找到: " + foundInXls + "\n");
            writer.write("  将被跳过: " + skippedFiles + "\n");
            writer.write("  跳过率: " + String.format("%.2f%%", skippedFiles * 100.0 / (totalFiles - convertedFiles)) + "\n\n");
            
            // 跳过的数字文件
            writer.write("跳过的数字文件名:\n");
            writer.write("--------------------------------------------------\n");
            results.stream()
                .filter(r -> "numeric".equals(r.getFileType()) && !r.isFoundInXls())
                .forEach(r -> {
                    try {
                        writer.write(r.getFileName() + "\n");
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            
            writer.write("\n跳过的文本文件名:\n");
            writer.write("--------------------------------------------------\n");
            results.stream()
                .filter(r -> "text".equals(r.getFileType()) && !r.isFoundInXls())
                .forEach(r -> {
                    try {
                        writer.write(r.getFileName() + "\n");
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            
            writer.write("\n找到匹配的文件 (前50个):\n");
            writer.write("================================================================================\n");
            writer.write(String.format("%-30s %-10s %-15s%n", "文件名", "XLS行号", "XLS值"));
            writer.write("================================================================================\n");
            results.stream()
                .filter(FileAnalysisResult::isFoundInXls)
                .limit(50)
                .forEach(r -> {
                    try {
                        writer.write(String.format("%-30s %-10d %-15s%n", 
                            r.getFileName(), r.getXlsRowIndex() + 1, r.getXlsValue()));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
            
            System.out.println("报告已生成: " + outputPath);
            
        } catch (IOException e) {
            System.out.println("生成报告时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 控制台输出简要报告
     */
    public void printSummary(List<FileAnalysisResult> results) {
        System.out.println("=== 文件分析摘要 ===");
        
        long totalFiles = results.size();
        long numericFiles = results.stream().filter(r -> "numeric".equals(r.getFileType())).count();
        long textFiles = results.stream().filter(r -> "text".equals(r.getFileType())).count();
        long convertedFiles = results.stream().filter(r -> "converted".equals(r.getFileType())).count();
        long foundInXls = results.stream().filter(FileAnalysisResult::isFoundInXls).count();
        long skippedFiles = results.stream().filter(r -> !r.isFoundInXls() && !"converted".equals(r.getFileType())).count();
        
        System.out.println("总文件数: " + totalFiles);
        System.out.println("  数字文件名: " + numericFiles);
        System.out.println("  文本文件名: " + textFiles);
        System.out.println("  已转换文件: " + convertedFiles);
        System.out.println("在XLS中找到: " + foundInXls);
        System.out.println("将被跳过: " + skippedFiles);
        System.out.printf("跳过率: %.2f%%%n", skippedFiles * 100.0 / (totalFiles - convertedFiles));
        
        System.out.println("\n跳过的数字文件示例 (前10个):");
        results.stream()
            .filter(r -> "numeric".equals(r.getFileType()) && !r.isFoundInXls())
            .limit(10)
            .forEach(r -> System.out.println("  " + r.getFileName()));
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        SkippedFilesReport report = new SkippedFilesReport();
        
        String xlsPath = DEFAULT_XLS_PATH;
        String itemFolder = DEFAULT_ITEM_FOLDER;
        String outputPath = "skipped_files_report.txt";
        
        if (args.length >= 1) xlsPath = args[0];
        if (args.length >= 2) itemFolder = args[1];
        if (args.length >= 3) outputPath = args[2];
        
        System.out.println("正在分析文件...");
        List<FileAnalysisResult> results = report.analyzeAllFiles(itemFolder, xlsPath);
        
        report.printSummary(results);
        report.generateReport(results, outputPath);
    }
}
