package com.tool.test;

import com.tool.wdf.*;
import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 优化后的WDF和NPK功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class OptimizedWdfAndNpkTest {
    
    private static final String TEST_FOLDER = "optimized_test";
    private static final String TEST_WDF = "optimized_test.wdf";
    private static final String TEST_NPK = "test.npk";
    
    /**
     * 创建大量测试文件来测试性能
     */
    public void createLargeTestSet() {
        System.out.println("=== 创建大量测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                // 清理旧的测试文件
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
            
            Files.createDirectories(testDir);
            System.out.println("创建测试目录: " + TEST_FOLDER);
            
            // 创建1000个测试文件来测试性能
            int fileCount = 1000;
            System.out.println("正在创建 " + fileCount + " 个测试文件...");
            
            for (int i = 0; i < fileCount; i++) {
                String hexId = String.format("%08X", i + 0x10000000);
                String fileName = "2025-" + hexId + (i % 2 == 0 ? ".png" : ".was");
                Path filePath = testDir.resolve(fileName);
                
                // 创建不同大小的文件内容
                StringBuilder content = new StringBuilder();
                content.append("Test file: ").append(fileName).append("\n");
                content.append("File ID: 0x").append(hexId).append("\n");
                content.append("File type: ").append(i % 2 == 0 ? "PNG Image" : "WAS Animation").append("\n");
                
                // 添加随机大小的内容
                int contentSize = 1000 + (i % 5000); // 1KB到6KB的文件
                for (int j = 0; j < contentSize / 50; j++) {
                    content.append("Content line ").append(j).append(" for file ").append(fileName).append("\n");
                }
                
                Files.write(filePath, content.toString().getBytes("UTF-8"));
                
                // 每100个文件显示一次进度
                if ((i + 1) % 100 == 0) {
                    System.out.println("已创建 " + (i + 1) + " 个文件...");
                }
            }
            
            System.out.println("测试文件创建完成，总计: " + fileCount + " 个文件");
            
        } catch (IOException e) {
            System.out.println("创建测试文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试优化后的WDF打包性能
     */
    public void testOptimizedWdfPacking() {
        System.out.println("\n=== 测试优化后的WDF打包性能 ===");
        
        Path testDir = Paths.get(TEST_FOLDER);
        if (!Files.exists(testDir)) {
            System.out.println("测试目录不存在，请先创建测试文件");
            return;
        }
        
        try {
            File sourceDir = testDir.toFile();
            File targetFile = new File(TEST_WDF);
            
            long startTime = System.currentTimeMillis();
            
            // 使用优化的文件收集方法
            List<WasData> wasDataList = new ArrayList<>();
            List<File> validFiles = new ArrayList<>();
            
            System.out.println("开始收集文件...");
            collectWdfFilesOptimized(sourceDir, wasDataList, validFiles);
            
            long collectTime = System.currentTimeMillis();
            System.out.println("文件收集完成，耗时: " + (collectTime - startTime) + "ms");
            System.out.println("找到符合条件的文件: " + wasDataList.size() + " 个");
            
            if (wasDataList.isEmpty()) {
                System.out.println("没有找到符合条件的文件");
                return;
            }
            
            // 计算文件偏移
            long dataOffset = 12L + (long)wasDataList.size() * 16L;
            for (int i = 0; i < wasDataList.size(); i++) {
                WasData wasData = wasDataList.get(i);
                wasData.setFileOffset(dataOffset);
                dataOffset += wasData.getFileSize();
            }
            
            // 执行打包
            System.out.println("开始WDF打包...");
            WdfTool.packWdfFile(sourceDir, targetFile, wasDataList, (progress, status) -> {
                if (progress % 100 == 0) {
                    System.out.println("打包进度: " + status);
                }
            });
            
            long endTime = System.currentTimeMillis();
            
            System.out.println("\n=== 性能测试结果 ===");
            System.out.println("文件收集耗时: " + (collectTime - startTime) + "ms");
            System.out.println("总打包耗时: " + (endTime - startTime) + "ms");
            System.out.println("平均每文件耗时: " + ((endTime - startTime) / (double)wasDataList.size()) + "ms");
            System.out.println("输出文件: " + targetFile.getAbsolutePath());
            System.out.println("文件数量: " + wasDataList.size());
            System.out.println("文件大小: " + DataTool.formatFileSize(targetFile.length()));
            
        } catch (Exception e) {
            System.out.println("WDF性能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 优化的文件收集方法
     */
    private void collectWdfFilesOptimized(File dir, List<WasData> wasDataList, List<File> validFiles) {
        File[] fileList = dir.listFiles();
        if (fileList == null) return;
        
        for (File file : fileList) {
            if (file.isDirectory()) {
                collectWdfFilesOptimized(file, wasDataList, validFiles);
            } else {
                String name = file.getName();
                String lowerName = name.toLowerCase();
                
                if (lowerName.endsWith(".png") || lowerName.endsWith(".was")) {
                    String baseName = name.substring(0, name.lastIndexOf('.'));
                    String[] parts = baseName.split("-");
                    
                    // 检查格式: 2025-XXXXXXXX
                    if (parts.length >= 2 && parts[0].equals("2025") && parts[1].matches("[0-9A-Fa-f]{8}")) {
                        try {
                            long id = Long.parseLong(parts[1], 16);
                            
                            WasData wasData = new WasData();
                            wasData.setId(id);
                            wasData.setFileSize(file.length());
                            wasData.setFileSpace(file.length());
                            
                            wasDataList.add(wasData);
                            validFiles.add(file);
                            
                        } catch (NumberFormatException e) {
                            // 忽略无效的十六进制ID
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 测试NPK文件分析功能
     */
    public void testNpkAnalysis() {
        System.out.println("\n=== 测试NPK文件分析功能 ===");
        
        // 创建一个模拟的NPK文件用于测试
        try {
            File testNpkFile = new File(TEST_NPK);
            
            // 创建一个简单的测试文件
            String content = "NPK Test File\n" +
                           "This is a mock NPK file for testing purposes\n" +
                           "Entry count: 6165\n" +
                           "Config: Westward Journey\n";
            Files.write(testNpkFile.toPath(), content.getBytes());
            
            System.out.println("创建模拟NPK文件: " + TEST_NPK);
            
            // 测试NPK头部信息
            NpkHeader header = new NpkHeader();
            header.setSignature("NPK");
            header.setEntryCount(6165);
            header.setUnknownVar(0);
            header.setEncryptionMode(0);
            header.setHashMode(0);
            header.setIndexOffset(0x7354149L);
            header.setConfigName("Westward Journey");
            header.setInfoSize(28);
            header.setDecryptionKey(150);
            
            System.out.println("\nNPK头部信息:");
            System.out.println(header.toString());
            
            // 测试NPK条目
            NpkEntry entry = new NpkEntry("test_file.png");
            entry.setOriginalSize(1024 * 1024); // 1MB
            entry.setCompressedSize(512 * 1024); // 512KB
            entry.setCompressionType(1);
            entry.setCrc32(0x12345678L);
            
            System.out.println("\nNPK条目示例:");
            System.out.println(entry.toString());
            
            System.out.println("NPK分析功能测试完成");
            
        } catch (Exception e) {
            System.out.println("NPK分析测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试缓冲区大小对性能的影响
     */
    public void testBufferSizePerformance() {
        System.out.println("\n=== 测试缓冲区大小性能影响 ===");
        
        // 创建一个大文件用于测试
        try {
            File testFile = new File("buffer_test.dat");
            byte[] data = new byte[1024 * 1024]; // 1MB数据
            for (int i = 0; i < data.length; i++) {
                data[i] = (byte) (i % 256);
            }
            Files.write(testFile.toPath(), data);
            
            System.out.println("创建测试文件: " + testFile.getName() + " (大小: " + DataTool.formatFileSize(testFile.length()) + ")");
            
            // 测试不同缓冲区大小
            int[] bufferSizes = {8192, 32768, 65536, 131072}; // 8KB, 32KB, 64KB, 128KB
            
            for (int bufferSize : bufferSizes) {
                long startTime = System.currentTimeMillis();
                
                // 模拟文件复制操作
                try (java.io.FileInputStream fis = new java.io.FileInputStream(testFile);
                     java.io.FileOutputStream fos = new java.io.FileOutputStream("buffer_test_copy.dat")) {
                    
                    byte[] buffer = new byte[bufferSize];
                    int bytesRead;
                    while ((bytesRead = fis.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                
                long endTime = System.currentTimeMillis();
                System.out.printf("缓冲区大小: %6d 字节, 耗时: %4d ms\n", bufferSize, (endTime - startTime));
            }
            
            // 清理测试文件
            testFile.delete();
            new File("buffer_test_copy.dat").delete();
            
            System.out.println("缓冲区性能测试完成");
            
        } catch (Exception e) {
            System.out.println("缓冲区性能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        try {
            // 删除测试目录
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
                System.out.println("删除测试目录: " + TEST_FOLDER);
            }
            
            // 删除测试文件
            String[] testFiles = {TEST_WDF, TEST_NPK};
            for (String fileName : testFiles) {
                File file = new File(fileName);
                if (file.exists()) {
                    if (file.delete()) {
                        System.out.println("删除: " + fileName);
                    }
                }
            }
            
            System.out.println("测试文件清理完成");
            
        } catch (IOException e) {
            System.out.println("清理测试文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        createLargeTestSet();
        testOptimizedWdfPacking();
        testNpkAnalysis();
        testBufferSizePerformance();
        
        System.out.println("\n=== 优化功能测试完成 ===");
        System.out.println("如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        OptimizedWdfAndNpkTest test = new OptimizedWdfAndNpkTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllTests();
        }
    }
}
