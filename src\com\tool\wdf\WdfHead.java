
package com.tool.wdf;


public class WdfHead {
    private long flag;
    private long fileSum;
    private long offset;
    private WasData[] wasDataList;

    public WdfHead() {
    }

    public long getFlag() {
        return this.flag;
    }

    public void setFlag(long flag) {
        this.flag = flag;
    }

    public long getFileSum() {
        return this.fileSum;
    }

    public void setFileSum(long fileSum) {
        this.fileSum = fileSum;
    }

    public long getOffset() {
        return this.offset;
    }

    public void setOffset(long offset) {
        this.offset = offset;
    }

    public WasData[] getWasDataList() {
        return this.wasDataList;
    }

    public void setWasDataList(WasData[] fileList) {
        this.wasDataList = fileList;
    }
}
