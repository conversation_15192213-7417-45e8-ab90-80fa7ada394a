package com.tool.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/**
 * 深度NPK文件结构分析器
 * 目标：找到真实的6165个条目和正确的文件名格式
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class DeepNpkStructureAnalyzer {
    
    private static final String REAL_NPK_PATH = "item.npk";
    
    /**
     * 深度分析NPK文件结构
     */
    public void analyzeDeepStructure() {
        System.out.println("=== 深度NPK文件结构分析 ===");
        System.out.println("目标: 找到6165个条目和正确的文件名格式");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(npkFile, "r")) {
            System.out.println("文件大小: " + formatFileSize(raf.length()));
            
            // 分析索引区域的真实结构
            analyzeIndexStructureDeep(raf);
            
            // 尝试不同的条目大小
            tryDifferentEntrySizes(raf);
            
            // 分析文件名存储方式
            analyzeFileNameStorage(raf);
            
            // 寻找十六进制文件名模式
            searchHexFileNamePattern(raf);
            
        } catch (IOException e) {
            System.out.println("分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 深度分析索引结构
     */
    private void analyzeIndexStructureDeep(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 深度索引结构分析 ---");
        
        long indexOffset = 0x7354149L;
        long fileSize = raf.length();
        long indexSize = fileSize - indexOffset;
        
        System.out.println("索引偏移: 0x" + Long.toHexString(indexOffset));
        System.out.println("索引大小: " + formatFileSize(indexSize));
        System.out.println("期望条目数: 6165");
        
        // 计算不同条目大小的可能性
        double[] possibleSizes = {32, 40, 48, 56, 64, 72, 80, 96, 128};
        
        for (double size : possibleSizes) {
            double calculatedEntries = indexSize / size;
            System.out.printf("条目大小 %.0f 字节 -> 条目数: %.0f\n", size, calculatedEntries);
            
            if (Math.abs(calculatedEntries - 6165) < 100) {
                System.out.println("  *** 可能匹配! ***");
            }
        }
        
        // 精确计算
        double exactSize = indexSize / 6165.0;
        System.out.printf("精确条目大小: %.2f 字节\n", exactSize);
    }
    
    /**
     * 尝试不同的条目大小
     */
    private void tryDifferentEntrySizes(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 尝试不同的条目大小 ---");
        
        long indexOffset = 0x7354149L;
        long indexSize = raf.length() - indexOffset;
        
        // 基于精确计算，尝试接近的整数大小
        int[] testSizes = {56, 57, 58, 59, 60};
        
        for (int entrySize : testSizes) {
            int maxEntries = (int)(indexSize / entrySize);
            System.out.println("\n测试条目大小: " + entrySize + " 字节 (最大条目数: " + maxEntries + ")");
            
            if (maxEntries >= 6165) {
                analyzeEntriesWithSize(raf, indexOffset, entrySize, Math.min(6165, maxEntries));
            }
        }
    }
    
    /**
     * 使用指定大小分析条目
     */
    private void analyzeEntriesWithSize(RandomAccessFile raf, long indexOffset, int entrySize, int entryCount) throws IOException {
        System.out.println("  分析前5个条目:");
        
        for (int i = 0; i < Math.min(5, entryCount); i++) {
            long entryPos = indexOffset + (i * entrySize);
            raf.seek(entryPos);
            
            System.out.println("    条目 " + i + " (位置: 0x" + Long.toHexString(entryPos) + "):");
            
            // 读取条目数据
            byte[] entryData = new byte[Math.min(entrySize, 32)];
            raf.read(entryData);
            
            // 显示十六进制
            System.out.print("      数据: ");
            for (int j = 0; j < Math.min(16, entryData.length); j++) {
                System.out.printf("%02X ", entryData[j] & 0xFF);
            }
            System.out.println();
            
            // 解析字段
            raf.seek(entryPos);
            long f1 = readInt32LE(raf) & 0xFFFFFFFFL;
            long f2 = readInt32LE(raf) & 0xFFFFFFFFL;
            long f3 = readInt32LE(raf) & 0xFFFFFFFFL;
            long f4 = readInt32LE(raf) & 0xFFFFFFFFL;
            
            System.out.println("      字段: " + Long.toHexString(f1) + " " + Long.toHexString(f2) + " " + Long.toHexString(f3) + " " + Long.toHexString(f4));
            
            // 检查是否有合理的偏移
            if (f2 < raf.length() && f3 > 0 && f3 < 10 * 1024 * 1024) {
                System.out.println("      可能: 偏移=0x" + Long.toHexString(f2) + ", 大小=" + f3);
                
                // 检查该位置的数据
                raf.seek(f2);
                byte[] data = new byte[8];
                raf.read(data);
                System.out.print("      数据头: ");
                for (byte b : data) {
                    System.out.printf("%02X ", b & 0xFF);
                }
                if (isPngHeader(data)) {
                    System.out.print(" <- PNG!");
                } else if (isZlibHeader(data)) {
                    System.out.print(" <- zlib压缩!");
                }
                System.out.println();
            }
        }
    }
    
    /**
     * 分析文件名存储方式
     */
    private void analyzeFileNameStorage(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 分析文件名存储方式 ---");
        
        // 搜索可能的文件名表
        long indexOffset = 0x7354149L;
        
        // 检查索引前是否有文件名表
        System.out.println("检查索引前的区域...");
        
        for (long pos = indexOffset - 1000; pos < indexOffset; pos += 100) {
            if (pos < 0) continue;
            
            raf.seek(pos);
            byte[] data = new byte[100];
            raf.read(data);
            
            // 查找可能的文件名模式
            String text = new String(data, "UTF-8");
            if (text.contains(".png") || text.contains("0x")) {
                System.out.println("在位置 0x" + Long.toHexString(pos) + " 发现可能的文件名:");
                System.out.println("  " + text.replaceAll("[\\x00-\\x1F\\x7F-\\x9F]", "."));
            }
        }
        
        // 检查索引后是否有文件名表
        System.out.println("检查索引后的区域...");
        
        long endOfIndex = indexOffset + (6165 * 57); // 假设57字节条目
        for (long pos = endOfIndex; pos < Math.min(endOfIndex + 10000, raf.length()); pos += 100) {
            raf.seek(pos);
            byte[] data = new byte[100];
            raf.read(data);
            
            String text = new String(data, "UTF-8");
            if (text.contains(".png") || text.contains("0x")) {
                System.out.println("在位置 0x" + Long.toHexString(pos) + " 发现可能的文件名:");
                System.out.println("  " + text.replaceAll("[\\x00-\\x1F\\x7F-\\x9F]", "."));
            }
        }
    }
    
    /**
     * 搜索十六进制文件名模式
     */
    private void searchHexFileNamePattern(RandomAccessFile raf) throws IOException {
        System.out.println("\n--- 搜索十六进制文件名模式 ---");
        System.out.println("寻找类似 '0x4d02ea73.png' 的模式...");
        
        // 搜索整个文件中的十六进制文件名模式
        byte[] buffer = new byte[8192];
        long position = 0;
        int foundCount = 0;
        
        while (position < raf.length() && foundCount < 10) {
            raf.seek(position);
            int bytesRead = raf.read(buffer);
            
            String text = new String(buffer, 0, bytesRead, "UTF-8");
            
            // 查找 0x[8位十六进制].png 模式
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("0x[0-9a-fA-F]{8}\\.png");
            java.util.regex.Matcher matcher = pattern.matcher(text);
            
            while (matcher.find()) {
                foundCount++;
                long filePos = position + matcher.start();
                System.out.println("找到十六进制文件名 #" + foundCount + " 在位置 0x" + Long.toHexString(filePos) + ": " + matcher.group());
                
                if (foundCount >= 10) break;
            }
            
            position += bytesRead - 20; // 重叠20字节以防模式跨越缓冲区边界
        }
        
        if (foundCount == 0) {
            System.out.println("未找到十六进制文件名模式，可能文件名存储方式不同");
            
            // 尝试查找其他模式
            System.out.println("尝试查找其他文件名模式...");
            searchAlternativeFileNamePatterns(raf);
        }
    }
    
    /**
     * 搜索其他文件名模式
     */
    private void searchAlternativeFileNamePatterns(RandomAccessFile raf) throws IOException {
        // 搜索 .png 扩展名
        byte[] buffer = new byte[8192];
        long position = 0;
        int foundCount = 0;
        
        while (position < raf.length() && foundCount < 20) {
            raf.seek(position);
            int bytesRead = raf.read(buffer);
            
            for (int i = 0; i <= bytesRead - 4; i++) {
                if (buffer[i] == '.' && 
                    buffer[i+1] == 'p' && 
                    buffer[i+2] == 'n' && 
                    buffer[i+3] == 'g') {
                    
                    foundCount++;
                    long filePos = position + i;
                    
                    // 尝试读取前面的文件名
                    int start = Math.max(0, i - 20);
                    int end = Math.min(bytesRead, i + 4);
                    
                    String context = new String(buffer, start, end - start, "UTF-8");
                    context = context.replaceAll("[\\x00-\\x1F\\x7F-\\x9F]", ".");
                    
                    System.out.println("找到.png #" + foundCount + " 在位置 0x" + Long.toHexString(filePos) + ": " + context);
                    
                    if (foundCount >= 20) break;
                }
            }
            
            position += bytesRead - 10;
        }
    }
    
    /**
     * 检查PNG文件头
     */
    private boolean isPngHeader(byte[] data) {
        if (data.length < 8) return false;
        return data[0] == (byte)0x89 && data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && data[7] == (byte)0x0A;
    }
    
    /**
     * 检查zlib文件头
     */
    private boolean isZlibHeader(byte[] data) {
        if (data.length < 2) return false;
        return (data[0] & 0xFF) == 0x78 && (data[1] & 0xFF) == 0x9C;
    }
    
    /**
     * 读取32位小端序整数
     */
    private int readInt32LE(RandomAccessFile raf) throws IOException {
        int b1 = raf.read();
        int b2 = raf.read();
        int b3 = raf.read();
        int b4 = raf.read();
        return b1 | (b2 << 8) | (b3 << 16) | (b4 << 24);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        DeepNpkStructureAnalyzer analyzer = new DeepNpkStructureAnalyzer();
        analyzer.analyzeDeepStructure();
    }
}
