package com.tool.skin;

import xsl.ReadExelTool;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * XLS调试工具 - 用于分析XLS文件内容和文件匹配情况
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class XlsDebugTool {
    
    private static final String DEFAULT_ITEM_FOLDER = "G:\\JXy2o\\GameClient3.0\\res\\item";
    private static final String DEFAULT_XLS_PATH = "G:\\JXy2o\\GameClient3.0\\res\\config\\item.xls";
    
    /**
     * 分析XLS文件内容
     */
    public void analyzeXlsContent(String xlsPath) {
        System.out.println("=== XLS文件内容分析 ===");
        System.out.println("文件路径: " + xlsPath);
        
        try {
            String[][] xlsData = ReadExelTool.getResult(xlsPath);
            if (xlsData == null) {
                System.out.println("错误: 无法读取XLS文件");
                return;
            }
            
            System.out.println("总行数: " + xlsData.length);
            
            // 分析第3列的数据类型
            int numericCount = 0;
            int hexCount = 0;
            int textCount = 0;
            int emptyCount = 0;
            
            System.out.println("\n前10行数据预览:");
            for (int i = 0; i < Math.min(10, xlsData.length); i++) {
                if (xlsData[i] != null && xlsData[i].length > 2) {
                    String value = xlsData[i][2];
                    System.out.printf("行%d: %s%n", i + 1, value);
                } else {
                    System.out.printf("行%d: (空或列数不足)%n", i + 1);
                }
            }
            
            System.out.println("\n第3列数据类型统计:");
            for (int i = 0; i < xlsData.length; i++) {
                if (xlsData[i] != null && xlsData[i].length > 2) {
                    String value = xlsData[i][2];
                    if (value == null || value.trim().isEmpty()) {
                        emptyCount++;
                    } else if (value.trim().matches("\\d+")) {
                        numericCount++;
                    } else if (value.trim().startsWith("0x") || value.trim().startsWith("0X")) {
                        hexCount++;
                    } else {
                        textCount++;
                    }
                } else {
                    emptyCount++;
                }
            }
            
            System.out.println("  数字格式: " + numericCount + " 行");
            System.out.println("  十六进制格式: " + hexCount + " 行");
            System.out.println("  文本格式: " + textCount + " 行");
            System.out.println("  空值: " + emptyCount + " 行");
            
        } catch (Exception e) {
            System.out.println("分析XLS文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 分析文件夹中的PNG文件
     */
    public List<String> analyzePngFiles(String itemFolder) {
        System.out.println("\n=== PNG文件分析 ===");
        System.out.println("文件夹路径: " + itemFolder);
        
        List<String> pngFiles = new ArrayList<>();
        
        try {
            Path folderPath = Paths.get(itemFolder);
            if (!Files.exists(folderPath)) {
                System.out.println("错误: 文件夹不存在");
                return pngFiles;
            }
            
            Files.list(folderPath)
                .filter(path -> path.toString().toLowerCase().endsWith(".png"))
                .forEach(path -> {
                    String fileName = path.getFileName().toString();
                    pngFiles.add(fileName);
                });
            
            System.out.println("总PNG文件数: " + pngFiles.size());
            
            // 分析文件名类型
            int numericFileCount = 0;
            int convertedFileCount = 0;
            int textFileCount = 0;
            
            for (String fileName : pngFiles) {
                String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                if (nameWithoutExt.matches("\\d+")) {
                    numericFileCount++;
                } else if (fileName.matches("^2025-[0-9A-Fa-f]{8}\\.png$")) {
                    convertedFileCount++;
                } else {
                    textFileCount++;
                }
            }
            
            System.out.println("  数字文件名: " + numericFileCount + " 个");
            System.out.println("  已转换文件: " + convertedFileCount + " 个");
            System.out.println("  文本文件名: " + textFileCount + " 个");
            
            // 显示一些示例
            System.out.println("\n数字文件名示例:");
            pngFiles.stream()
                .filter(name -> name.substring(0, name.lastIndexOf('.')).matches("\\d+"))
                .limit(10)
                .forEach(name -> System.out.println("  " + name));
                
        } catch (IOException e) {
            System.out.println("分析PNG文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        
        return pngFiles;
    }
    
    /**
     * 测试特定文件的匹配情况
     */
    public void testFileMatching(String xlsPath, List<String> testFiles) {
        System.out.println("\n=== 文件匹配测试 ===");
        
        try {
            String[][] xlsData = ReadExelTool.getResult(xlsPath);
            if (xlsData == null) {
                System.out.println("错误: 无法读取XLS文件");
                return;
            }
            
            for (String fileName : testFiles) {
                String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
                int foundRow = findXlsRowByFileName(nameWithoutExt, xlsData);
                
                if (foundRow >= 0) {
                    System.out.printf("文件: %-20s -> 匹配行%d, XLS值: %s%n", 
                        fileName, foundRow + 1, xlsData[foundRow][2]);
                } else {
                    System.out.printf("文件: %-20s -> 未找到匹配%n", fileName);
                }
            }
            
        } catch (Exception e) {
            System.out.println("测试文件匹配时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 查找XLS行的方法（与主工具相同的逻辑）
     */
    private int findXlsRowByFileName(String originalFileName, String[][] xlsData) {
        if (xlsData == null) return -1;
        
        for (int i = 0; i < xlsData.length; i++) {
            if (xlsData[i] != null && xlsData[i].length > 2) {
                String xlsValue = xlsData[i][2];
                if (xlsValue != null) {
                    xlsValue = xlsValue.trim();
                    
                    // 1. 直接匹配
                    if (xlsValue.equals(originalFileName)) {
                        return i;
                    }
                    
                    // 2. 数字匹配
                    if (originalFileName.matches("\\d+")) {
                        try {
                            if (xlsValue.matches("\\d+") && originalFileName.equals(xlsValue)) {
                                return i;
                            }
                            
                            if (xlsValue.startsWith("0x") || xlsValue.startsWith("0X")) {
                                String hexPart = xlsValue.substring(2);
                                long decimalValue = Long.parseLong(hexPart, 16);
                                if (originalFileName.equals(String.valueOf(decimalValue))) {
                                    return i;
                                }
                            }
                        } catch (Exception e) {
                            // 忽略转换错误
                        }
                    }
                }
            }
        }
        return -1;
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        XlsDebugTool tool = new XlsDebugTool();
        
        String xlsPath = DEFAULT_XLS_PATH;
        String itemFolder = DEFAULT_ITEM_FOLDER;
        
        // 如果提供了命令行参数，使用它们
        if (args.length >= 1) {
            xlsPath = args[0];
        }
        if (args.length >= 2) {
            itemFolder = args[1];
        }
        
        // 分析XLS文件
        tool.analyzeXlsContent(xlsPath);
        
        // 分析PNG文件
        List<String> pngFiles = tool.analyzePngFiles(itemFolder);
        
        // 测试特定文件的匹配
        List<String> testFiles = new ArrayList<>();
        testFiles.add("6123.png");
        testFiles.add("9134.png");
        testFiles.add("255.png");
        testFiles.add("weapon_sword.png");
        testFiles.add("armor_001.png");
        
        // 添加一些实际存在的数字文件进行测试
        pngFiles.stream()
            .filter(name -> name.substring(0, name.lastIndexOf('.')).matches("\\d+"))
            .limit(5)
            .forEach(testFiles::add);
            
        tool.testFileMatching(xlsPath, testFiles);
    }
}
