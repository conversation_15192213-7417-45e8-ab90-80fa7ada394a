package com.tool.was;

import com.tool.wdf.DataTool;
import com.tool.wdf.WasHead;
import com.tool.wdf.FrameData;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.function.Consumer;

/**
 * WAS文件解包工具
 */
public class WasExtractor {
    
    /**
     * 解包WAS文件
     * 
     * @param wasFile WAS文件
     * @param outputDir 输出目录
     * @param extractStatic 是否提取静态图像
     * @param extractAnimated 是否提取动态图像
     * @param createSubfolders 是否为每个WAS文件创建子文件夹
     * @param logger 日志回调
     * @return 提取的图像数量
     */
    public static int extractWasFile(File wasFile, File outputDir, boolean extractStatic, 
                                   boolean extractAnimated, boolean createSubfolders, 
                                   Consumer<String> logger) throws IOException {
        
        if (logger != null) {
            logger.accept("正在解析WAS文件: " + wasFile.getName());
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(wasFile, "r")) {
            // 读取WAS文件头
            WasHead wasHead = parseWasHead(raf);
            if (wasHead == null) {
                if (logger != null) {
                    logger.accept("无法解析WAS文件头: " + wasFile.getName());
                }
                return 0;
            }
            
            // 确定输出目录
            File targetDir = outputDir;
            if (createSubfolders) {
                String baseName = wasFile.getName();
                if (baseName.contains(".")) {
                    baseName = baseName.substring(0, baseName.lastIndexOf('.'));
                }
                targetDir = new File(outputDir, baseName);
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }
            }
            
            int extractedCount = 0;
            
            // 检查是否是静态图像（只有一帧）
            boolean isStatic = wasHead.getSpriteFrame() <= 1;
            
            if (isStatic && !extractStatic) {
                if (logger != null) {
                    logger.accept("跳过静态图像: " + wasFile.getName());
                }
                return 0;
            }
            
            if (!isStatic && !extractAnimated) {
                if (logger != null) {
                    logger.accept("跳过动态图像: " + wasFile.getName());
                }
                return 0;
            }
            
            // 提取帧数据
            FrameData[] frameDataList = wasHead.getFrameDataList();
            if (frameDataList != null && frameDataList.length > 0) {
                for (int i = 0; i < frameDataList.length; i++) {
                    FrameData frameData = frameDataList[i];
                    if (frameData != null) {
                        BufferedImage image = extractFrame(raf, wasHead, frameData, i);
                        if (image != null) {
                            String fileName;
                            if (isStatic) {
                                fileName = wasFile.getName().replace(".was", ".png");
                            } else {
                                fileName = wasFile.getName().replace(".was", "_frame_" + String.format("%03d", i) + ".png");
                            }
                            
                            File outputFile = new File(targetDir, fileName);
                            ImageIO.write(image, "PNG", outputFile);
                            extractedCount++;
                            
                            if (logger != null) {
                                logger.accept("提取帧 " + i + ": " + fileName);
                            }
                        }
                    }
                }
            } else {
                // 尝试直接提取整个图像
                BufferedImage image = extractDirectImage(raf, wasHead);
                if (image != null) {
                    String fileName = wasFile.getName().replace(".was", ".png");
                    File outputFile = new File(targetDir, fileName);
                    ImageIO.write(image, "PNG", outputFile);
                    extractedCount++;
                    
                    if (logger != null) {
                        logger.accept("提取图像: " + fileName);
                    }
                }
            }
            
            if (logger != null) {
                logger.accept("完成解析 " + wasFile.getName() + "，提取了 " + extractedCount + " 个图像");
            }
            
            return extractedCount;
        }
    }
    
    /**
     * 解析WAS文件头
     */
    private static WasHead parseWasHead(RandomAccessFile raf) throws IOException {
        raf.seek(0);

        // 读取文件头标识
        byte[] header = new byte[2];
        raf.read(header);

        String headerStr = new String(header);
        if (!"SP".equals(headerStr) && !"SH".equals(headerStr)) {
            return null; // 不是有效的WAS文件
        }

        WasHead wasHead = new WasHead();

        // 读取基本信息
        wasHead.setFlag(headerStr.equals("SP") ? 1 : 2);

        // 读取头部大小 (16位小端序)
        int headSize = readInt16LE(raf);
        wasHead.setHeadSize(headSize);

        // 读取方向数 (16位小端序)
        int directionNum = readInt16LE(raf);
        wasHead.setDirectionNum(directionNum);

        // 读取帧数 (16位小端序)
        int spriteFrame = readInt16LE(raf);
        wasHead.setSpriteFrame(spriteFrame);

        // 读取宽度和高度 (16位小端序)
        int spriteWidth = readInt16LE(raf);
        int spriteHeight = readInt16LE(raf);
        wasHead.setSpriteWidth(spriteWidth);
        wasHead.setSpriteHeight(spriteHeight);

        // 读取中心点 (16位小端序)
        int spriteCenterX = readInt16LE(raf);
        int spriteCenterY = readInt16LE(raf);
        wasHead.setSpriteCenterX(spriteCenterX);
        wasHead.setSpriteCenterY(spriteCenterY);

        // 现在我们已经读取了16字节的基本头部
        // 如果头部大小大于16，说明有额外数据（如调色板）
        if (headSize > 16) {
            // 跳过额外的头部数据（可能是调色板等）
            raf.seek(headSize);
        }

        // 对于简单的WAS文件，图像数据紧跟在头部后面
        // 创建一个简单的帧数据
        if (spriteFrame <= 1) {
            // 静态图像，创建单帧
            FrameData[] frameDataList = new FrameData[1];
            FrameData frameData = new FrameData();
            frameData.setOffset(headSize); // 图像数据从头部结束后开始
            frameData.setCenterX(spriteCenterX);
            frameData.setCenterY(spriteCenterY);
            frameData.setWidth(spriteWidth);
            frameData.setHeight(spriteHeight);
            frameDataList[0] = frameData;
            wasHead.setFrameDataList(frameDataList);
        } else {
            // 多帧动画，需要读取帧信息表
            FrameData[] frameDataList = new FrameData[spriteFrame];
            for (int i = 0; i < spriteFrame; i++) {
                FrameData frameData = new FrameData();

                // 读取帧偏移 (32位小端序)
                long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                frameData.setOffset(offset);

                // 读取帧中心点 (32位小端序)
                long centerX = readInt32LE(raf) & 0xFFFFFFFFL;
                long centerY = readInt32LE(raf) & 0xFFFFFFFFL;
                frameData.setCenterX(centerX);
                frameData.setCenterY(centerY);

                // 读取帧尺寸 (32位小端序)
                long width = readInt32LE(raf) & 0xFFFFFFFFL;
                long height = readInt32LE(raf) & 0xFFFFFFFFL;
                frameData.setWidth(width);
                frameData.setHeight(height);

                frameDataList[i] = frameData;
            }
            wasHead.setFrameDataList(frameDataList);
        }

        return wasHead;
    }

    /**
     * 读取16位小端序整数
     */
    private static int readInt16LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[2];
        raf.read(bytes);
        return (bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8);
    }

    /**
     * 读取32位小端序整数
     */
    private static int readInt32LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[4];
        raf.read(bytes);
        return (bytes[0] & 0xFF) |
               ((bytes[1] & 0xFF) << 8) |
               ((bytes[2] & 0xFF) << 16) |
               ((bytes[3] & 0xFF) << 24);
    }
    
    /**
     * 提取单帧图像
     */
    private static BufferedImage extractFrame(RandomAccessFile raf, WasHead wasHead, FrameData frameData, int frameIndex) throws IOException {
        // 跳转到帧数据位置
        raf.seek(frameData.getOffset());

        int width = (int) frameData.getWidth();
        int height = (int) frameData.getHeight();

        if (width <= 0 || height <= 0) {
            return null;
        }

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        try {
            // WAS文件通常使用8位索引颜色或简单的像素格式
            // 先尝试读取所有剩余数据
            long remainingBytes = raf.length() - raf.getFilePointer();
            int maxDataSize = (int) Math.min(remainingBytes, width * height * 4);

            if (maxDataSize <= 0) {
                return createPlaceholderImage(width, height, "No Data");
            }

            byte[] imageData = new byte[maxDataSize];
            int bytesRead = raf.read(imageData);

            if (bytesRead > 0) {
                // 尝试不同的像素格式
                if (tryExtractAsIndexed(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGB(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGBA(image, imageData, width, height)) {
                    return image;
                } else {
                    // 如果都失败，创建一个基于数据的简单图像
                    return createDataVisualization(imageData, width, height, "Frame " + frameIndex);
                }
            }
        } catch (Exception e) {
            // 如果读取失败，返回一个占位图像
            return createPlaceholderImage(width, height, "Error: " + e.getMessage());
        }

        return createPlaceholderImage(width, height, "Frame " + frameIndex);
    }

    /**
     * 尝试作为索引颜色提取
     */
    private static boolean tryExtractAsIndexed(BufferedImage image, byte[] data, int width, int height) {
        try {
            if (data.length < width * height) {
                return false;
            }

            // 创建一个简单的灰度调色板
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int index = y * width + x;
                    if (index < data.length) {
                        int colorValue = data[index] & 0xFF;
                        int rgb = (0xFF << 24) | (colorValue << 16) | (colorValue << 8) | colorValue;
                        image.setRGB(x, y, rgb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试作为RGB格式提取
     */
    private static boolean tryExtractAsRGB(BufferedImage image, byte[] data, int width, int height) {
        try {
            if (data.length < width * height * 3) {
                return false;
            }

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 3;
                    if (offset + 2 < data.length) {
                        int r = data[offset] & 0xFF;
                        int g = data[offset + 1] & 0xFF;
                        int b = data[offset + 2] & 0xFF;
                        int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, rgb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试作为RGBA格式提取
     */
    private static boolean tryExtractAsRGBA(BufferedImage image, byte[] data, int width, int height) {
        try {
            if (data.length < width * height * 4) {
                return false;
            }

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 4;
                    if (offset + 3 < data.length) {
                        int r = data[offset] & 0xFF;
                        int g = data[offset + 1] & 0xFF;
                        int b = data[offset + 2] & 0xFF;
                        int a = data[offset + 3] & 0xFF;
                        int argb = (a << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, argb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建数据可视化图像
     */
    private static BufferedImage createDataVisualization(byte[] data, int width, int height, String label) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        // 将原始数据可视化
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int index = y * width + x;
                if (index < data.length) {
                    int value = data[index] & 0xFF;
                    // 使用彩虹色谱来可视化数据
                    int hue = (value * 360) / 256;
                    java.awt.Color color = java.awt.Color.getHSBColor(hue / 360.0f, 1.0f, 1.0f);
                    image.setRGB(x, y, color.getRGB());
                } else {
                    image.setRGB(x, y, 0xFF000000); // 黑色
                }
            }
        }

        return image;
    }
    
    /**
     * 直接提取图像（用于简单的WAS文件）
     */
    private static BufferedImage extractDirectImage(RandomAccessFile raf, WasHead wasHead) throws IOException {
        int width = wasHead.getSpriteWidth();
        int height = wasHead.getSpriteHeight();

        if (width <= 0 || height <= 0) {
            return null;
        }

        // 跳过头部，直接读取图像数据
        raf.seek(wasHead.getHeadSize());

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        try {
            // 读取所有剩余数据
            long remainingBytes = raf.length() - raf.getFilePointer();
            int maxDataSize = (int) Math.min(remainingBytes, width * height * 4);

            if (maxDataSize <= 0) {
                return createPlaceholderImage(width, height, "No Data");
            }

            byte[] imageData = new byte[maxDataSize];
            int bytesRead = raf.read(imageData);

            if (bytesRead > 0) {
                // 尝试不同的像素格式
                if (tryExtractAsIndexed(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGB(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGBA(image, imageData, width, height)) {
                    return image;
                } else {
                    // 创建数据可视化
                    return createDataVisualization(imageData, width, height, "WAS Image");
                }
            }
        } catch (Exception e) {
            return createPlaceholderImage(width, height, "Error: " + e.getMessage());
        }

        return createPlaceholderImage(width, height, "WAS Image");
    }
    
    /**
     * 创建占位图像
     */
    private static BufferedImage createPlaceholderImage(int width, int height, String text) {
        BufferedImage image = new BufferedImage(Math.max(width, 100), Math.max(height, 50), BufferedImage.TYPE_INT_ARGB);
        java.awt.Graphics2D g2d = image.createGraphics();
        g2d.setColor(java.awt.Color.LIGHT_GRAY);
        g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
        g2d.setColor(java.awt.Color.BLACK);
        g2d.drawString(text, 10, 25);
        g2d.dispose();
        return image;
    }
    
    /**
     * 读取指定字节数
     */
    private static byte[] readBytes(RandomAccessFile raf, int count) throws IOException {
        byte[] bytes = new byte[count];
        raf.read(bytes);
        return bytes;
    }
}
