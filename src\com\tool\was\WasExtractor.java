package com.tool.was;

import com.tool.wdf.DataTool;
import com.tool.wdf.WasHead;
import com.tool.wdf.FrameData;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.function.Consumer;

/**
 * WAS文件解包工具
 */
public class WasExtractor {
    
    /**
     * 解包WAS文件
     * 
     * @param wasFile WAS文件
     * @param outputDir 输出目录
     * @param extractStatic 是否提取静态图像
     * @param extractAnimated 是否提取动态图像
     * @param createSubfolders 是否为每个WAS文件创建子文件夹
     * @param logger 日志回调
     * @return 提取的图像数量
     */
    public static int extractWasFile(File wasFile, File outputDir, boolean extractStatic, 
                                   boolean extractAnimated, boolean createSubfolders, 
                                   Consumer<String> logger) throws IOException {
        
        if (logger != null) {
            logger.accept("正在解析WAS文件: " + wasFile.getName());
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(wasFile, "r")) {
            // 读取WAS文件头
            WasHead wasHead = parseWasHead(raf);
            if (wasHead == null) {
                if (logger != null) {
                    logger.accept("无法解析WAS文件头: " + wasFile.getName());
                }
                return 0;
            }
            
            // 确定输出目录
            File targetDir = outputDir;
            if (createSubfolders) {
                String baseName = wasFile.getName();
                if (baseName.contains(".")) {
                    baseName = baseName.substring(0, baseName.lastIndexOf('.'));
                }
                targetDir = new File(outputDir, baseName);
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }
            }
            
            int extractedCount = 0;
            
            // 检查是否是静态图像（只有一帧）
            boolean isStatic = wasHead.getSpriteFrame() <= 1;
            
            if (isStatic && !extractStatic) {
                if (logger != null) {
                    logger.accept("跳过静态图像: " + wasFile.getName());
                }
                return 0;
            }
            
            if (!isStatic && !extractAnimated) {
                if (logger != null) {
                    logger.accept("跳过动态图像: " + wasFile.getName());
                }
                return 0;
            }
            
            // 提取帧数据
            FrameData[] frameDataList = wasHead.getFrameDataList();
            if (frameDataList != null && frameDataList.length > 0) {
                for (int i = 0; i < frameDataList.length; i++) {
                    FrameData frameData = frameDataList[i];
                    if (frameData != null) {
                        BufferedImage image = extractFrame(raf, wasHead, frameData, i);
                        if (image != null) {
                            String fileName;
                            if (isStatic) {
                                fileName = wasFile.getName().replace(".was", ".png");
                            } else {
                                fileName = wasFile.getName().replace(".was", "_frame_" + String.format("%03d", i) + ".png");
                            }
                            
                            File outputFile = new File(targetDir, fileName);
                            ImageIO.write(image, "PNG", outputFile);
                            extractedCount++;
                            
                            if (logger != null) {
                                logger.accept("提取帧 " + i + ": " + fileName);
                            }
                        }
                    }
                }
            } else {
                // 尝试直接提取整个图像
                BufferedImage image = extractDirectImage(raf, wasHead);
                if (image != null) {
                    String fileName = wasFile.getName().replace(".was", ".png");
                    File outputFile = new File(targetDir, fileName);
                    ImageIO.write(image, "PNG", outputFile);
                    extractedCount++;
                    
                    if (logger != null) {
                        logger.accept("提取图像: " + fileName);
                    }
                }
            }
            
            if (logger != null) {
                logger.accept("完成解析 " + wasFile.getName() + "，提取了 " + extractedCount + " 个图像");
            }
            
            return extractedCount;
        }
    }
    
    /**
     * 解析WAS文件头
     */
    private static WasHead parseWasHead(RandomAccessFile raf) throws IOException {
        raf.seek(0);
        
        // 读取文件头标识
        byte[] header = new byte[2];
        raf.read(header);
        
        String headerStr = new String(header);
        if (!"SP".equals(headerStr) && !"SH".equals(headerStr)) {
            return null; // 不是有效的WAS文件
        }
        
        WasHead wasHead = new WasHead();
        
        // 读取基本信息
        wasHead.setFlag(headerStr.equals("SP") ? 1 : 2);
        
        // 读取头部大小
        int headSize = DataTool.byte2int16(readBytes(raf, 2), 0);
        wasHead.setHeadSize(headSize);
        
        // 读取方向数
        int directionNum = DataTool.byte2int16(readBytes(raf, 2), 0);
        wasHead.setDirectionNum(directionNum);
        
        // 读取帧数
        int spriteFrame = DataTool.byte2int16(readBytes(raf, 2), 0);
        wasHead.setSpriteFrame(spriteFrame);
        
        // 读取宽度和高度
        int spriteWidth = DataTool.byte2int16(readBytes(raf, 2), 0);
        int spriteHeight = DataTool.byte2int16(readBytes(raf, 2), 0);
        wasHead.setSpriteWidth(spriteWidth);
        wasHead.setSpriteHeight(spriteHeight);
        
        // 读取中心点
        int spriteCenterX = DataTool.byte2int16(readBytes(raf, 2), 0);
        int spriteCenterY = DataTool.byte2int16(readBytes(raf, 2), 0);
        wasHead.setSpriteCenterX(spriteCenterX);
        wasHead.setSpriteCenterY(spriteCenterY);
        
        // 读取调色板（如果存在）
        if (headSize > 20) {
            int paletteSize = Math.min(256, (headSize - 20) / 4);
            int[] colorBoard = new int[paletteSize];
            for (int i = 0; i < paletteSize; i++) {
                colorBoard[i] = DataTool.byte2int32(readBytes(raf, 4), 0);
            }
            wasHead.setColorBoard(colorBoard);
        }
        
        // 读取帧数据信息
        if (spriteFrame > 0) {
            FrameData[] frameDataList = new FrameData[spriteFrame];
            for (int i = 0; i < spriteFrame; i++) {
                FrameData frameData = new FrameData();
                
                // 读取帧偏移
                long offset = DataTool.byte2uint32(readBytes(raf, 4), 0);
                frameData.setOffset(offset);
                
                // 读取帧中心点
                long centerX = DataTool.byte2uint32(readBytes(raf, 4), 0);
                long centerY = DataTool.byte2uint32(readBytes(raf, 4), 0);
                frameData.setCenterX(centerX);
                frameData.setCenterY(centerY);
                
                // 读取帧尺寸
                long width = DataTool.byte2uint32(readBytes(raf, 4), 0);
                long height = DataTool.byte2uint32(readBytes(raf, 4), 0);
                frameData.setWidth(width);
                frameData.setHeight(height);
                
                frameDataList[i] = frameData;
            }
            wasHead.setFrameDataList(frameDataList);
        }
        
        return wasHead;
    }
    
    /**
     * 提取单帧图像
     */
    private static BufferedImage extractFrame(RandomAccessFile raf, WasHead wasHead, FrameData frameData, int frameIndex) throws IOException {
        // 跳转到帧数据位置
        raf.seek(frameData.getOffset());
        
        int width = (int) frameData.getWidth();
        int height = (int) frameData.getHeight();
        
        if (width <= 0 || height <= 0) {
            return null;
        }
        
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        
        // 简化的图像数据读取（需要根据实际WAS格式调整）
        try {
            // 读取图像数据
            byte[] imageData = new byte[width * height * 4]; // 假设RGBA格式
            int bytesRead = raf.read(imageData);
            
            if (bytesRead > 0) {
                // 转换为BufferedImage
                for (int y = 0; y < height && y * width * 4 < bytesRead; y++) {
                    for (int x = 0; x < width && (y * width + x) * 4 + 3 < bytesRead; x++) {
                        int offset = (y * width + x) * 4;
                        int r = imageData[offset] & 0xFF;
                        int g = imageData[offset + 1] & 0xFF;
                        int b = imageData[offset + 2] & 0xFF;
                        int a = imageData[offset + 3] & 0xFF;
                        
                        int argb = (a << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, argb);
                    }
                }
            }
        } catch (Exception e) {
            // 如果读取失败，返回一个占位图像
            return createPlaceholderImage(width, height, "Frame " + frameIndex);
        }
        
        return image;
    }
    
    /**
     * 直接提取图像（用于简单的WAS文件）
     */
    private static BufferedImage extractDirectImage(RandomAccessFile raf, WasHead wasHead) throws IOException {
        int width = wasHead.getSpriteWidth();
        int height = wasHead.getSpriteHeight();
        
        if (width <= 0 || height <= 0) {
            return null;
        }
        
        // 跳过头部，直接读取图像数据
        raf.seek(wasHead.getHeadSize());
        
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        
        try {
            // 简化的图像数据读取
            byte[] imageData = new byte[width * height * 4];
            int bytesRead = raf.read(imageData);
            
            if (bytesRead > 0) {
                for (int y = 0; y < height && y * width * 4 < bytesRead; y++) {
                    for (int x = 0; x < width && (y * width + x) * 4 + 3 < bytesRead; x++) {
                        int offset = (y * width + x) * 4;
                        int r = imageData[offset] & 0xFF;
                        int g = imageData[offset + 1] & 0xFF;
                        int b = imageData[offset + 2] & 0xFF;
                        int a = imageData[offset + 3] & 0xFF;
                        
                        int argb = (a << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, argb);
                    }
                }
            }
        } catch (Exception e) {
            return createPlaceholderImage(width, height, "WAS Image");
        }
        
        return image;
    }
    
    /**
     * 创建占位图像
     */
    private static BufferedImage createPlaceholderImage(int width, int height, String text) {
        BufferedImage image = new BufferedImage(Math.max(width, 100), Math.max(height, 50), BufferedImage.TYPE_INT_ARGB);
        java.awt.Graphics2D g2d = image.createGraphics();
        g2d.setColor(java.awt.Color.LIGHT_GRAY);
        g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
        g2d.setColor(java.awt.Color.BLACK);
        g2d.drawString(text, 10, 25);
        g2d.dispose();
        return image;
    }
    
    /**
     * 读取指定字节数
     */
    private static byte[] readBytes(RandomAccessFile raf, int count) throws IOException {
        byte[] bytes = new byte[count];
        raf.read(bytes);
        return bytes;
    }
}
