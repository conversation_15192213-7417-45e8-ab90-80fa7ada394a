package com.tool.was;

import com.tool.wdf.DataTool;
import com.tool.wdf.WasHead;
import com.tool.wdf.FrameData;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.function.Consumer;

/**
 * WAS文件解包工具
 */
public class WasExtractor {
    
    /**
     * 解包WAS文件
     * 
     * @param wasFile WAS文件
     * @param outputDir 输出目录
     * @param extractStatic 是否提取静态图像
     * @param extractAnimated 是否提取动态图像
     * @param createSubfolders 是否为每个WAS文件创建子文件夹
     * @param logger 日志回调
     * @return 提取的图像数量
     */
    public static int extractWasFile(File wasFile, File outputDir, boolean extractStatic, 
                                   boolean extractAnimated, boolean createSubfolders, 
                                   Consumer<String> logger) throws IOException {
        
        if (logger != null) {
            logger.accept("正在解析WAS文件: " + wasFile.getName());
        }
        
        try (RandomAccessFile raf = new RandomAccessFile(wasFile, "r")) {
            // 读取WAS文件头
            WasHead wasHead = parseWasHead(raf);
            if (wasHead == null) {
                if (logger != null) {
                    logger.accept("无法解析WAS文件头: " + wasFile.getName());
                }
                return 0;
            }
            
            // 确定输出目录
            File targetDir = outputDir;
            if (createSubfolders) {
                String baseName = wasFile.getName();
                if (baseName.contains(".")) {
                    baseName = baseName.substring(0, baseName.lastIndexOf('.'));
                }
                targetDir = new File(outputDir, baseName);
                if (!targetDir.exists()) {
                    targetDir.mkdirs();
                }
            }
            
            int extractedCount = 0;
            
            // 检查是否是静态图像（只有一帧）
            boolean isStatic = wasHead.getSpriteFrame() <= 1;
            
            if (isStatic && !extractStatic) {
                if (logger != null) {
                    logger.accept("跳过静态图像: " + wasFile.getName());
                }
                return 0;
            }
            
            if (!isStatic && !extractAnimated) {
                if (logger != null) {
                    logger.accept("跳过动态图像: " + wasFile.getName());
                }
                return 0;
            }
            
            // 提取帧数据
            FrameData[] frameDataList = wasHead.getFrameDataList();
            if (frameDataList != null && frameDataList.length > 0) {
                for (int i = 0; i < frameDataList.length; i++) {
                    FrameData frameData = frameDataList[i];
                    if (frameData != null) {
                        BufferedImage image = extractFrame(raf, wasHead, frameData, i);
                        if (image != null) {
                            String fileName;
                            if (isStatic) {
                                fileName = wasFile.getName().replace(".was", ".png");
                            } else {
                                fileName = wasFile.getName().replace(".was", "_frame_" + String.format("%03d", i) + ".png");
                            }
                            
                            File outputFile = new File(targetDir, fileName);
                            ImageIO.write(image, "PNG", outputFile);
                            extractedCount++;
                            
                            if (logger != null) {
                                logger.accept("提取帧 " + i + ": " + fileName);
                            }
                        }
                    }
                }
            } else {
                // 尝试直接提取整个图像
                BufferedImage image = extractDirectImage(raf, wasHead);
                if (image != null) {
                    String fileName = wasFile.getName().replace(".was", ".png");
                    File outputFile = new File(targetDir, fileName);
                    ImageIO.write(image, "PNG", outputFile);
                    extractedCount++;
                    
                    if (logger != null) {
                        logger.accept("提取图像: " + fileName);
                    }
                }
            }
            
            if (logger != null) {
                logger.accept("完成解析 " + wasFile.getName() + "，提取了 " + extractedCount + " 个图像");
            }
            
            return extractedCount;
        }
    }
    
    /**
     * 解析WAS文件头
     */
    private static WasHead parseWasHead(RandomAccessFile raf) throws IOException {
        raf.seek(0);

        // 读取文件头标识
        byte[] header = new byte[2];
        raf.read(header);

        String headerStr = new String(header);
        if (!"SP".equals(headerStr) && !"SH".equals(headerStr)) {
            return null; // 不是有效的WAS文件
        }

        WasHead wasHead = new WasHead();

        // 读取基本信息
        wasHead.setFlag(headerStr.equals("SP") ? 1 : 2);

        // 读取头部大小 (16位小端序)
        int headSize = readInt16LE(raf);
        wasHead.setHeadSize(headSize);

        // 读取方向数 (16位小端序)
        int directionNum = readInt16LE(raf);
        wasHead.setDirectionNum(directionNum);

        // 读取帧数 (16位小端序)
        int spriteFrame = readInt16LE(raf);
        wasHead.setSpriteFrame(spriteFrame);

        // 读取宽度和高度 (16位小端序)
        int spriteWidth = readInt16LE(raf);
        int spriteHeight = readInt16LE(raf);
        wasHead.setSpriteWidth(spriteWidth);
        wasHead.setSpriteHeight(spriteHeight);

        // 读取中心点 (16位小端序)
        int spriteCenterX = readInt16LE(raf);
        int spriteCenterY = readInt16LE(raf);
        wasHead.setSpriteCenterX(spriteCenterX);
        wasHead.setSpriteCenterY(spriteCenterY);

        // 现在我们已经读取了16字节的基本头部
        // 如果头部大小大于16，说明有额外数据（如调色板）
        if (headSize > 16) {
            // 跳过额外的头部数据（可能是调色板等）
            raf.seek(headSize);
        }

        // 对于简单的WAS文件，图像数据紧跟在头部后面
        // 创建一个简单的帧数据
        if (spriteFrame <= 1) {
            // 静态图像，创建单帧
            FrameData[] frameDataList = new FrameData[1];
            FrameData frameData = new FrameData();
            frameData.setOffset(headSize); // 图像数据从头部结束后开始
            frameData.setCenterX(spriteCenterX);
            frameData.setCenterY(spriteCenterY);
            frameData.setWidth(spriteWidth);
            frameData.setHeight(spriteHeight);
            frameDataList[0] = frameData;
            wasHead.setFrameDataList(frameDataList);
        } else {
            // 多帧动画，需要读取帧信息表
            FrameData[] frameDataList = new FrameData[spriteFrame];
            for (int i = 0; i < spriteFrame; i++) {
                FrameData frameData = new FrameData();

                // 读取帧偏移 (32位小端序)
                long offset = readInt32LE(raf) & 0xFFFFFFFFL;
                frameData.setOffset(offset);

                // 读取帧中心点 (32位小端序)
                long centerX = readInt32LE(raf) & 0xFFFFFFFFL;
                long centerY = readInt32LE(raf) & 0xFFFFFFFFL;
                frameData.setCenterX(centerX);
                frameData.setCenterY(centerY);

                // 读取帧尺寸 (32位小端序)
                long width = readInt32LE(raf) & 0xFFFFFFFFL;
                long height = readInt32LE(raf) & 0xFFFFFFFFL;
                frameData.setWidth(width);
                frameData.setHeight(height);

                frameDataList[i] = frameData;
            }
            wasHead.setFrameDataList(frameDataList);
        }

        return wasHead;
    }

    /**
     * 读取16位小端序整数
     */
    private static int readInt16LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[2];
        raf.read(bytes);
        return (bytes[0] & 0xFF) | ((bytes[1] & 0xFF) << 8);
    }

    /**
     * 读取32位小端序整数
     */
    private static int readInt32LE(RandomAccessFile raf) throws IOException {
        byte[] bytes = new byte[4];
        raf.read(bytes);
        return (bytes[0] & 0xFF) |
               ((bytes[1] & 0xFF) << 8) |
               ((bytes[2] & 0xFF) << 16) |
               ((bytes[3] & 0xFF) << 24);
    }
    
    /**
     * 提取单帧图像
     */
    private static BufferedImage extractFrame(RandomAccessFile raf, WasHead wasHead, FrameData frameData, int frameIndex) throws IOException {
        // 跳转到帧数据位置
        raf.seek(frameData.getOffset());

        int width = (int) frameData.getWidth();
        int height = (int) frameData.getHeight();

        if (width <= 0 || height <= 0) {
            return null;
        }

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        try {
            // WAS文件通常使用8位索引颜色或简单的像素格式
            // 先尝试读取所有剩余数据
            long remainingBytes = raf.length() - raf.getFilePointer();
            int maxDataSize = (int) Math.min(remainingBytes, width * height * 4);

            if (maxDataSize <= 0) {
                return createPlaceholderImage(width, height, "No Data");
            }

            byte[] imageData = new byte[maxDataSize];
            int bytesRead = raf.read(imageData);

            if (bytesRead > 0) {
                // 首先尝试作为16位像素数据解析
                BufferedImage rgb565Image = tryExtractAsRGB565(imageData, width, height);
                if (rgb565Image != null) {
                    return rgb565Image;
                }

                // 然后尝试作为TGA格式解析
                BufferedImage tgaImage = tryExtractAsTGA(imageData);
                if (tgaImage != null) {
                    return tgaImage;
                }

                // 如果不是TGA，尝试其他格式
                if (tryExtractAsIndexed(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGB(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGBA(image, imageData, width, height)) {
                    return image;
                } else {
                    // 如果都失败，创建一个基于数据的简单图像
                    return createDataVisualization(imageData, width, height, "Frame " + frameIndex);
                }
            }
        } catch (Exception e) {
            // 如果读取失败，返回一个占位图像
            return createPlaceholderImage(width, height, "Error: " + e.getMessage());
        }

        return createPlaceholderImage(width, height, "Frame " + frameIndex);
    }

    /**
     * 尝试作为RGB565格式提取（16位像素）
     */
    private static BufferedImage tryExtractAsRGB565(byte[] data, int width, int height) {
        // 尝试多种16位格式
        BufferedImage result;

        // 方法1：小端序RGB565
        result = tryRGB565LittleEndian(data, width, height);
        if (result != null) return result;

        // 方法2：大端序RGB565
        result = tryRGB565BigEndian(data, width, height);
        if (result != null) return result;

        // 方法3：BGR565小端序
        result = tryBGR565LittleEndian(data, width, height);
        if (result != null) return result;

        // 方法4：BGR565大端序
        result = tryBGR565BigEndian(data, width, height);
        if (result != null) return result;

        return null;
    }

    /**
     * RGB565小端序
     */
    private static BufferedImage tryRGB565LittleEndian(byte[] data, int width, int height) {
        try {
            int expectedSize = width * height * 2;
            if (data.length < expectedSize) return null;

            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 2;
                    if (offset + 1 < data.length) {
                        int pixel16 = ((data[offset + 1] & 0xFF) << 8) | (data[offset] & 0xFF);

                        int r = (pixel16 >> 11) & 0x1F;
                        int g = (pixel16 >> 5) & 0x3F;
                        int b = pixel16 & 0x1F;

                        r = (r << 3) | (r >> 2);
                        g = (g << 2) | (g >> 4);
                        b = (b << 3) | (b >> 2);

                        int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, rgb);
                    }
                }
            }

            return image;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * RGB565大端序
     */
    private static BufferedImage tryRGB565BigEndian(byte[] data, int width, int height) {
        try {
            int expectedSize = width * height * 2;
            if (data.length < expectedSize) return null;

            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 2;
                    if (offset + 1 < data.length) {
                        int pixel16 = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);

                        int r = (pixel16 >> 11) & 0x1F;
                        int g = (pixel16 >> 5) & 0x3F;
                        int b = pixel16 & 0x1F;

                        r = (r << 3) | (r >> 2);
                        g = (g << 2) | (g >> 4);
                        b = (b << 3) | (b >> 2);

                        int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, rgb);
                    }
                }
            }

            return image;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * BGR565小端序
     */
    private static BufferedImage tryBGR565LittleEndian(byte[] data, int width, int height) {
        try {
            int expectedSize = width * height * 2;
            if (data.length < expectedSize) return null;

            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 2;
                    if (offset + 1 < data.length) {
                        int pixel16 = ((data[offset + 1] & 0xFF) << 8) | (data[offset] & 0xFF);

                        int b = (pixel16 >> 11) & 0x1F;  // 蓝色在高位
                        int g = (pixel16 >> 5) & 0x3F;   // 绿色在中间
                        int r = pixel16 & 0x1F;          // 红色在低位

                        r = (r << 3) | (r >> 2);
                        g = (g << 2) | (g >> 4);
                        b = (b << 3) | (b >> 2);

                        int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, rgb);
                    }
                }
            }

            return image;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * BGR565大端序
     */
    private static BufferedImage tryBGR565BigEndian(byte[] data, int width, int height) {
        try {
            int expectedSize = width * height * 2;
            if (data.length < expectedSize) return null;

            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 2;
                    if (offset + 1 < data.length) {
                        int pixel16 = ((data[offset] & 0xFF) << 8) | (data[offset + 1] & 0xFF);

                        int b = (pixel16 >> 11) & 0x1F;
                        int g = (pixel16 >> 5) & 0x3F;
                        int r = pixel16 & 0x1F;

                        r = (r << 3) | (r >> 2);
                        g = (g << 2) | (g >> 4);
                        b = (b << 3) | (b >> 2);

                        int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, rgb);
                    }
                }
            }

            return image;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 尝试作为索引颜色提取
     */
    private static boolean tryExtractAsIndexed(BufferedImage image, byte[] data, int width, int height) {
        try {
            if (data.length < width * height) {
                return false;
            }

            // 创建一个简单的灰度调色板
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int index = y * width + x;
                    if (index < data.length) {
                        int colorValue = data[index] & 0xFF;
                        int rgb = (0xFF << 24) | (colorValue << 16) | (colorValue << 8) | colorValue;
                        image.setRGB(x, y, rgb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试作为RGB格式提取
     */
    private static boolean tryExtractAsRGB(BufferedImage image, byte[] data, int width, int height) {
        try {
            if (data.length < width * height * 3) {
                return false;
            }

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 3;
                    if (offset + 2 < data.length) {
                        int r = data[offset] & 0xFF;
                        int g = data[offset + 1] & 0xFF;
                        int b = data[offset + 2] & 0xFF;
                        int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, rgb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试作为RGBA格式提取
     */
    private static boolean tryExtractAsRGBA(BufferedImage image, byte[] data, int width, int height) {
        try {
            if (data.length < width * height * 4) {
                return false;
            }

            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int offset = (y * width + x) * 4;
                    if (offset + 3 < data.length) {
                        int r = data[offset] & 0xFF;
                        int g = data[offset + 1] & 0xFF;
                        int b = data[offset + 2] & 0xFF;
                        int a = data[offset + 3] & 0xFF;
                        int argb = (a << 24) | (r << 16) | (g << 8) | b;
                        image.setRGB(x, y, argb);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试作为TGA格式提取
     */
    private static BufferedImage tryExtractAsTGA(byte[] data) {
        try {
            if (data.length < 18) {
                return null; // TGA头部至少需要18字节
            }

            // 解析TGA头部
            int idLength = data[0] & 0xFF;
            int colorMapType = data[1] & 0xFF;
            int imageType = data[2] & 0xFF;

            // 颜色映射表信息（5字节）
            int colorMapStart = ((data[4] & 0xFF) << 8) | (data[3] & 0xFF);
            int colorMapLength = ((data[6] & 0xFF) << 8) | (data[5] & 0xFF);
            int colorMapDepth = data[7] & 0xFF;

            // 图像规格（10字节）
            int xOrigin = ((data[9] & 0xFF) << 8) | (data[8] & 0xFF);
            int yOrigin = ((data[11] & 0xFF) << 8) | (data[10] & 0xFF);
            int width = ((data[13] & 0xFF) << 8) | (data[12] & 0xFF);
            int height = ((data[15] & 0xFF) << 8) | (data[14] & 0xFF);
            int pixelDepth = data[16] & 0xFF;
            int imageDescriptor = data[17] & 0xFF;

            if (width <= 0 || height <= 0 || width > 2048 || height > 2048) {
                return null; // 无效的图像尺寸
            }

            // 跳过图像ID字段
            int dataOffset = 18 + idLength;

            // 跳过颜色映射表
            if (colorMapType == 1) {
                dataOffset += colorMapLength * (colorMapDepth / 8);
            }

            if (dataOffset >= data.length) {
                return null;
            }

            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

            // 根据图像类型和像素深度解析数据
            if (imageType == 2) { // 未压缩真彩色
                if (pixelDepth == 24) {
                    return parseTGA24(data, dataOffset, width, height, imageDescriptor);
                } else if (pixelDepth == 32) {
                    return parseTGA32(data, dataOffset, width, height, imageDescriptor);
                }
            } else if (imageType == 10) { // RLE压缩真彩色
                if (pixelDepth == 24) {
                    return parseTGARLE24(data, dataOffset, width, height, imageDescriptor);
                } else if (pixelDepth == 32) {
                    return parseTGARLE32(data, dataOffset, width, height, imageDescriptor);
                }
            }

            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 解析24位未压缩TGA
     */
    private static BufferedImage parseTGA24(byte[] data, int offset, int width, int height, int imageDescriptor) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        boolean flipVertical = (imageDescriptor & 0x20) == 0; // 检查Y方向

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int pixelOffset = offset + (y * width + x) * 3;
                if (pixelOffset + 2 < data.length) {
                    int b = data[pixelOffset] & 0xFF;
                    int g = data[pixelOffset + 1] & 0xFF;
                    int r = data[pixelOffset + 2] & 0xFF;

                    int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;

                    int targetY = flipVertical ? (height - 1 - y) : y;
                    image.setRGB(x, targetY, rgb);
                }
            }
        }

        return image;
    }

    /**
     * 解析32位未压缩TGA
     */
    private static BufferedImage parseTGA32(byte[] data, int offset, int width, int height, int imageDescriptor) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        boolean flipVertical = (imageDescriptor & 0x20) == 0;

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int pixelOffset = offset + (y * width + x) * 4;
                if (pixelOffset + 3 < data.length) {
                    int b = data[pixelOffset] & 0xFF;
                    int g = data[pixelOffset + 1] & 0xFF;
                    int r = data[pixelOffset + 2] & 0xFF;
                    int a = data[pixelOffset + 3] & 0xFF;

                    int argb = (a << 24) | (r << 16) | (g << 8) | b;

                    int targetY = flipVertical ? (height - 1 - y) : y;
                    image.setRGB(x, targetY, argb);
                }
            }
        }

        return image;
    }

    /**
     * 解析24位RLE压缩TGA
     */
    private static BufferedImage parseTGARLE24(byte[] data, int offset, int width, int height, int imageDescriptor) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        boolean flipVertical = (imageDescriptor & 0x20) == 0;
        int pixelCount = width * height;
        int currentPixel = 0;
        int dataIndex = offset;

        while (currentPixel < pixelCount && dataIndex < data.length) {
            int packetHeader = data[dataIndex++] & 0xFF;
            int packetSize = (packetHeader & 0x7F) + 1;

            if ((packetHeader & 0x80) != 0) {
                // RLE包
                if (dataIndex + 2 < data.length) {
                    int b = data[dataIndex++] & 0xFF;
                    int g = data[dataIndex++] & 0xFF;
                    int r = data[dataIndex++] & 0xFF;
                    int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;

                    for (int i = 0; i < packetSize && currentPixel < pixelCount; i++) {
                        int x = currentPixel % width;
                        int y = currentPixel / width;
                        int targetY = flipVertical ? (height - 1 - y) : y;
                        image.setRGB(x, targetY, rgb);
                        currentPixel++;
                    }
                }
            } else {
                // 原始包
                for (int i = 0; i < packetSize && currentPixel < pixelCount && dataIndex + 2 < data.length; i++) {
                    int b = data[dataIndex++] & 0xFF;
                    int g = data[dataIndex++] & 0xFF;
                    int r = data[dataIndex++] & 0xFF;
                    int rgb = (0xFF << 24) | (r << 16) | (g << 8) | b;

                    int x = currentPixel % width;
                    int y = currentPixel / width;
                    int targetY = flipVertical ? (height - 1 - y) : y;
                    image.setRGB(x, targetY, rgb);
                    currentPixel++;
                }
            }
        }

        return image;
    }

    /**
     * 解析32位RLE压缩TGA
     */
    private static BufferedImage parseTGARLE32(byte[] data, int offset, int width, int height, int imageDescriptor) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        boolean flipVertical = (imageDescriptor & 0x20) == 0;
        int pixelCount = width * height;
        int currentPixel = 0;
        int dataIndex = offset;

        while (currentPixel < pixelCount && dataIndex < data.length) {
            int packetHeader = data[dataIndex++] & 0xFF;
            int packetSize = (packetHeader & 0x7F) + 1;

            if ((packetHeader & 0x80) != 0) {
                // RLE包
                if (dataIndex + 3 < data.length) {
                    int b = data[dataIndex++] & 0xFF;
                    int g = data[dataIndex++] & 0xFF;
                    int r = data[dataIndex++] & 0xFF;
                    int a = data[dataIndex++] & 0xFF;
                    int argb = (a << 24) | (r << 16) | (g << 8) | b;

                    for (int i = 0; i < packetSize && currentPixel < pixelCount; i++) {
                        int x = currentPixel % width;
                        int y = currentPixel / width;
                        int targetY = flipVertical ? (height - 1 - y) : y;
                        image.setRGB(x, targetY, argb);
                        currentPixel++;
                    }
                }
            } else {
                // 原始包
                for (int i = 0; i < packetSize && currentPixel < pixelCount && dataIndex + 3 < data.length; i++) {
                    int b = data[dataIndex++] & 0xFF;
                    int g = data[dataIndex++] & 0xFF;
                    int r = data[dataIndex++] & 0xFF;
                    int a = data[dataIndex++] & 0xFF;
                    int argb = (a << 24) | (r << 16) | (g << 8) | b;

                    int x = currentPixel % width;
                    int y = currentPixel / width;
                    int targetY = flipVertical ? (height - 1 - y) : y;
                    image.setRGB(x, targetY, argb);
                    currentPixel++;
                }
            }
        }

        return image;
    }

    /**
     * 创建数据可视化图像
     */
    private static BufferedImage createDataVisualization(byte[] data, int width, int height, String label) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        // 将原始数据可视化
        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int index = y * width + x;
                if (index < data.length) {
                    int value = data[index] & 0xFF;
                    // 使用彩虹色谱来可视化数据
                    int hue = (value * 360) / 256;
                    java.awt.Color color = java.awt.Color.getHSBColor(hue / 360.0f, 1.0f, 1.0f);
                    image.setRGB(x, y, color.getRGB());
                } else {
                    image.setRGB(x, y, 0xFF000000); // 黑色
                }
            }
        }

        return image;
    }
    
    /**
     * 直接提取图像（用于简单的WAS文件）
     */
    private static BufferedImage extractDirectImage(RandomAccessFile raf, WasHead wasHead) throws IOException {
        int width = wasHead.getSpriteWidth();
        int height = wasHead.getSpriteHeight();

        if (width <= 0 || height <= 0) {
            return null;
        }

        // 跳过头部，直接读取图像数据
        raf.seek(wasHead.getHeadSize());

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);

        try {
            // 读取所有剩余数据
            long remainingBytes = raf.length() - raf.getFilePointer();
            int maxDataSize = (int) Math.min(remainingBytes, width * height * 4);

            if (maxDataSize <= 0) {
                return createPlaceholderImage(width, height, "No Data");
            }

            byte[] imageData = new byte[maxDataSize];
            int bytesRead = raf.read(imageData);

            if (bytesRead > 0) {
                // 首先尝试作为16位像素数据解析
                BufferedImage rgb565Image = tryExtractAsRGB565(imageData, width, height);
                if (rgb565Image != null) {
                    return rgb565Image;
                }

                // 然后尝试作为TGA格式解析
                BufferedImage tgaImage = tryExtractAsTGA(imageData);
                if (tgaImage != null) {
                    return tgaImage;
                }

                // 尝试其他像素格式
                if (tryExtractAsIndexed(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGB(image, imageData, width, height)) {
                    return image;
                } else if (tryExtractAsRGBA(image, imageData, width, height)) {
                    return image;
                } else {
                    // 创建数据可视化
                    return createDataVisualization(imageData, width, height, "WAS Image");
                }
            }
        } catch (Exception e) {
            return createPlaceholderImage(width, height, "Error: " + e.getMessage());
        }

        return createPlaceholderImage(width, height, "WAS Image");
    }
    
    /**
     * 创建占位图像
     */
    private static BufferedImage createPlaceholderImage(int width, int height, String text) {
        BufferedImage image = new BufferedImage(Math.max(width, 100), Math.max(height, 50), BufferedImage.TYPE_INT_ARGB);
        java.awt.Graphics2D g2d = image.createGraphics();
        g2d.setColor(java.awt.Color.LIGHT_GRAY);
        g2d.fillRect(0, 0, image.getWidth(), image.getHeight());
        g2d.setColor(java.awt.Color.BLACK);
        g2d.drawString(text, 10, 25);
        g2d.dispose();
        return image;
    }
    
    /**
     * 读取指定字节数
     */
    private static byte[] readBytes(RandomAccessFile raf, int count) throws IOException {
        byte[] bytes = new byte[count];
        raf.read(bytes);
        return bytes;
    }
}
