package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 真实NPK文件提取测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class RealNpkExtractionTest {
    
    private static final String REAL_NPK_PATH = "item.npk";
    private static final String EXTRACT_DIR = "extracted_real_png";
    
    /**
     * 测试真实NPK文件的PNG提取
     */
    public void testRealNpkExtraction() {
        System.out.println("=== 测试真实NPK文件的PNG提取 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在于根目录");
            return;
        }
        
        try {
            // 创建提取目录
            File extractDir = new File(EXTRACT_DIR);
            if (extractDir.exists()) {
                // 清理旧文件
                deleteDirectory(extractDir);
            }
            extractDir.mkdirs();
            
            System.out.println("开始解析NPK文件...");
            
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                if (progress % 10 == 0 || progress == 100) {
                    System.out.println("解析进度 " + progress + "%: " + status);
                }
            });
            
            if (parsedFile == null) {
                System.out.println("NPK文件解析失败");
                return;
            }
            
            List<NpkEntry> allEntries = parsedFile.getEntries();
            System.out.println("解析成功，找到 " + allEntries.size() + " 个文件");
            
            // 测试提取前几个文件
            System.out.println("\n--- 测试提取前10个文件 ---");
            int successCount = 0;
            int errorCount = 0;
            
            for (int i = 0; i < Math.min(10, allEntries.size()); i++) {
                NpkEntry entry = allEntries.get(i);
                
                try {
                    System.out.println("提取文件 " + (i + 1) + ": " + entry.getFileName());
                    System.out.println("  偏移: 0x" + Long.toHexString(entry.getOffset()));
                    System.out.println("  大小: " + formatFileSize(entry.getOriginalSize()));
                    
                    // 检查偏移和大小的合理性
                    if (entry.getOffset() >= npkFile.length()) {
                        System.out.println("  ✗ 偏移超出文件范围");
                        errorCount++;
                        continue;
                    }
                    
                    if (entry.getOriginalSize() > 100 * 1024 * 1024) { // 100MB
                        System.out.println("  ✗ 文件过大，跳过");
                        errorCount++;
                        continue;
                    }
                    
                    if (entry.getOriginalSize() <= 0) {
                        System.out.println("  ✗ 文件大小无效");
                        errorCount++;
                        continue;
                    }
                    
                    // 尝试提取文件
                    byte[] fileData = NpkTool.extractFile(parsedFile, entry, null);
                    
                    if (fileData != null && fileData.length > 0) {
                        // 验证PNG文件头
                        if (isPngData(fileData)) {
                            // 保存文件
                            File outputFile = new File(extractDir, entry.getFileName());
                            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(outputFile)) {
                                fos.write(fileData);
                            }
                            
                            System.out.println("  ✓ 成功提取: " + fileData.length + " 字节 (有效PNG)");
                            successCount++;
                        } else {
                            System.out.println("  ✗ 提取的数据不是有效的PNG文件");
                            errorCount++;
                        }
                    } else {
                        System.out.println("  ✗ 提取失败: 数据为空");
                        errorCount++;
                    }
                    
                } catch (Exception e) {
                    System.out.println("  ✗ 提取异常: " + e.getMessage());
                    errorCount++;
                }
            }
            
            System.out.println("\n--- 测试结果 ---");
            System.out.println("成功提取: " + successCount + " 个");
            System.out.println("提取失败: " + errorCount + " 个");
            System.out.println("成功率: " + (successCount * 100.0 / (successCount + errorCount)) + "%");
            
            if (successCount > 0) {
                System.out.println("提取的文件保存在: " + extractDir.getAbsolutePath());
                
                // 显示提取的文件信息
                File[] extractedFiles = extractDir.listFiles();
                if (extractedFiles != null) {
                    System.out.println("提取的文件列表:");
                    for (File file : extractedFiles) {
                        System.out.println("  - " + file.getName() + " (" + formatFileSize(file.length()) + ")");
                    }
                }
            }
            
        } catch (Exception e) {
            System.out.println("真实NPK提取测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试批量PNG提取性能
     */
    public void testBatchPngExtraction() {
        System.out.println("\n=== 测试批量PNG提取性能 ===");
        
        File npkFile = new File(REAL_NPK_PATH);
        if (!npkFile.exists()) {
            System.out.println("错误: item.npk文件不存在");
            return;
        }
        
        try {
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, null);
            if (parsedFile == null) {
                System.out.println("NPK文件解析失败");
                return;
            }
            
            // 创建提取目录
            File extractDir = new File(EXTRACT_DIR + "_batch");
            if (extractDir.exists()) {
                deleteDirectory(extractDir);
            }
            extractDir.mkdirs();
            
            System.out.println("开始批量PNG提取测试...");
            long startTime = System.currentTimeMillis();
            
            // 使用高性能PNG提取方法
            int extractedCount = NpkTool.extractPngFiles(parsedFile, extractDir.getAbsolutePath(), (progress, status) -> {
                if (progress % 10 == 0 || progress == 100) {
                    System.out.println("提取进度 " + progress + "%: " + status);
                }
            });
            
            long endTime = System.currentTimeMillis();
            long extractTime = endTime - startTime;
            
            System.out.println("\n--- 批量提取结果 ---");
            System.out.println("提取耗时: " + extractTime + "ms (" + (extractTime / 1000.0) + "秒)");
            System.out.println("成功提取: " + extractedCount + " 个PNG文件");
            
            if (extractedCount > 0) {
                System.out.println("平均每文件耗时: " + (extractTime / (double)extractedCount) + "ms");
                System.out.println("提取速度: " + (extractedCount * 1000.0 / extractTime) + " 文件/秒");
                
                // 验证提取的文件
                File[] extractedFiles = extractDir.listFiles();
                if (extractedFiles != null) {
                    System.out.println("实际提取的文件数: " + extractedFiles.length);
                    
                    long totalSize = 0;
                    for (File file : extractedFiles) {
                        totalSize += file.length();
                    }
                    System.out.println("总文件大小: " + formatFileSize(totalSize));
                }
            }
            
        } catch (Exception e) {
            System.out.println("批量PNG提取测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证PNG文件数据
     */
    private boolean isPngData(byte[] data) {
        if (data == null || data.length < 8) {
            return false;
        }
        
        // PNG文件头: 89 50 4E 47 0D 0A 1A 0A
        return data[0] == (byte)0x89 && 
               data[1] == (byte)0x50 && 
               data[2] == (byte)0x4E && 
               data[3] == (byte)0x47 && 
               data[4] == (byte)0x0D && 
               data[5] == (byte)0x0A && 
               data[6] == (byte)0x1A && 
               data[7] == (byte)0x0A;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }
    
    /**
     * 删除目录及其内容
     */
    private void deleteDirectory(File dir) {
        if (dir.exists()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            dir.delete();
        }
    }
    
    /**
     * 运行所有提取测试
     */
    public void runAllExtractionTests() {
        System.out.println("=== 真实NPK文件提取测试 ===");
        System.out.println("目标: 验证修复后的NPK解析和PNG提取功能");
        System.out.println("期望: 成功提取6165个PNG文件");
        
        testRealNpkExtraction();
        testBatchPngExtraction();
        
        System.out.println("\n=== 提取测试完成 ===");
        System.out.println("如果测试成功，说明NPK解析修复有效");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        RealNpkExtractionTest test = new RealNpkExtractionTest();
        test.runAllExtractionTests();
    }
}
