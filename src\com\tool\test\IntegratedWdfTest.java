package com.tool.test;

import com.tool.wdf.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 整合后的WDF功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class IntegratedWdfTest {
    
    private static final String TEST_FOLDER = "integrated_wdf_test";
    private static final String TEST_WDF = "integrated_test.wdf";
    
    /**
     * 创建符合WDF格式的测试文件
     */
    public void createWdfTestFiles() {
        System.out.println("=== 创建WDF格式测试文件 ===");
        
        try {
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                // 清理旧的测试文件
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
            
            Files.createDirectories(testDir);
            System.out.println("创建测试目录: " + TEST_FOLDER);
            
            // 创建符合WDF格式的测试文件 (2025-XXXXXXXX.png/was)
            String[] testFiles = {
                "2025-A1B2C3D4.png",
                "2025-F5E6D7C8.was", 
                "2025-12345678.png",
                "2025-ABCDEF01.was",
                "2025-87654321.png"
            };
            
            for (String fileName : testFiles) {
                Path filePath = testDir.resolve(fileName);
                
                // 创建文件内容 - 模拟图像或WAS文件
                StringBuilder content = new StringBuilder();
                content.append("WDF Test File: ").append(fileName).append("\n");
                content.append("File Type: ").append(fileName.endsWith(".png") ? "PNG Image" : "WAS Animation").append("\n");
                content.append("Created for WDF integration test\n");
                
                // 添加一些填充内容使文件有一定大小
                for (int i = 0; i < 50; i++) {
                    content.append("Data line ").append(i).append(": ").append(fileName).append("\n");
                }
                
                Files.write(filePath, content.toString().getBytes("UTF-8"));
                System.out.println("创建测试文件: " + fileName + " (大小: " + DataTool.formatFileSize(Files.size(filePath)) + ")");
            }
            
            // 创建一些不符合格式的文件（应该被忽略）
            String[] invalidFiles = {
                "normal_image.png",
                "2024-12345678.png",  // 错误的年份前缀
                "2025-123.png",       // ID长度不对
                "readme.txt"
            };
            
            for (String fileName : invalidFiles) {
                Path filePath = testDir.resolve(fileName);
                String content = "Invalid file: " + fileName + "\nShould be ignored during WDF packing\n";
                Files.write(filePath, content.getBytes("UTF-8"));
                System.out.println("创建无效文件: " + fileName + " (应被忽略)");
            }
            
            System.out.println("测试文件创建完成");
            
        } catch (IOException e) {
            System.out.println("创建测试文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WDF打包功能
     */
    public void testWdfPacking() {
        System.out.println("\n=== 测试WDF打包功能 ===");
        
        Path testDir = Paths.get(TEST_FOLDER);
        if (!Files.exists(testDir)) {
            System.out.println("测试目录不存在，请先创建测试文件");
            return;
        }
        
        try {
            File sourceDir = testDir.toFile();
            File targetFile = new File(TEST_WDF);
            
            // 收集符合条件的文件
            List<File> files = new ArrayList<>();
            collectWdfFiles(sourceDir, files);
            
            System.out.println("找到符合条件的文件: " + files.size() + " 个");
            for (File file : files) {
                System.out.println("  - " + file.getName());
            }
            
            if (files.isEmpty()) {
                System.out.println("没有找到符合条件的文件");
                return;
            }
            
            // 创建WasData列表
            List<WasData> wasDataList = new ArrayList<>();
            long dataOffset = 12L + (long)files.size() * 16L;
            
            for (File file : files) {
                WasData wasData = new WasData();
                String fileName = file.getName();
                String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
                String[] nameParts = baseName.split("-");
                
                if (nameParts.length >= 2) {
                    try {
                        long id = Long.parseLong(nameParts[1], 16);
                        wasData.setId(id);
                        wasData.setFileOffset(dataOffset);
                        wasData.setFileSize(file.length());
                        wasData.setFileSpace(file.length());
                        wasDataList.add(wasData);
                        dataOffset += file.length();
                        
                        System.out.println("处理文件: " + fileName + " -> ID: 0x" + nameParts[1]);
                    } catch (NumberFormatException e) {
                        System.out.println("跳过无效文件名: " + fileName);
                    }
                }
            }
            
            // 执行打包
            System.out.println("\n开始打包WDF文件...");
            WdfTool.packWdfFile(sourceDir, targetFile, wasDataList, (progress, status) -> {
                System.out.println("进度 " + progress + ": " + status);
            });
            
            System.out.println("\n=== 打包完成 ===");
            System.out.println("输出文件: " + targetFile.getAbsolutePath());
            System.out.println("文件数量: " + wasDataList.size());
            System.out.println("文件大小: " + DataTool.formatFileSize(targetFile.length()));
            
        } catch (Exception e) {
            System.out.println("WDF打包测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WDF文件分析
     */
    public void testWdfAnalysis() {
        System.out.println("\n=== 测试WDF文件分析 ===");
        
        File wdfFile = new File(TEST_WDF);
        if (!wdfFile.exists()) {
            System.out.println("WDF文件不存在，请先执行打包测试");
            return;
        }
        
        try {
            WdfHead wdfHead = WdfTool.getWdfHead(wdfFile);
            
            if (wdfHead == null) {
                System.out.println("无法读取WDF文件头");
                return;
            }
            
            System.out.println("WDF文件信息:");
            System.out.println("  文件标识: 0x" + Long.toHexString(wdfHead.getFlag()).toUpperCase());
            System.out.println("  文件数量: " + wdfHead.getFileSum());
            System.out.println("  索引偏移: " + wdfHead.getOffset());
            System.out.println("  文件大小: " + DataTool.formatFileSize(wdfFile.length()));
            
            System.out.println("\n文件列表:");
            WasData[] wasDataList = wdfHead.getWasDataList();
            for (int i = 0; i < wasDataList.length; i++) {
                WasData wasData = wasDataList[i];
                System.out.printf("  文件 %d: ID=0x%08X, 偏移=%d, 大小=%s\n", 
                    i + 1, 
                    wasData.getId(), 
                    wasData.getFileOffset(), 
                    DataTool.formatFileSize(wasData.getFileSize()));
            }
            
            System.out.println("WDF文件分析完成");
            
        } catch (Exception e) {
            System.out.println("WDF文件分析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 收集符合WDF打包条件的文件
     */
    private void collectWdfFiles(File dir, List<File> files) {
        File[] fileList = dir.listFiles();
        if (fileList != null) {
            for (File file : fileList) {
                if (file.isDirectory()) {
                    collectWdfFiles(file, files);
                } else {
                    String name = file.getName().toLowerCase();
                    if (name.endsWith(".png") || name.endsWith(".was")) {
                        String baseName = name.substring(0, name.lastIndexOf('.'));
                        String[] parts = baseName.split("-");
                        // 检查格式: 2025-XXXXXXXX
                        if (parts.length >= 2 && parts[0].equals("2025") && parts[1].matches("[0-9A-Fa-f]{8}")) {
                            files.add(file);
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 测试数据工具功能
     */
    public void testDataToolFunctions() {
        System.out.println("\n=== 测试数据工具功能 ===");
        
        // 测试文件大小格式化
        long[] testSizes = {512L, 1536L, 2097152L, 1073741824L};
        System.out.println("文件大小格式化测试:");
        for (long size : testSizes) {
            System.out.printf("  %d 字节 -> %s\n", size, DataTool.formatFileSize(size));
        }
        
        // 测试十六进制转换
        System.out.println("\n十六进制转换测试:");
        String[] testNumbers = {"255", "4096", "65535", "16777215"};
        for (String num : testNumbers) {
            System.out.printf("  %s -> %s\n", num, DataTool.ten2six(num));
        }
        
        System.out.println("数据工具功能测试完成");
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        try {
            // 删除测试目录
            Path testDir = Paths.get(TEST_FOLDER);
            if (Files.exists(testDir)) {
                Files.walk(testDir)
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                            System.out.println("删除: " + path.getFileName());
                        } catch (IOException e) {
                            System.out.println("删除失败: " + path.getFileName());
                        }
                    });
            }
            
            // 删除WDF文件
            File wdfFile = new File(TEST_WDF);
            if (wdfFile.exists()) {
                if (wdfFile.delete()) {
                    System.out.println("删除: " + TEST_WDF);
                } else {
                    System.out.println("删除失败: " + TEST_WDF);
                }
            }
            
            System.out.println("测试文件清理完成");
            
        } catch (IOException e) {
            System.out.println("清理测试文件时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 运行所有测试
     */
    public void runAllTests() {
        createWdfTestFiles();
        testWdfPacking();
        testWdfAnalysis();
        testDataToolFunctions();
        
        System.out.println("\n=== 整合WDF功能测试完成 ===");
        System.out.println("如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        IntegratedWdfTest test = new IntegratedWdfTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllTests();
        }
    }
}
