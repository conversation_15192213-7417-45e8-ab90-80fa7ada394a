package com.tool.test;

import com.tool.npk.*;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * WDF和NPK增强功能测试
 * 
 * <AUTHOR>
 * @date 2024/12/16
 */
public class WdfNpkEnhancementTest {
    
    private static final String TEST_NPK = "test_enhanced.npk";
    private static final String EXTRACT_DIR = "extracted_files";
    
    /**
     * 创建增强的NXPK测试文件
     */
    public void createEnhancedNxpkFile() {
        System.out.println("=== 创建增强的NXPK测试文件 ===");
        
        try {
            File testFile = new File(TEST_NPK);
            
            try (RandomAccessFile raf = new RandomAccessFile(testFile, "rw")) {
                // 写入NXPK文件头
                raf.write("NXPK".getBytes()); // 魔数头
                writeInt32LE(raf, 5); // 条目数量
                writeInt32LE(raf, 0); // 未知变量
                writeInt32LE(raf, 0); // 加密模式
                writeInt32LE(raf, 0); // 哈希模式
                writeInt32LE(raf, 128); // 索引偏移
                writeInt32LE(raf, 0);   // 填充到64位
                
                // 填充到索引位置
                while (raf.getFilePointer() < 128) {
                    raf.write(0);
                }
                
                // 写入固定长度的文件索引 (每个条目32字节)
                for (int i = 0; i < 5; i++) {
                    long offset = 300 + i * 100; // 文件偏移
                    long compressedSize = 50 + i * 10; // 压缩大小
                    long originalSize = 80 + i * 15; // 原始大小
                    long crc32 = 0x12345678 + i; // CRC32
                    
                    // 写入32字节的条目信息
                    writeInt32LE(raf, (int)offset);
                    writeInt32LE(raf, (int)compressedSize);
                    writeInt32LE(raf, (int)originalSize);
                    writeInt32LE(raf, (int)crc32);
                    writeInt32LE(raf, i); // 额外信息1
                    writeInt32LE(raf, 0); // 额外信息2
                    writeInt32LE(raf, 0); // 额外信息3
                    writeInt32LE(raf, 0); // 额外信息4
                }
                
                // 写入模拟的文件数据
                for (int i = 0; i < 5; i++) {
                    raf.seek(300 + i * 100);
                    String content = "Test file content " + i + " - This is sample data for testing NPK extraction.";
                    byte[] data = content.getBytes("UTF-8");
                    raf.write(data);
                }
            }
            
            System.out.println("创建增强NXPK文件: " + TEST_NPK);
            System.out.println("文件大小: " + testFile.length() + " 字节");
            
        } catch (IOException e) {
            System.out.println("创建增强NXPK文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试增强的NPK解析
     */
    public void testEnhancedNpkParsing() {
        System.out.println("\n=== 测试增强的NPK解析 ===");
        
        File npkFile = new File(TEST_NPK);
        if (!npkFile.exists()) {
            System.out.println("NPK文件不存在，请先创建测试文件");
            return;
        }
        
        try {
            System.out.println("开始解析增强NXPK文件...");
            
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                System.out.println("进度 " + progress + "%: " + status);
            });
            
            if (parsedFile != null) {
                System.out.println("NPK解析成功！");
                System.out.println("文件条目数: " + parsedFile.getEntries().size());
                
                // 显示条目信息
                for (int i = 0; i < parsedFile.getEntries().size(); i++) {
                    NpkEntry entry = parsedFile.getEntries().get(i);
                    System.out.println("条目 " + (i + 1) + ":");
                    System.out.println("  文件名: " + entry.getFileName());
                    System.out.println("  偏移: 0x" + Long.toHexString(entry.getOffset()));
                    System.out.println("  压缩大小: " + entry.getCompressedSize());
                    System.out.println("  原始大小: " + entry.getOriginalSize());
                    System.out.println("  CRC32: 0x" + Long.toHexString(entry.getCrc32()));
                }
            } else {
                System.out.println("NPK解析失败");
            }
            
        } catch (Exception e) {
            System.out.println("NPK解析异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试NPK文件提取
     */
    public void testNpkExtraction() {
        System.out.println("\n=== 测试NPK文件提取 ===");
        
        File npkFile = new File(TEST_NPK);
        if (!npkFile.exists()) {
            System.out.println("NPK文件不存在，请先创建测试文件");
            return;
        }
        
        try {
            // 创建提取目录
            File extractDir = new File(EXTRACT_DIR);
            if (extractDir.exists()) {
                // 清理旧文件
                Files.walk(extractDir.toPath())
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
            }
            extractDir.mkdirs();
            
            System.out.println("开始提取NPK文件...");
            
            // 解析NPK文件
            NpkFile parsedFile = NpkTool.parseNpkFile(npkFile, (progress, status) -> {
                System.out.println("解析进度 " + progress + "%: " + status);
            });
            
            if (parsedFile == null || parsedFile.getEntries().isEmpty()) {
                System.out.println("NPK文件解析失败或没有文件条目");
                return;
            }
            
            // 提取文件
            int extractedCount = 0;
            int errorCount = 0;
            
            for (int i = 0; i < parsedFile.getEntries().size(); i++) {
                NpkEntry entry = parsedFile.getEntries().get(i);
                
                try {
                    System.out.println("提取文件 " + (i + 1) + ": " + entry.getFileName());
                    
                    byte[] fileData = NpkTool.extractFile(parsedFile, entry, (progress, status) -> {
                        System.out.println("  " + status);
                    });
                    
                    if (fileData != null && fileData.length > 0) {
                        File outputFile = new File(extractDir, entry.getFileName());
                        try (java.io.FileOutputStream fos = new java.io.FileOutputStream(outputFile)) {
                            fos.write(fileData);
                        }
                        
                        System.out.println("  成功提取: " + fileData.length + " 字节");
                        extractedCount++;
                    } else {
                        System.out.println("  提取失败: 数据为空");
                        errorCount++;
                    }
                    
                } catch (Exception e) {
                    System.out.println("  提取异常: " + e.getMessage());
                    errorCount++;
                }
            }
            
            System.out.println("\n=== 提取结果 ===");
            System.out.println("总文件数: " + parsedFile.getEntries().size());
            System.out.println("成功提取: " + extractedCount);
            System.out.println("提取失败: " + errorCount);
            System.out.println("输出目录: " + extractDir.getAbsolutePath());
            
        } catch (Exception e) {
            System.out.println("NPK提取测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试WDF文件名可选功能
     */
    public void testWdfCustomFileName() {
        System.out.println("\n=== 测试WDF文件名可选功能 ===");
        
        String fixedOutputDir = "G:\\JXy2o\\GameClient3.0\\res";
        System.out.println("固定输出目录: " + fixedOutputDir);
        
        // 测试不同的文件名
        String[] testNames = {"custom_item", "test_wdf", "my_resources.wdf", "game_data"};
        
        for (String testName : testNames) {
            System.out.println("\n测试文件名: " + testName);
            
            // 处理文件名，确保以.wdf结尾
            String fileName = testName.trim();
            if (!fileName.toLowerCase().endsWith(".wdf")) {
                fileName += ".wdf";
            }
            
            File outputFile = new File(fixedOutputDir, fileName);
            System.out.println("完整路径: " + outputFile.getAbsolutePath());
            
            // 测试文件创建
            try {
                if (outputFile.getParentFile().exists() || outputFile.getParentFile().mkdirs()) {
                    if (outputFile.createNewFile()) {
                        System.out.println("✓ 文件创建成功");
                        outputFile.delete(); // 删除测试文件
                        System.out.println("✓ 测试文件已删除");
                    } else {
                        System.out.println("✗ 文件创建失败");
                    }
                } else {
                    System.out.println("✗ 无法创建输出目录");
                }
            } catch (IOException e) {
                System.out.println("✗ 文件操作异常: " + e.getMessage());
            }
        }
    }
    
    /**
     * 写入32位小端序整数
     */
    private void writeInt32LE(RandomAccessFile raf, int value) throws IOException {
        raf.write(value & 0xFF);
        raf.write((value >> 8) & 0xFF);
        raf.write((value >> 16) & 0xFF);
        raf.write((value >> 24) & 0xFF);
    }
    
    /**
     * 清理测试文件
     */
    public void cleanupTestFiles() {
        System.out.println("\n=== 清理测试文件 ===");
        
        // 删除NPK文件
        File npkFile = new File(TEST_NPK);
        if (npkFile.exists()) {
            if (npkFile.delete()) {
                System.out.println("删除: " + TEST_NPK);
            } else {
                System.out.println("删除失败: " + TEST_NPK);
            }
        }
        
        // 删除提取目录
        File extractDir = new File(EXTRACT_DIR);
        if (extractDir.exists()) {
            try {
                Files.walk(extractDir.toPath())
                    .sorted((a, b) -> b.compareTo(a))
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            // 忽略删除错误
                        }
                    });
                System.out.println("删除目录: " + EXTRACT_DIR);
            } catch (IOException e) {
                System.out.println("删除目录失败: " + e.getMessage());
            }
        }
        
        System.out.println("测试文件清理完成");
    }
    
    /**
     * 运行所有增强功能测试
     */
    public void runAllEnhancementTests() {
        System.out.println("=== WDF和NPK增强功能测试 ===");
        System.out.println("测试内容:");
        System.out.println("1. WDF文件名可选功能");
        System.out.println("2. NPK文件NXPK格式解析");
        System.out.println("3. NPK文件提取功能");
        System.out.println("4. 固定长度索引解析");
        
        testWdfCustomFileName();
        createEnhancedNxpkFile();
        testEnhancedNpkParsing();
        testNpkExtraction();
        
        System.out.println("\n=== 增强功能测试完成 ===");
        System.out.println("主要增强内容:");
        System.out.println("✓ WDF输出目录固定，文件名可自定义");
        System.out.println("✓ NPK文件NXPK格式正确识别");
        System.out.println("✓ NPK索引偏移负数问题修复");
        System.out.println("✓ NPK文件提取功能完整实现");
        System.out.println("✓ 智能固定长度索引解析");
        System.out.println("\n如需清理测试文件，请调用 cleanupTestFiles() 方法");
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        WdfNpkEnhancementTest test = new WdfNpkEnhancementTest();
        
        if (args.length > 0 && "cleanup".equals(args[0])) {
            test.cleanupTestFiles();
        } else {
            test.runAllEnhancementTests();
        }
    }
}
