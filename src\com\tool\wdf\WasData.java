
package com.tool.wdf;

public class WasData {
    private long id;
    private long fileSize;
    private long fileOffset;
    private long fileSpace;
    private int frames;
    private int width;
    private int height;
    private int direction;
    private WasHead wasHead;

    public WasData() {
    }

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getFileSize() {
        return this.fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public long getFileOffset() {
        return this.fileOffset;
    }

    public void setFileOffset(long fileOffset) {
        this.fileOffset = fileOffset;
    }

    public long getFileSpace() {
        return this.fileSpace;
    }

    public void setFileSpace(long fileSpace) {
        this.fileSpace = fileSpace;
    }

    public int getFrames() {
        return this.frames;
    }

    public void setFrames(int frames) {
        this.frames = frames;
    }

    public int getWidth() {
        return this.width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return this.height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public int getDirection() {
        return this.direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public WasHead getWasHead() {
        return this.wasHead;
    }

    public void setWasHead(WasHead wasHead) {
        this.wasHead = wasHead;
    }
}
